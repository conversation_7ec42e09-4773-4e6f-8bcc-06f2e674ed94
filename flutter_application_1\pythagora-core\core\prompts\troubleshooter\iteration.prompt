You are working on an app called "{{ state.branch.project.name }}" and you need to write code for the entire application.

{% include "partials/project_details.prompt" %}

{% if state.tasks and state.current_task %}
Development process of this app was split into smaller tasks. Here is the list of all tasks:
```
{% for task in state.tasks %}
{{ loop.index }}. {{ task.description }}
{% endfor %}
```

You are currently working on, and have to focus only on, this task:
```
{{ state.current_task.description }}
```

{% endif %}
A part of the app is already finished.
{% include "partials/files_list.prompt" %}

{% include "partials/user_feedback.prompt" %}

{% if test_instructions %}
User was testing the current implementation of the app when they requested some changes to the app. These are the testing instructions:
```
{% for step in test_instructions %}
Step #{{ loop.index }}
Action: {{ step.action }}
Expected result: {{ step.result }}
{% endfor %}
```
{% endif %}

{% if next_solution_to_try is not none %}
Focus on solving this issue in the following way:
```
{{ next_solution_to_try }}
```
{% endif %}
{% include "partials/doc_snippets.prompt" %}
Now, tell me how can we implement the changes that the user requested. Think step by step and explain each change you want to make and write code snippets that you want to change.

**IMPORTANT**
Think about all information provided. Your job is to look at big picture by analysing all files to find where the issue is.
When there are multiple things that have to be done in one file write everything as one step and don't split it in multiple steps.
You can count that the environment is set up previously and packages listed in files are installed so tell me only commands needed for installation of new dependencies, if there are any.

**IMPORTANT**
If report mentions *multiple* issues, treat it as if you got several separate reports: think through each, and provide solutions for each.

{% include "partials/execution_order.prompt" %}
{% include "partials/file_size_limit.prompt" %}
{% include "partials/file_naming.prompt" %}
{% include "partials/relative_paths.prompt" %}
