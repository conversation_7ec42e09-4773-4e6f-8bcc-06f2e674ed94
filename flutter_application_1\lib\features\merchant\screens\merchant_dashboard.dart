import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../providers/auth_provider.dart';
import '../../../providers/product_provider.dart';
import '../../../providers/transaction_provider.dart';
import 'modern_inventory_dashboard.dart';

class MerchantDashboard extends ConsumerStatefulWidget {
  const MerchantDashboard({super.key});

  @override
  ConsumerState<MerchantDashboard> createState() => _MerchantDashboardState();
}

class _MerchantDashboardState extends ConsumerState<MerchantDashboard> {
  @override
  Widget build(BuildContext context) {
    // Show only the Modern Inventory Dashboard without bottom navigation
    return const Scaffold(
      body: ModernInventoryDashboard(),
    );
  }
}

class MerchantHomeScreen extends ConsumerWidget {
  const MerchantHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider).value;
    final productsAsync = ref.watch(currentUserProductsProvider);
    final lowStockAsync = ref.watch(lowStockProductsProvider);
    final transactionsAsync = ref.watch(currentUserTransactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Merchant Dashboard'),
        actions: [
          IconButton(
            onPressed: () => context.go('/login'),
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      child: Text(
                        user?.firstName[0].toUpperCase() ?? 'M',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back, ${user?.firstName ?? 'Merchant'}!',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Manage your inventory and track sales',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Quick stats
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Products',
                    productsAsync.when(
                      data: (products) => products.length.toString(),
                      loading: () => '...',
                      error: (_, __) => '0',
                    ),
                    Icons.inventory,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Low Stock',
                    lowStockAsync.when(
                      data: (products) => products.length.toString(),
                      loading: () => '...',
                      error: (_, __) => '0',
                    ),
                    Icons.warning,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Today\'s Sales',
                    transactionsAsync.when(
                      data: (transactions) {
                        final today = DateTime.now();
                        final todaySales = transactions
                            .where((t) =>
                                t.createdAt.day == today.day &&
                                t.createdAt.month == today.month &&
                                t.createdAt.year == today.year)
                            .length;
                        return todaySales.toString();
                      },
                      loading: () => '...',
                      error: (_, __) => '0',
                    ),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Revenue',
                    transactionsAsync.when(
                      data: (transactions) {
                        final today = DateTime.now();
                        final todayRevenue = transactions
                            .where((t) =>
                                t.createdAt.day == today.day &&
                                t.createdAt.month == today.month &&
                                t.createdAt.year == today.year)
                            .fold(0.0, (sum, t) => sum + t.total);
                        return '₦${todayRevenue.toStringAsFixed(0)}';
                      },
                      loading: () => '...',
                      error: (_, __) => '₦0',
                    ),
                    Icons.attach_money,
                    Colors.purple,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Quick actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1.5,
              children: [
                _buildActionCard(
                  context,
                  'Add Product',
                  Icons.add_box,
                  Colors.blue,
                  () => _showAddProductDialog(context, ref),
                ),
                _buildActionCard(
                  context,
                  'Quick Add',
                  Icons.qr_code_scanner,
                  Colors.green,
                  () => _showQuickAddDialog(context, ref),
                ),
                _buildActionCard(
                  context,
                  'View Analytics',
                  Icons.analytics,
                  Colors.purple,
                  () {
                    // Navigate to analytics - this would need to be handled by parent
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Analytics feature coming soon!')),
                    );
                  },
                ),
                _buildActionCard(
                  context,
                  'Manage Staff',
                  Icons.people,
                  Colors.orange,
                  () {
                    // Navigate to staff management - this would need to be handled by parent
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content:
                              Text('Staff management feature coming soon!')),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Low stock alerts
            lowStockAsync.when(
              data: (lowStockProducts) {
                if (lowStockProducts.isEmpty) return const SizedBox.shrink();

                return Card(
                  color: Colors.orange[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.warning, color: Colors.orange[700]),
                            const SizedBox(width: 8),
                            Text(
                              'Low Stock Alert',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[700],
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${lowStockProducts.length} product${lowStockProducts.length != 1 ? 's' : ''} running low on stock',
                          style: TextStyle(color: Colors.orange[700]),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text(
                                      'Navigate to inventory from main dashboard')),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('View Inventory'),
                        ),
                      ],
                    ),
                  ),
                );
              },
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const Spacer(),
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddProductDialog(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Product feature - Demo Mode'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showQuickAddDialog(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quick Add feature - Demo Mode'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
