import 'package:cloud_firestore/cloud_firestore.dart';

enum ProfileVisibility { public, private, friendsOnly }
enum TwoFactorMethod { none, sms, email, authenticator }

class AccountSettingsModel {
  final String userId;
  final ProfileVisibility profileVisibility;
  final bool showEmail;
  final bool showPhone;
  final bool showLastSeen;
  final bool allowDirectMessages;
  final bool allowFriendRequests;
  final bool shareDataForAnalytics;
  final bool shareDataForMarketing;
  final TwoFactorMethod twoFactorMethod;
  final bool twoFactorEnabled;
  final String? backupEmail;
  final String? recoveryPhone;
  final List<String> blockedUsers;
  final List<String> trustedDevices;
  final DateTime? lastPasswordChange;
  final DateTime? lastSecurityReview;
  final bool accountDeactivationRequested;
  final DateTime? deactivationRequestDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  AccountSettingsModel({
    required this.userId,
    this.profileVisibility = ProfileVisibility.public,
    this.showEmail = false,
    this.showPhone = false,
    this.showLastSeen = true,
    this.allowDirectMessages = true,
    this.allowFriendRequests = true,
    this.shareDataForAnalytics = false,
    this.shareDataForMarketing = false,
    this.twoFactorMethod = TwoFactorMethod.none,
    this.twoFactorEnabled = false,
    this.backupEmail,
    this.recoveryPhone,
    this.blockedUsers = const [],
    this.trustedDevices = const [],
    this.lastPasswordChange,
    this.lastSecurityReview,
    this.accountDeactivationRequested = false,
    this.deactivationRequestDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AccountSettingsModel.defaultSettings(String userId) {
    final now = DateTime.now();
    return AccountSettingsModel(
      userId: userId,
      createdAt: now,
      updatedAt: now,
    );
  }

  factory AccountSettingsModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AccountSettingsModel(
      userId: doc.id,
      profileVisibility: ProfileVisibility.values.firstWhere(
        (e) => e.name == data['profileVisibility'],
        orElse: () => ProfileVisibility.public,
      ),
      showEmail: data['showEmail'] ?? false,
      showPhone: data['showPhone'] ?? false,
      showLastSeen: data['showLastSeen'] ?? true,
      allowDirectMessages: data['allowDirectMessages'] ?? true,
      allowFriendRequests: data['allowFriendRequests'] ?? true,
      shareDataForAnalytics: data['shareDataForAnalytics'] ?? false,
      shareDataForMarketing: data['shareDataForMarketing'] ?? false,
      twoFactorMethod: TwoFactorMethod.values.firstWhere(
        (e) => e.name == data['twoFactorMethod'],
        orElse: () => TwoFactorMethod.none,
      ),
      twoFactorEnabled: data['twoFactorEnabled'] ?? false,
      backupEmail: data['backupEmail'],
      recoveryPhone: data['recoveryPhone'],
      blockedUsers: data['blockedUsers'] != null 
          ? List<String>.from(data['blockedUsers']) 
          : [],
      trustedDevices: data['trustedDevices'] != null 
          ? List<String>.from(data['trustedDevices']) 
          : [],
      lastPasswordChange: data['lastPasswordChange'] != null
          ? (data['lastPasswordChange'] as Timestamp).toDate()
          : null,
      lastSecurityReview: data['lastSecurityReview'] != null
          ? (data['lastSecurityReview'] as Timestamp).toDate()
          : null,
      accountDeactivationRequested: data['accountDeactivationRequested'] ?? false,
      deactivationRequestDate: data['deactivationRequestDate'] != null
          ? (data['deactivationRequestDate'] as Timestamp).toDate()
          : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'profileVisibility': profileVisibility.name,
      'showEmail': showEmail,
      'showPhone': showPhone,
      'showLastSeen': showLastSeen,
      'allowDirectMessages': allowDirectMessages,
      'allowFriendRequests': allowFriendRequests,
      'shareDataForAnalytics': shareDataForAnalytics,
      'shareDataForMarketing': shareDataForMarketing,
      'twoFactorMethod': twoFactorMethod.name,
      'twoFactorEnabled': twoFactorEnabled,
      'backupEmail': backupEmail,
      'recoveryPhone': recoveryPhone,
      'blockedUsers': blockedUsers,
      'trustedDevices': trustedDevices,
      'lastPasswordChange': lastPasswordChange != null 
          ? Timestamp.fromDate(lastPasswordChange!) 
          : null,
      'lastSecurityReview': lastSecurityReview != null 
          ? Timestamp.fromDate(lastSecurityReview!) 
          : null,
      'accountDeactivationRequested': accountDeactivationRequested,
      'deactivationRequestDate': deactivationRequestDate != null 
          ? Timestamp.fromDate(deactivationRequestDate!) 
          : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  AccountSettingsModel copyWith({
    ProfileVisibility? profileVisibility,
    bool? showEmail,
    bool? showPhone,
    bool? showLastSeen,
    bool? allowDirectMessages,
    bool? allowFriendRequests,
    bool? shareDataForAnalytics,
    bool? shareDataForMarketing,
    TwoFactorMethod? twoFactorMethod,
    bool? twoFactorEnabled,
    String? backupEmail,
    String? recoveryPhone,
    List<String>? blockedUsers,
    List<String>? trustedDevices,
    DateTime? lastPasswordChange,
    DateTime? lastSecurityReview,
    bool? accountDeactivationRequested,
    DateTime? deactivationRequestDate,
    DateTime? updatedAt,
  }) {
    return AccountSettingsModel(
      userId: userId,
      profileVisibility: profileVisibility ?? this.profileVisibility,
      showEmail: showEmail ?? this.showEmail,
      showPhone: showPhone ?? this.showPhone,
      showLastSeen: showLastSeen ?? this.showLastSeen,
      allowDirectMessages: allowDirectMessages ?? this.allowDirectMessages,
      allowFriendRequests: allowFriendRequests ?? this.allowFriendRequests,
      shareDataForAnalytics: shareDataForAnalytics ?? this.shareDataForAnalytics,
      shareDataForMarketing: shareDataForMarketing ?? this.shareDataForMarketing,
      twoFactorMethod: twoFactorMethod ?? this.twoFactorMethod,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      backupEmail: backupEmail ?? this.backupEmail,
      recoveryPhone: recoveryPhone ?? this.recoveryPhone,
      blockedUsers: blockedUsers ?? this.blockedUsers,
      trustedDevices: trustedDevices ?? this.trustedDevices,
      lastPasswordChange: lastPasswordChange ?? this.lastPasswordChange,
      lastSecurityReview: lastSecurityReview ?? this.lastSecurityReview,
      accountDeactivationRequested: accountDeactivationRequested ?? this.accountDeactivationRequested,
      deactivationRequestDate: deactivationRequestDate ?? this.deactivationRequestDate,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  bool get needsSecurityReview {
    if (lastSecurityReview == null) return true;
    final daysSinceReview = DateTime.now().difference(lastSecurityReview!).inDays;
    return daysSinceReview > 90; // Suggest review every 3 months
  }

  bool get needsPasswordChange {
    if (lastPasswordChange == null) return true;
    final daysSinceChange = DateTime.now().difference(lastPasswordChange!).inDays;
    return daysSinceChange > 180; // Suggest change every 6 months
  }
}

extension ProfileVisibilityExtension on ProfileVisibility {
  String get displayName {
    switch (this) {
      case ProfileVisibility.public:
        return 'Public';
      case ProfileVisibility.private:
        return 'Private';
      case ProfileVisibility.friendsOnly:
        return 'Friends Only';
    }
  }

  String get description {
    switch (this) {
      case ProfileVisibility.public:
        return 'Anyone can see your profile';
      case ProfileVisibility.private:
        return 'Only you can see your profile';
      case ProfileVisibility.friendsOnly:
        return 'Only your friends can see your profile';
    }
  }
}

extension TwoFactorMethodExtension on TwoFactorMethod {
  String get displayName {
    switch (this) {
      case TwoFactorMethod.none:
        return 'None';
      case TwoFactorMethod.sms:
        return 'SMS';
      case TwoFactorMethod.email:
        return 'Email';
      case TwoFactorMethod.authenticator:
        return 'Authenticator App';
    }
  }
}
