name: Run unit tests

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main", "rewrite" ]

jobs:
  build:

    runs-on: ${{ matrix.os }}
    timeout-minutes: 10
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.9", "3.12"]
        os: [ubuntu-latest, macos-latest, windows-latest]

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install poetry
        poetry install --with=dev
    - name: Lint with ruff
      run: poetry run ruff check --output-format github
    - name: Check code style with ruff
      run: poetry run ruff format --check --diff
    - name: Test with pytest
      run: poetry run pytest
