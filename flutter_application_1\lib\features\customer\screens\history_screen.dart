import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../providers/transaction_provider.dart';
import '../../../providers/auth_provider.dart';
import '../widgets/transaction_card.dart';

class HistoryScreen extends ConsumerStatefulWidget {
  const HistoryScreen({super.key});

  @override
  ConsumerState<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends ConsumerState<HistoryScreen> {
  String _selectedFilter = 'all';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  Widget build(BuildContext context) {
    final userId = ref.watch(currentUserIdProvider);

    if (userId == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please log in to view your purchase history'),
        ),
      );
    }

    final transactionsAsync = ref.watch(customerTransactionsProvider(userId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Purchase History'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter chips
          if (_selectedFilter != 'all' || _startDate != null)
            Container(
              padding: const EdgeInsets.all(16),
              child: Wrap(
                spacing: 8,
                children: [
                  if (_selectedFilter != 'all')
                    FilterChip(
                      label: Text(_getFilterLabel(_selectedFilter)),
                      onSelected: (selected) {},
                      onDeleted: () => setState(() => _selectedFilter = 'all'),
                      deleteIcon: const Icon(Icons.close, size: 16),
                    ),
                  if (_startDate != null)
                    FilterChip(
                      label: Text(
                        'From ${DateFormat('MMM dd').format(_startDate!)}',
                      ),
                      onSelected: (selected) {},
                      onDeleted: () => setState(() {
                        _startDate = null;
                        _endDate = null;
                      }),
                      deleteIcon: const Icon(Icons.close, size: 16),
                    ),
                ],
              ),
            ),

          // Transactions list
          Expanded(
            child: transactionsAsync.when(
              data: (transactions) {
                final filteredTransactions = _filterTransactions(transactions);

                if (filteredTransactions.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(customerTransactionsProvider(userId));
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: filteredTransactions.length,
                    itemBuilder: (context, index) {
                      final transaction = filteredTransactions[index];
                      return TransactionCard(
                        transaction: transaction,
                        onTap: () => _showTransactionDetails(transaction),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading transactions',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.invalidate(customerTransactionsProvider(userId));
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No transactions found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your purchase history will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<dynamic> _filterTransactions(List<dynamic> transactions) {
    var filtered = transactions;

    // Filter by status
    if (_selectedFilter != 'all') {
      filtered =
          filtered.where((t) => t.status.value == _selectedFilter).toList();
    }

    // Filter by date range
    if (_startDate != null) {
      filtered = filtered.where((t) {
        final transactionDate = t.createdAt;
        final endDate = _endDate ?? DateTime.now();
        return transactionDate.isAfter(_startDate!) &&
            transactionDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    return filtered;
  }

  String _getFilterLabel(String filter) {
    switch (filter) {
      case 'completed':
        return 'Completed';
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      default:
        return 'All';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Transactions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Status:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                'all',
                'completed',
                'verified',
                'pending',
                'failed',
              ].map((status) {
                return ChoiceChip(
                  label: Text(_getFilterLabel(status)),
                  selected: _selectedFilter == status,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() => _selectedFilter = status);
                    }
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            const Text('Date Range:'),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _selectStartDate,
                    child: Text(
                      _startDate != null
                          ? DateFormat('MMM dd, yyyy').format(_startDate!)
                          : 'Start Date',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _startDate != null ? _selectEndDate : null,
                    child: Text(
                      _endDate != null
                          ? DateFormat('MMM dd, yyyy').format(_endDate!)
                          : 'End Date',
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedFilter = 'all';
                _startDate = null;
                _endDate = null;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _startDate = date;
        if (_endDate != null && _endDate!.isBefore(date)) {
          _endDate = null;
        }
      });
    }
  }

  void _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate!,
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _endDate = date);
    }
  }

  void _showTransactionDetails(dynamic transaction) {
    showDialog(
      context: context,
      builder: (context) => TransactionDetailsDialog(transaction: transaction),
    );
  }
}

class TransactionDetailsDialog extends StatelessWidget {
  final dynamic transaction;

  const TransactionDetailsDialog({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Transaction #${transaction.id.substring(0, 8)}'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDetailRow('Date',
                DateFormat('MMM dd, yyyy HH:mm').format(transaction.createdAt)),
            _buildDetailRow('Status', transaction.status.displayName),
            _buildDetailRow('Items', '${transaction.totalItems}'),
            _buildDetailRow(
                'Subtotal', '₦${transaction.subtotal.toStringAsFixed(2)}'),
            _buildDetailRow('Tax', '₦${transaction.tax.toStringAsFixed(2)}'),
            _buildDetailRow(
                'Total', '₦${transaction.total.toStringAsFixed(2)}'),
            if (transaction.paystackReference != null)
              _buildDetailRow('Reference', transaction.paystackReference!),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
