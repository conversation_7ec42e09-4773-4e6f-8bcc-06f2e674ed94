import 'package:flutter/material.dart';

/// Modern customer mobile theme inspired by successful fintech apps
/// Features vibrant colors, smooth animations, and Material Design 3 principles
class CustomerTheme {
  // Primary Color System - Industry-Leading Palette (Inspired by Revolut/PayPal)
  static const Color primaryTeal = Color(0xFF00D4AA);
  static const Color primaryTealDark = Color(0xFF00A085);
  static const Color primaryTealLight = Color(0xFF4DFFDB);
  static const Color primaryTealSurface = Color(0xFFF0FFFE);
  static const Color primaryTealContainer = Color(0xFFB3F5E8);

  // Secondary Color System - Professional Accents
  static const Color accentBlue = Color(0xFF0066FF);
  static const Color accentBlueDark = Color(0xFF0052CC);
  static const Color accentBlueLight = Color(0xFF4D94FF);
  static const Color accentBlueSurface = Color(0xFFF0F6FF);

  static const Color accentOrange = Color(0xFFFF8A00);
  static const Color accentOrangeDark = Color(0xFFE67300);
  static const Color accentOrangeLight = Color(0xFFFFB84D);
  static const Color accentOrangeSurface = Color(0xFFFFF8F0);

  static const Color accentPurple = Color(0xFF8B5CF6);
  static const Color accentPurpleDark = Color(0xFF7C3AED);
  static const Color accentPurpleLight = Color(0xFFA78BFA);
  static const Color accentPurpleSurface = Color(0xFFF8F5FF);

  // Semantic Color System - Enhanced for Trust & Clarity
  static const Color successGreen = Color(0xFF10B981);
  static const Color successGreenDark = Color(0xFF059669);
  static const Color successGreenLight = Color(0xFF34D399);
  static const Color successGreenSurface = Color(0xFFF0FDF4);

  static const Color warningAmber = Color(0xFFF59E0B);
  static const Color warningAmberDark = Color(0xFFD97706);
  static const Color warningAmberLight = Color(0xFFFBBF24);
  static const Color warningAmberSurface = Color(0xFFFFFBEB);

  static const Color errorRed = Color(0xFFEF4444);
  static const Color errorRedDark = Color(0xFFDC2626);
  static const Color errorRedLight = Color(0xFFF87171);
  static const Color errorRedSurface = Color(0xFFFEF2F2);

  static const Color infoBlue = Color(0xFF3B82F6);
  static const Color infoBlueDark = Color(0xFF2563EB);
  static const Color infoBlueLight = Color(0xFF60A5FA);
  static const Color infoBlueSurface = Color(0xFFEFF6FF);

  // Neutral Color System - Professional Hierarchy
  static const Color backgroundPrimary = Color(0xFFFFFFFF);
  static const Color backgroundSecondary = Color(0xFFFAFBFC);
  static const Color backgroundTertiary = Color(0xFFF5F6F7);
  static const Color backgroundQuaternary = Color(0xFFEBECED);

  static const Color surfaceElevated = Color(0xFFFFFFFF);
  static const Color surfaceContainer = Color(0xFFF8F9FA);
  static const Color surfaceContainerHigh = Color(0xFFF1F3F4);
  static const Color surfaceContainerHighest = Color(0xFFE8EAED);

  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF5F6368);
  static const Color textTertiary = Color(0xFF80868B);
  static const Color textQuaternary = Color(0xFFBDC1C6);
  static const Color textInverse = Color(0xFFFFFFFF);

  static const Color borderPrimary = Color(0xFFDADCE0);
  static const Color borderSecondary = Color(0xFFE8EAED);
  static const Color borderTertiary = Color(0xFFF1F3F4);
  static const Color borderFocus = primaryTeal;

  // Legacy support
  static const Color backgroundLight = backgroundSecondary;
  static const Color surfaceWhite = surfaceElevated;
  static const Color borderLight = borderPrimary;

  // Professional Gradient System - Industry-Leading Designs
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryTeal, primaryTealDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient primarySubtleGradient = LinearGradient(
    colors: [primaryTealLight, primaryTeal],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient accentBlueGradient = LinearGradient(
    colors: [accentBlue, accentBlueDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentOrangeGradient = LinearGradient(
    colors: [accentOrange, accentOrangeDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [successGreen, successGreenDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [warningAmber, warningAmberDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [errorRed, errorRedDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Subtle Surface Gradients for Cards
  static const LinearGradient surfaceGradient = LinearGradient(
    colors: [surfaceElevated, surfaceContainer],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient shimmerGradient = LinearGradient(
    colors: [
      Color(0xFFEBEBF4),
      Color(0xFFF4F4F4),
      Color(0xFFEBEBF4),
    ],
    stops: [0.1, 0.3, 0.4],
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
  );

  // Professional Shadow System - Multi-Level Elevation
  static List<BoxShadow> get shadowNone => [];

  static List<BoxShadow> get shadowXS => [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ];

  static List<BoxShadow> get shadowSM => [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 3,
          offset: const Offset(0, 1),
        ),
        BoxShadow(
          color: Colors.black.withOpacity(0.06),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ];

  static List<BoxShadow> get shadowMD => [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 6,
          offset: const Offset(0, 4),
        ),
        BoxShadow(
          color: Colors.black.withOpacity(0.06),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ];

  static List<BoxShadow> get shadowLG => [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 15,
          offset: const Offset(0, 10),
        ),
        BoxShadow(
          color: Colors.black.withOpacity(0.04),
          blurRadius: 6,
          offset: const Offset(0, 4),
        ),
      ];

  static List<BoxShadow> get shadowXL => [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 25,
          offset: const Offset(0, 20),
        ),
        BoxShadow(
          color: Colors.black.withOpacity(0.04),
          blurRadius: 10,
          offset: const Offset(0, 8),
        ),
      ];

  static List<BoxShadow> get shadow2XL => [
        BoxShadow(
          color: Colors.black.withOpacity(0.25),
          blurRadius: 50,
          offset: const Offset(0, 25),
        ),
      ];

  // Legacy support
  static List<BoxShadow> get cardShadow => shadowMD;
  static List<BoxShadow> get elevatedShadow => shadowLG;

  // Animation Durations - Material Design 3 Standard
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
  static const Duration extraSlowAnimation = Duration(milliseconds: 700);

  // Animation Curves
  static const Curve standardCurve = Curves.easeInOut;
  static const Curve emphasizedCurve = Curves.easeOutCubic;
  static const Curve deceleratedCurve = Curves.easeOut;
  static const Curve acceleratedCurve = Curves.easeIn;

  // Border Radius
  static const double smallRadius = 8.0;
  static const double mediumRadius = 12.0;
  static const double largeRadius = 16.0;
  static const double extraLargeRadius = 24.0;

  // 8dp Grid System - Material Design 3 Standard
  static const double spacing2 = 2.0;
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;

  // Legacy spacing (for backward compatibility)
  static const double spacingXS = spacing4;
  static const double spacingS = spacing8;
  static const double spacingM = spacing16;
  static const double spacingL = spacing24;
  static const double spacingXL = spacing32;
  static const double spacingXXL = spacing48;

  // Touch Target Sizes (Accessibility)
  static const double minTouchTarget = 44.0; // iOS minimum
  static const double recommendedTouchTarget = 48.0; // Material Design
  static const double largeTouchTarget = 56.0; // For primary actions

  // Typography Scale
  static const TextStyle headingLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
    height: 1.2,
  );

  static const TextStyle headingMedium = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
    height: 1.3,
  );

  static const TextStyle headingSmall = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    letterSpacing: 0,
    height: 1.4,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.15,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.25,
    height: 1.4,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.4,
    height: 1.3,
  );

  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.3,
  );

  // Theme Data
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: primaryTeal,
        onPrimary: Colors.white,
        secondary: accentOrange,
        onSecondary: Colors.white,
        tertiary: accentBlue,
        surface: surfaceWhite,
        onSurface: textPrimary,
        surfaceContainerLowest: backgroundLight,
        surfaceContainer: surfaceWhite,
        error: errorRed,
        onError: Colors.white,
        outline: borderLight,
        outlineVariant: Color(0xFFE5E7EB),
      ),

      // App Bar Theme
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: textPrimary,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
      ),

      // Card Theme
      cardTheme: CardTheme(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(mediumRadius),
        ),
        margin: const EdgeInsets.all(spacingS),
        shadowColor: Colors.black.withOpacity(0.1),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingL,
            vertical: spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(mediumRadius),
          ),
          textStyle: labelLarge,
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(mediumRadius),
          borderSide: const BorderSide(color: borderLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(mediumRadius),
          borderSide: const BorderSide(color: borderLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(mediumRadius),
          borderSide: const BorderSide(color: primaryTeal, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingM,
        ),
        filled: true,
        fillColor: surfaceWhite,
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: primaryTeal,
        unselectedItemColor: textTertiary,
        elevation: 8,
        backgroundColor: surfaceWhite,
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryTeal,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
    );
  }

  // Helper Methods
  static Color getStockStatusColor(int stock, int threshold) {
    if (stock <= 0) return errorRed;
    if (stock <= threshold) return warningAmber;
    return successGreen;
  }

  static BoxDecoration getGradientDecoration({
    required Gradient gradient,
    double borderRadius = mediumRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      gradient: gradient,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: boxShadow ?? cardShadow,
    );
  }

  static BoxDecoration getCardDecoration({
    Color? color,
    double borderRadius = mediumRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return BoxDecoration(
      color: color ?? surfaceWhite,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: boxShadow ?? cardShadow,
      border: border,
    );
  }
}
