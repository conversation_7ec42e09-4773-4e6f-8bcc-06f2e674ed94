import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../providers/cart_provider.dart';
import '../../../providers/auth_provider.dart';
import '../../../providers/transaction_provider.dart';
import '../../../shared/widgets/loading_button.dart';
import '../../../services/payment_service.dart';
import '../../../core/enums/transaction_status.dart';
import '../widgets/cart_item_card.dart';

class CheckoutScreen extends ConsumerStatefulWidget {
  const CheckoutScreen({super.key});

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen> {
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    final cart = ref.watch(cartProvider);
    final user = ref.watch(currentUserProvider).value;

    if (cart.isEmpty) {
      return Scaffold(
        appBar: AppBar(title: const Text('Checkout')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.shopping_cart_outlined, size: 80, color: Colors.grey),
              SizedBox(height: 16),
              Text('Your cart is empty'),
            ],
          ),
        ),
      );
    }

    if (user == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Checkout')),
        body: const Center(
          child: Text('Please log in to continue'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
      ),
      body: Column(
        children: [
          // Order summary
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer info
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Customer Information',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text('Name: ${user.fullName}'),
                          Text('Email: ${user.email}'),
                          if (user.phoneNumber != null)
                            Text('Phone: ${user.phoneNumber}'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Order items
                  Text(
                    'Order Items (${cart.itemCount})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),

                  ...cart.items.map((item) => CartItemCard(
                        item: item,
                        onQuantityChanged: (quantity) {
                          ref.read(cartProvider.notifier).updateItemQuantity(
                                item.productId,
                                quantity,
                              );
                        },
                        onRemove: () {
                          ref
                              .read(cartProvider.notifier)
                              .removeItem(item.productId);
                        },
                      )),

                  const SizedBox(height: 16),

                  // Payment method
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Payment Method',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.credit_card,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(width: 8),
                              const Text('Paystack Payment Gateway'),
                              const Spacer(),
                              Icon(
                                Icons.security,
                                color: Colors.green[600],
                                size: 20,
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Secure payment powered by Paystack',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Order summary and pay button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Summary rows
                  _buildSummaryRow(
                    context,
                    'Subtotal',
                    '₦${cart.subtotal.toStringAsFixed(2)}',
                  ),
                  const SizedBox(height: 8),
                  _buildSummaryRow(
                    context,
                    'Tax (7.5%)',
                    '₦${cart.tax.toStringAsFixed(2)}',
                  ),
                  const Divider(height: 24),
                  _buildSummaryRow(
                    context,
                    'Total',
                    '₦${cart.total.toStringAsFixed(2)}',
                    isTotal: true,
                  ),
                  const SizedBox(height: 16),

                  // Pay button
                  SizedBox(
                    width: double.infinity,
                    child: LoadingButton(
                      onPressed: _isProcessing ? null : _processPayment,
                      isLoading: _isProcessing,
                      height: 50,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.payment),
                          const SizedBox(width: 8),
                          Text(
                            'Pay ₦${cart.total.toStringAsFixed(2)}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                fontSize: isTotal ? 18 : 16,
              ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: isTotal ? 18 : 16,
                color: isTotal ? Theme.of(context).colorScheme.primary : null,
              ),
        ),
      ],
    );
  }

  Future<void> _processPayment() async {
    setState(() => _isProcessing = true);

    try {
      final cart = ref.read(cartProvider);
      final user = ref.read(currentUserProvider).value!;

      // Create transaction
      final transactionController =
          ref.read(transactionControllerProvider.notifier);
      final transaction = await transactionController.createTransaction(
        customerId: user.id,
        customerName: user.fullName,
        customerEmail: user.email,
        mallId: user.mallId ?? 'default-mall',
        merchantId: 'default-merchant', // TODO: Get from scanned products
        items: cart.items,
        subtotal: cart.subtotal,
        tax: cart.tax,
        total: cart.total,
      );

      if (transaction != null) {
        // Process payment with Paystack
        final paymentService = PaymentService();
        final paymentResult = await paymentService.processPayment(
          amount: cart.total,
          email: user.email,
          reference: transaction.id,
        );

        if (paymentResult['success'] == true) {
          // Update transaction status
          await transactionController.updateTransactionStatus(
            transaction.id,
            TransactionStatus.completed,
            paystackReference: paymentResult['reference'],
            receiptQrCode: transaction.id, // Use transaction ID as QR code
            paymentMetadata: paymentResult,
          );

          // Clear cart
          ref.read(cartProvider.notifier).clearCart();

          // Navigate to success screen
          if (mounted) {
            context.go('/customer/payment-success/${transaction.id}');
          }
        } else {
          throw Exception(paymentResult['message'] ?? 'Payment failed');
        }
      } else {
        throw Exception('Failed to create transaction');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }
}
