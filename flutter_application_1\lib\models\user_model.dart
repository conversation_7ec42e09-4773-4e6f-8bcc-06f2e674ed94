import 'package:cloud_firestore/cloud_firestore.dart';
import '../core/enums/user_role.dart';

class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String? mallId; // For customers, sales reps, merchants
  final String? merchantId; // For sales reps
  final String? adminId; // For merchants (who approved them)
  final List<String>? managedMallIds; // For admins and boss
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? securityQuestions; // For sales reps and admins

  UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.phoneNumber,
    this.profileImageUrl,
    this.mallId,
    this.merchantId,
    this.adminId,
    this.managedMallIds,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.securityQuestions,
  });

  String get fullName => '$firstName $lastName';
  String get displayName => fullName;

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      role: UserRole.fromString(data['role'] ?? 'customer'),
      phoneNumber: data['phoneNumber'],
      profileImageUrl: data['profileImageUrl'],
      mallId: data['mallId'],
      merchantId: data['merchantId'],
      adminId: data['adminId'],
      managedMallIds: data['managedMallIds'] != null 
          ? List<String>.from(data['managedMallIds']) 
          : null,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      securityQuestions: data['securityQuestions'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'role': role.value,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'mallId': mallId,
      'merchantId': merchantId,
      'adminId': adminId,
      'managedMallIds': managedMallIds,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'securityQuestions': securityQuestions,
    };
  }

  UserModel copyWith({
    String? email,
    String? firstName,
    String? lastName,
    UserRole? role,
    String? phoneNumber,
    String? profileImageUrl,
    String? mallId,
    String? merchantId,
    String? adminId,
    List<String>? managedMallIds,
    bool? isActive,
    DateTime? updatedAt,
    Map<String, dynamic>? securityQuestions,
  }) {
    return UserModel(
      id: id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      role: role ?? this.role,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      mallId: mallId ?? this.mallId,
      merchantId: merchantId ?? this.merchantId,
      adminId: adminId ?? this.adminId,
      managedMallIds: managedMallIds ?? this.managedMallIds,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      securityQuestions: securityQuestions ?? this.securityQuestions,
    );
  }
}
