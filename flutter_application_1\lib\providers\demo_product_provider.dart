import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';

import '../models/product_model.dart';

/// Demo Product Repository for testing without Firebase
class DemoProductRepository {
  // In-memory storage for demo purposes
  static final List<ProductModel> _products = [
    ProductModel(
      id: '1',
      name: 'Samsung Galaxy S24',
      barcode: '123456789012',
      category: 'Electronics',
      description: 'Latest Samsung smartphone with advanced features',
      pricePerCarton: 299999.99,
      stockQuantity: 15,
      lowStockThreshold: 5,
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      unitType: 'piece',
      unitsPerCarton: 1,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      createdBy: 'demo-merchant',
    ),
    ProductModel(
      id: '2',
      name: 'Nike Air Max',
      barcode: '************',
      category: 'Sports',
      description: 'Comfortable running shoes for athletes',
      pricePerCarton: 45000.00,
      stockQuantity: 8,
      lowStockThreshold: 10,
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      unitType: 'pair',
      unitsPerCarton: 1,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      createdBy: 'demo-merchant',
    ),
    ProductModel(
      id: '3',
      name: 'MacBook Pro',
      barcode: '345678901234',
      category: 'Electronics',
      description: 'High-performance laptop for professionals',
      pricePerCarton: 850000.00,
      stockQuantity: 0,
      lowStockThreshold: 3,
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      unitType: 'piece',
      unitsPerCarton: 1,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      createdBy: 'demo-merchant',
    ),
    ProductModel(
      id: '4',
      name: 'Adidas T-Shirt',
      barcode: '456789012345',
      category: 'Clothing',
      description: 'Premium cotton t-shirt for casual wear',
      pricePerCarton: 12500.00,
      stockQuantity: 25,
      lowStockThreshold: 15,
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      unitType: 'piece',
      unitsPerCarton: 1,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      createdBy: 'demo-merchant',
    ),
    ProductModel(
      id: '5',
      name: 'Organic Apples',
      barcode: '567890123456',
      category: 'Food',
      description: 'Fresh organic apples from local farms',
      pricePerCarton: 8500.00,
      stockQuantity: 50,
      lowStockThreshold: 20,
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      unitType: 'kg',
      unitsPerCarton: 5,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now(),
      createdBy: 'demo-merchant',
    ),
  ];

  // Get products for a specific merchant and mall
  Stream<List<ProductModel>> getProducts(String merchantId, String mallId) {
    return Stream.value(_products
        .where((p) => p.merchantId == merchantId && p.mallId == mallId)
        .toList());
  }

  // Get product by barcode
  Future<ProductModel?> getProductByBarcode(
      String barcode, String mallId) async {
    try {
      final product = _products.firstWhere(
        (p) => p.barcode == barcode && p.mallId == mallId,
      );
      return product;
    } catch (e) {
      return null;
    }
  }

  // Add new product
  Future<void> addProduct(ProductModel product) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final newProduct = ProductModel(
      id: product.id.isEmpty ? const Uuid().v4() : product.id,
      name: product.name,
      barcode: product.barcode,
      category: product.category,
      description: product.description,
      pricePerCarton: product.pricePerCarton,
      stockQuantity: product.stockQuantity,
      lowStockThreshold: product.lowStockThreshold,
      merchantId: product.merchantId,
      mallId: product.mallId,
      unitType: product.unitType,
      unitsPerCarton: product.unitsPerCarton,
      imageUrl: product.imageUrl,
      isActive: product.isActive,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: product.createdBy,
    );

    _products.add(newProduct);
  }

  // Update product
  Future<void> updateProduct(ProductModel product) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index] = product.copyWith(updatedAt: DateTime.now());
    }
  }

  // Delete product
  Future<void> deleteProduct(String productId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    _products.removeWhere((p) => p.id == productId);
  }

  // Update stock quantity
  Future<void> updateStock(String productId, int newQuantity) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final index = _products.indexWhere((p) => p.id == productId);
    if (index != -1) {
      _products[index] = _products[index].copyWith(
        stockQuantity: newQuantity,
        updatedAt: DateTime.now(),
      );
    }
  }

  // Get low stock products
  Stream<List<ProductModel>> getLowStockProducts(
      String merchantId, String mallId) {
    return Stream.value(_products
        .where((p) =>
            p.merchantId == merchantId &&
            p.mallId == mallId &&
            p.stockQuantity <= p.lowStockThreshold)
        .toList());
  }

  // Search products
  Stream<List<ProductModel>> searchProducts(
    String merchantId,
    String mallId,
    String searchTerm,
  ) {
    return Stream.value(_products
        .where((p) =>
            p.merchantId == merchantId &&
            p.mallId == mallId &&
            (p.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
                p.barcode.contains(searchTerm) ||
                p.category.toLowerCase().contains(searchTerm.toLowerCase())))
        .toList());
  }
}

// Demo Product repository provider
final demoProductRepositoryProvider = Provider<DemoProductRepository>((ref) {
  return DemoProductRepository();
});

// Demo Products stream provider
final demoProductsProvider = StreamProvider.family<List<ProductModel>,
    ({String merchantId, String mallId})>((ref, params) {
  final repository = ref.watch(demoProductRepositoryProvider);
  return repository.getProducts(params.merchantId, params.mallId);
});

// Demo Current user's products provider
final demoCurrentUserProductsProvider =
    StreamProvider<List<ProductModel>>((ref) {
  final repository = ref.watch(demoProductRepositoryProvider);
  // For demo, use fixed merchant and mall IDs
  return repository.getProducts('demo-merchant', 'demo-mall');
});

// Demo Low stock products provider
final demoLowStockProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  final repository = ref.watch(demoProductRepositoryProvider);
  return repository.getLowStockProducts('demo-merchant', 'demo-mall');
});

// Demo Product controller
class DemoProductController extends StateNotifier<AsyncValue<void>> {
  DemoProductController(this._repository) : super(const AsyncValue.data(null));

  final DemoProductRepository _repository;

  Future<void> addProduct(ProductModel product) async {
    state = const AsyncValue.loading();

    try {
      await _repository.addProduct(product);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateProduct(ProductModel product) async {
    state = const AsyncValue.loading();

    try {
      await _repository.updateProduct(product);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteProduct(String productId) async {
    state = const AsyncValue.loading();

    try {
      await _repository.deleteProduct(productId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<ProductModel?> getProductByBarcode(
      String barcode, String mallId) async {
    try {
      return await _repository.getProductByBarcode(barcode, mallId);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Demo Product controller provider
final demoProductControllerProvider =
    StateNotifierProvider<DemoProductController, AsyncValue<void>>((ref) {
  final repository = ref.watch(demoProductRepositoryProvider);
  return DemoProductController(repository);
});
