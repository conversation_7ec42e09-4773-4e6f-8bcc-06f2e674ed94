import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/themes/customer_theme.dart';

/// Premium UI components inspired by industry-leading mobile apps
/// Features sophisticated animations, micro-interactions, and professional design

/// Premium Card Component - Inspired by Apple Pay/Google Pay
class PremiumCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final Gradient? gradient;
  final List<BoxShadow>? boxShadow;
  final double borderRadius;
  final VoidCallback? onTap;
  final bool enableHoverEffect;
  final bool enablePressEffect;

  const PremiumCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.gradient,
    this.boxShadow,
    this.borderRadius = CustomerTheme.mediumRadius,
    this.onTap,
    this.enableHoverEffect = true,
    this.enablePressEffect = true,
  });

  @override
  State<PremiumCard> createState() => _PremiumCardState();
}

class _PremiumCardState extends State<PremiumCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: CustomerTheme.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: CustomerTheme.standardCurve,
    ));
    _elevationAnimation = Tween<double>(
      begin: 1.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: CustomerTheme.emphasizedCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enablePressEffect) {
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enablePressEffect) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.enablePressEffect) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: widget.margin,
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? CustomerTheme.surfaceElevated,
              gradient: widget.gradient,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: widget.boxShadow?.map((shadow) {
                    return BoxShadow(
                      color: shadow.color,
                      blurRadius: shadow.blurRadius * _elevationAnimation.value,
                      offset: shadow.offset * _elevationAnimation.value,
                      spreadRadius: shadow.spreadRadius,
                    );
                  }).toList() ??
                  CustomerTheme.shadowMD,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                splashColor: CustomerTheme.primaryTeal.withOpacity(0.1),
                highlightColor: CustomerTheme.primaryTeal.withOpacity(0.05),
                child: Padding(
                  padding: widget.padding ??
                      const EdgeInsets.all(CustomerTheme.spacing16),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Premium Button Component - Inspired by Stripe/Square
class PremiumButton extends StatefulWidget {
  final String text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isDisabled;
  final PremiumButtonStyle style;
  final Size? size;

  const PremiumButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.isLoading = false,
    this.isDisabled = false,
    this.style = PremiumButtonStyle.primary,
    this.size,
  });

  @override
  State<PremiumButton> createState() => _PremiumButtonState();
}

class _PremiumButtonState extends State<PremiumButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: CustomerTheme.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: CustomerTheme.standardCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
    HapticFeedback.mediumImpact();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled =
        !widget.isDisabled && !widget.isLoading && widget.onPressed != null;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.size?.width,
            height: widget.size?.height ?? CustomerTheme.largeTouchTarget,
            decoration: BoxDecoration(
              gradient: _getGradient(),
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
              boxShadow: isEnabled ? CustomerTheme.shadowSM : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: isEnabled ? widget.onPressed : null,
                onTapDown: isEnabled ? _onTapDown : null,
                onTapUp: isEnabled ? _onTapUp : null,
                onTapCancel: isEnabled ? _onTapCancel : null,
                borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: CustomerTheme.spacing24,
                    vertical: CustomerTheme.spacing12,
                  ),
                  child: _buildContent(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    if (widget.isLoading) {
      return const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: _getTextColor(),
            size: 20,
          ),
          const SizedBox(width: CustomerTheme.spacing8),
        ],
        Text(
          widget.text,
          style: CustomerTheme.labelLarge.copyWith(
            color: _getTextColor(),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Gradient _getGradient() {
    if (widget.isDisabled) {
      return const LinearGradient(
        colors: [CustomerTheme.textQuaternary, CustomerTheme.textQuaternary],
      );
    }

    switch (widget.style) {
      case PremiumButtonStyle.primary:
        return CustomerTheme.primaryGradient;
      case PremiumButtonStyle.secondary:
        return CustomerTheme.accentBlueGradient;
      case PremiumButtonStyle.success:
        return CustomerTheme.successGradient;
      case PremiumButtonStyle.warning:
        return CustomerTheme.warningGradient;
      case PremiumButtonStyle.error:
        return CustomerTheme.errorGradient;
    }
  }

  Color _getTextColor() {
    return widget.isDisabled ? CustomerTheme.textTertiary : Colors.white;
  }
}

enum PremiumButtonStyle {
  primary,
  secondary,
  success,
  warning,
  error,
}

/// Premium Loading Indicator - Inspired by Revolut/Cash App
class PremiumLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final double strokeWidth;

  const PremiumLoadingIndicator({
    super.key,
    this.size = 24.0,
    this.color,
    this.strokeWidth = 3.0,
  });

  @override
  State<PremiumLoadingIndicator> createState() =>
      _PremiumLoadingIndicatorState();
}

class _PremiumLoadingIndicatorState extends State<PremiumLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat();
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: SweepGradient(
                  colors: [
                    (widget.color ?? CustomerTheme.primaryTeal)
                        .withOpacity(0.1),
                    widget.color ?? CustomerTheme.primaryTeal,
                    (widget.color ?? CustomerTheme.primaryTeal)
                        .withOpacity(0.1),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
              child: Container(
                margin: EdgeInsets.all(widget.strokeWidth),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: CustomerTheme.surfaceElevated,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Premium Search Bar - Inspired by Amazon/Shopify
class PremiumSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onFilterTap;
  final bool showFilter;
  final TextEditingController? controller;

  const PremiumSearchBar({
    super.key,
    this.hintText = 'Search products...',
    this.onChanged,
    this.onSubmitted,
    this.onFilterTap,
    this.showFilter = true,
    this.controller,
  });

  @override
  State<PremiumSearchBar> createState() => _PremiumSearchBarState();
}

class _PremiumSearchBarState extends State<PremiumSearchBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late TextEditingController _controller;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _animationController = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: CustomerTheme.emphasizedCurve,
    ));
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              color: CustomerTheme.surfaceContainer,
              borderRadius: BorderRadius.circular(CustomerTheme.largeRadius),
              border: Border.all(
                color: _isFocused
                    ? CustomerTheme.primaryTeal
                    : CustomerTheme.borderSecondary,
                width: _isFocused ? 2 : 1,
              ),
              boxShadow: _isFocused ? CustomerTheme.shadowSM : null,
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    onChanged: widget.onChanged,
                    onSubmitted: widget.onSubmitted,
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      hintStyle: CustomerTheme.bodyMedium.copyWith(
                        color: CustomerTheme.textTertiary,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: _isFocused
                            ? CustomerTheme.primaryTeal
                            : CustomerTheme.textTertiary,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: CustomerTheme.spacing16,
                        vertical: CustomerTheme.spacing12,
                      ),
                    ),
                    style: CustomerTheme.bodyMedium.copyWith(
                      color: CustomerTheme.textPrimary,
                    ),
                    onTap: () {
                      setState(() => _isFocused = true);
                      _animationController.forward();
                    },
                    onEditingComplete: () {
                      setState(() => _isFocused = false);
                      _animationController.reverse();
                    },
                  ),
                ),
                if (widget.showFilter) ...[
                  Container(
                    width: 1,
                    height: 24,
                    color: CustomerTheme.borderSecondary,
                  ),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: widget.onFilterTap,
                      borderRadius:
                          BorderRadius.circular(CustomerTheme.mediumRadius),
                      child: Container(
                        padding: const EdgeInsets.all(CustomerTheme.spacing12),
                        child: Icon(
                          Icons.tune,
                          color: CustomerTheme.textSecondary,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
