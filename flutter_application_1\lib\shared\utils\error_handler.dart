import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../themes/customer_theme.dart';

/// Professional Error Handling System - Fortune 500 Level
/// Provides graceful error recovery, user-friendly messages, and comprehensive logging

class ErrorHandler {
  static const String _logTag = 'ErrorHandler';
  
  /// Handle and display user-friendly error messages
  static void handleError(
    BuildContext context,
    dynamic error, {
    String? customMessage,
    VoidCallback? onRetry,
    bool showSnackBar = true,
  }) {
    final errorInfo = _parseError(error);
    
    // Log error for debugging
    _logError(error, errorInfo);
    
    // Show user-friendly message
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        customMessage ?? errorInfo.userMessage,
        onRetry: onRetry,
      );
    }
    
    // Provide haptic feedback
    HapticFeedback.lightImpact();
  }
  
  /// Show professional error dialog
  static Future<void> showErrorDialog(
    BuildContext context,
    dynamic error, {
    String? title,
    String? customMessage,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) async {
    final errorInfo = _parseError(error);
    
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
          ),
          title: Row(
            children: [
              Icon(
                errorInfo.icon,
                color: errorInfo.color,
                size: 24,
              ),
              const SizedBox(width: CustomerTheme.spacing12),
              Text(
                title ?? errorInfo.title,
                style: CustomerTheme.headingSmall.copyWith(
                  color: CustomerTheme.textPrimary,
                ),
              ),
            ],
          ),
          content: Text(
            customMessage ?? errorInfo.userMessage,
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
          ),
          actions: [
            if (onCancel != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel();
                },
                child: Text(
                  'Cancel',
                  style: CustomerTheme.labelLarge.copyWith(
                    color: CustomerTheme.textSecondary,
                  ),
                ),
              ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onRetry != null) {
                  onRetry();
                }
              },
              child: Text(
                onRetry != null ? 'Retry' : 'OK',
                style: CustomerTheme.labelLarge.copyWith(
                  color: CustomerTheme.primaryTeal,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// Show professional error snackbar
  static void _showErrorSnackBar(
    BuildContext context,
    String message, {
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: CustomerTheme.spacing12),
            Expanded(
              child: Text(
                message,
                style: CustomerTheme.bodyMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: CustomerTheme.errorRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
        ),
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }
  
  /// Parse error and return user-friendly information
  static ErrorInfo _parseError(dynamic error) {
    if (error is NetworkException) {
      return ErrorInfo(
        title: 'Connection Error',
        userMessage: 'Please check your internet connection and try again.',
        technicalMessage: error.toString(),
        icon: Icons.wifi_off,
        color: CustomerTheme.errorRed,
        severity: ErrorSeverity.medium,
      );
    }
    
    if (error is AuthenticationException) {
      return ErrorInfo(
        title: 'Authentication Error',
        userMessage: 'Please log in again to continue.',
        technicalMessage: error.toString(),
        icon: Icons.lock_outline,
        color: CustomerTheme.warningAmber,
        severity: ErrorSeverity.high,
      );
    }
    
    if (error is ValidationException) {
      return ErrorInfo(
        title: 'Validation Error',
        userMessage: error.message ?? 'Please check your input and try again.',
        technicalMessage: error.toString(),
        icon: Icons.warning_outlined,
        color: CustomerTheme.warningAmber,
        severity: ErrorSeverity.low,
      );
    }
    
    if (error is ServerException) {
      return ErrorInfo(
        title: 'Server Error',
        userMessage: 'Something went wrong on our end. Please try again later.',
        technicalMessage: error.toString(),
        icon: Icons.cloud_off_outlined,
        color: CustomerTheme.errorRed,
        severity: ErrorSeverity.high,
      );
    }
    
    // Generic error
    return ErrorInfo(
      title: 'Unexpected Error',
      userMessage: 'Something went wrong. Please try again.',
      technicalMessage: error.toString(),
      icon: Icons.error_outline,
      color: CustomerTheme.errorRed,
      severity: ErrorSeverity.medium,
    );
  }
  
  /// Log error with appropriate level
  static void _logError(dynamic error, ErrorInfo errorInfo) {
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String();
      final severityIcon = _getSeverityIcon(errorInfo.severity);
      
      print('$severityIcon [$_logTag] $timestamp');
      print('Title: ${errorInfo.title}');
      print('User Message: ${errorInfo.userMessage}');
      print('Technical: ${errorInfo.technicalMessage}');
      print('Severity: ${errorInfo.severity}');
      print('---');
    }
    
    // TODO: Send to crash reporting service (Firebase Crashlytics, Sentry, etc.)
  }
  
  static String _getSeverityIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return '⚠️';
      case ErrorSeverity.medium:
        return '❌';
      case ErrorSeverity.high:
        return '🚨';
      case ErrorSeverity.critical:
        return '💥';
    }
  }
}

/// Error information container
class ErrorInfo {
  final String title;
  final String userMessage;
  final String technicalMessage;
  final IconData icon;
  final Color color;
  final ErrorSeverity severity;

  const ErrorInfo({
    required this.title,
    required this.userMessage,
    required this.technicalMessage,
    required this.icon,
    required this.color,
    required this.severity,
  });
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Custom exception types for better error handling
class NetworkException implements Exception {
  final String message;
  final int? statusCode;

  const NetworkException(this.message, {this.statusCode});

  @override
  String toString() => 'NetworkException: $message (Status: $statusCode)';
}

class AuthenticationException implements Exception {
  final String message;

  const AuthenticationException(this.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

class ValidationException implements Exception {
  final String? message;
  final Map<String, String>? fieldErrors;

  const ValidationException({this.message, this.fieldErrors});

  @override
  String toString() => 'ValidationException: $message';
}

class ServerException implements Exception {
  final String message;
  final int? statusCode;

  const ServerException(this.message, {this.statusCode});

  @override
  String toString() => 'ServerException: $message (Status: $statusCode)';
}

/// Error boundary widget for catching widget errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace stackTrace)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!, _stackTrace!) ??
          _buildDefaultErrorWidget();
    }
    
    return widget.child;
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(CustomerTheme.spacing16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: CustomerTheme.errorRed,
            size: 48,
          ),
          const SizedBox(height: CustomerTheme.spacing16),
          Text(
            'Something went wrong',
            style: CustomerTheme.headingSmall.copyWith(
              color: CustomerTheme.textPrimary,
            ),
          ),
          const SizedBox(height: CustomerTheme.spacing8),
          Text(
            'Please try refreshing the page',
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
          ),
          const SizedBox(height: CustomerTheme.spacing24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _error = null;
                _stackTrace = null;
              });
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception;
        _stackTrace = details.stack;
      });
      ErrorHandler._logError(details.exception, ErrorHandler._parseError(details.exception));
    };
  }
}
