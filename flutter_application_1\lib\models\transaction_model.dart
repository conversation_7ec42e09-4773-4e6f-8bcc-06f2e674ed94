import 'package:cloud_firestore/cloud_firestore.dart';
import '../core/enums/transaction_status.dart';
import 'cart_item_model.dart';

class TransactionModel {
  final String id;
  final String customerId;
  final String customerName;
  final String customerEmail;
  final String mallId;
  final String merchantId;
  final List<CartItemModel> items;
  final double subtotal;
  final double tax;
  final double total;
  final TransactionStatus status;
  final String? paystackReference;
  final String? paystackAccessCode;
  final String? receiptQrCode;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? verifiedAt;
  final String? verifiedBy; // Sales rep ID
  final Map<String, dynamic>? paymentMetadata;

  TransactionModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.mallId,
    required this.merchantId,
    required this.items,
    required this.subtotal,
    this.tax = 0.0,
    required this.total,
    this.status = TransactionStatus.pending,
    this.paystackReference,
    this.paystackAccessCode,
    this.receiptQrCode,
    required this.createdAt,
    required this.updatedAt,
    this.verifiedAt,
    this.verifiedBy,
    this.paymentMetadata,
  });

  int get totalItems => items.fold(0, (total, item) => total + item.quantity);
  bool get canBeVerified => status.canBeVerified;
  bool get isVerified => status.isVerified;

  factory TransactionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TransactionModel(
      id: doc.id,
      customerId: data['customerId'] ?? '',
      customerName: data['customerName'] ?? '',
      customerEmail: data['customerEmail'] ?? '',
      mallId: data['mallId'] ?? '',
      merchantId: data['merchantId'] ?? '',
      items: (data['items'] as List<dynamic>?)
              ?.map((item) => CartItemModel.fromJson(item))
              .toList() ??
          [],
      subtotal: (data['subtotal'] ?? 0.0).toDouble(),
      tax: (data['tax'] ?? 0.0).toDouble(),
      total: (data['total'] ?? 0.0).toDouble(),
      status: TransactionStatus.fromString(data['status'] ?? 'pending'),
      paystackReference: data['paystackReference'],
      paystackAccessCode: data['paystackAccessCode'],
      receiptQrCode: data['receiptQrCode'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      verifiedAt: data['verifiedAt'] != null
          ? (data['verifiedAt'] as Timestamp).toDate()
          : null,
      verifiedBy: data['verifiedBy'],
      paymentMetadata: data['paymentMetadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'mallId': mallId,
      'merchantId': merchantId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'status': status.value,
      'paystackReference': paystackReference,
      'paystackAccessCode': paystackAccessCode,
      'receiptQrCode': receiptQrCode,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'verifiedAt': verifiedAt != null ? Timestamp.fromDate(verifiedAt!) : null,
      'verifiedBy': verifiedBy,
      'paymentMetadata': paymentMetadata,
    };
  }

  TransactionModel copyWith({
    String? customerId,
    String? customerName,
    String? customerEmail,
    String? mallId,
    String? merchantId,
    List<CartItemModel>? items,
    double? subtotal,
    double? tax,
    double? total,
    TransactionStatus? status,
    String? paystackReference,
    String? paystackAccessCode,
    String? receiptQrCode,
    DateTime? updatedAt,
    DateTime? verifiedAt,
    String? verifiedBy,
    Map<String, dynamic>? paymentMetadata,
  }) {
    return TransactionModel(
      id: id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      mallId: mallId ?? this.mallId,
      merchantId: merchantId ?? this.merchantId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      total: total ?? this.total,
      status: status ?? this.status,
      paystackReference: paystackReference ?? this.paystackReference,
      paystackAccessCode: paystackAccessCode ?? this.paystackAccessCode,
      receiptQrCode: receiptQrCode ?? this.receiptQrCode,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      paymentMetadata: paymentMetadata ?? this.paymentMetadata,
    );
  }
}
