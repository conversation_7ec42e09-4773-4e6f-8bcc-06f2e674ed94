import 'dart:async';
import 'dart:math';
import '../models/boss_analytics_model.dart';

class BossAnalyticsService {
  static final BossAnalyticsService _instance = BossAnalyticsService._internal();
  factory BossAnalyticsService() => _instance;
  BossAnalyticsService._internal();

  // Mock data for demonstration - in real app, this would connect to your backend
  Future<NetworkAnalytics> getNetworkAnalytics() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate API call
    
    final random = Random();
    final now = DateTime.now();
    
    // Generate mock mall performances
    final mallPerformances = [
      MallPerformance(
        mallId: 'mall_001',
        mallName: 'Central Mall',
        revenue: 2500000 + random.nextDouble() * 500000,
        growthRate: 15.5 + random.nextDouble() * 10,
        efficiency: 92.3 + random.nextDouble() * 7,
        totalStores: 120,
        activeStores: 115,
        occupancyRate: 95.8,
        lastUpdated: now,
      ),
      MallPerformance(
        mallId: 'mall_002',
        mallName: 'Plaza Mall',
        revenue: 1800000 + random.nextDouble() * 400000,
        growthRate: 25.3 + random.nextDouble() * 5,
        efficiency: 88.7 + random.nextDouble() * 8,
        totalStores: 85,
        activeStores: 82,
        occupancyRate: 96.5,
        lastUpdated: now,
      ),
      MallPerformance(
        mallId: 'mall_003',
        mallName: 'Gateway Mall',
        revenue: 2200000 + random.nextDouble() * 300000,
        growthRate: 18.2 + random.nextDouble() * 7,
        efficiency: 97.2 + random.nextDouble() * 2,
        totalStores: 95,
        activeStores: 93,
        occupancyRate: 97.9,
        lastUpdated: now,
      ),
      MallPerformance(
        mallId: 'mall_004',
        mallName: 'Metro Mall',
        revenue: 1600000 + random.nextDouble() * 350000,
        growthRate: 12.8 + random.nextDouble() * 8,
        efficiency: 85.4 + random.nextDouble() * 10,
        totalStores: 75,
        activeStores: 71,
        occupancyRate: 94.7,
        lastUpdated: now,
      ),
    ];

    // Generate revenue chart data
    final revenueChart = List.generate(12, (index) {
      final month = DateTime(now.year, index + 1);
      return ChartData(
        label: _getMonthName(index + 1),
        value: 8000000 + random.nextDouble() * 4000000,
        date: month,
      );
    });

    return NetworkAnalytics(
      totalRevenue: mallPerformances.fold(0, (sum, mall) => sum + mall.revenue),
      averageGrowthRate: (mallPerformances.fold(0.0, (sum, mall) => sum + mall.growthRate) / mallPerformances.length),
      totalMalls: mallPerformances.length,
      totalStores: mallPerformances.fold(0, (sum, mall) => sum + mall.totalStores),
      networkEfficiency: (mallPerformances.fold(0.0, (sum, mall) => sum + mall.efficiency) / mallPerformances.length),
      mallPerformances: mallPerformances,
      categoryBreakdown: {
        'Fashion & Apparel': 35.2,
        'Food & Beverage': 28.7,
        'Electronics': 18.5,
        'Health & Beauty': 12.3,
        'Entertainment': 5.3,
      },
      revenueChart: revenueChart,
      generatedAt: now,
    );
  }

  Future<List<MallPerformance>> getMallComparisons() async {
    final analytics = await getNetworkAnalytics();
    return analytics.mallPerformances..sort((a, b) => b.revenue.compareTo(a.revenue));
  }

  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    final random = Random();
    return {
      'totalRevenue': 15200000 + random.nextDouble() * 2000000,
      'monthlyGrowth': 18.5 + random.nextDouble() * 5,
      'activeStores': 375 + random.nextInt(25),
      'occupancyRate': 95.8 + random.nextDouble() * 3,
      'customerSatisfaction': 4.7 + random.nextDouble() * 0.3,
      'averageTransactionValue': 2850 + random.nextDouble() * 500,
    };
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  // Real-time data simulation
  Stream<Map<String, dynamic>> getRealTimeMetrics() async* {
    while (true) {
      await Future.delayed(const Duration(seconds: 5));
      final random = Random();
      yield {
        'currentVisitors': 1250 + random.nextInt(500),
        'todaysSales': 125000 + random.nextDouble() * 50000,
        'activeTransactions': 45 + random.nextInt(20),
        'systemHealth': 98.5 + random.nextDouble() * 1.5,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}