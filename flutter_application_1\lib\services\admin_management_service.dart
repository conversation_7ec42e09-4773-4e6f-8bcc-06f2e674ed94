import 'dart:async';
import 'dart:math';
import '../models/admin_model.dart';

class AdminManagementService {
  static final AdminManagementService _instance = AdminManagementService._internal();
  factory AdminManagementService() => _instance;
  AdminManagementService._internal();

  // Mock data storage
  final List<AdminUser> _admins = [];
  final List<AdminActivity> _activities = [];

  Future<void> initialize() async {
    if (_admins.isEmpty) {
      await _generateMockData();
    }
  }

  Future<void> _generateMockData() async {
    final random = Random();
    final now = DateTime.now();

    // Generate mock admin users
    _admins.addAll([
      AdminUser(
        id: 'admin_001',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+234-************',
        role: AdminRole.superAdmin,
        status: AdminStatus.active,
        permissions: ['all_permissions'],
        assignedMalls: ['mall_001', 'mall_002', 'mall_003', 'mall_004'],
        createdAt: now.subtract(const Duration(days: 365)),
        lastLogin: now.subtract(const Duration(hours: 2)),
        profileImage: 'https://via.placeholder.com/150',
      ),
      AdminUser(
        id: 'admin_002',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+234-************',
        role: AdminRole.mallAdmin,
        status: AdminStatus.active,
        permissions: ['manage_stores', 'view_analytics', 'manage_users'],
        assignedMalls: ['mall_001', 'mall_002'],
        createdAt: now.subtract(const Duration(days: 180)),
        lastLogin: now.subtract(const Duration(minutes: 30)),
        profileImage: 'https://via.placeholder.com/150',
      ),
      AdminUser(
        id: 'admin_003',
        name: 'Michael Brown',
        email: '<EMAIL>',
        phone: '+234-************',
        role: AdminRole.systemAdmin,
        status: AdminStatus.active,
        permissions: ['system_config', 'backup_restore', 'security_settings'],
        assignedMalls: ['mall_003'],
        createdAt: now.subtract(const Duration(days: 90)),
        lastLogin: now.subtract(const Duration(hours: 1)),
        profileImage: 'https://via.placeholder.com/150',
      ),
      AdminUser(
        id: 'admin_004',
        name: 'Emily Davis',
        email: '<EMAIL>',
        phone: '+234-************',
        role: AdminRole.securityAdmin,
        status: AdminStatus.inactive,
        permissions: ['security_monitoring', 'audit_logs', 'incident_response'],
        assignedMalls: ['mall_004'],
        createdAt: now.subtract(const Duration(days: 45)),
        lastLogin: now.subtract(const Duration(days: 3)),
        profileImage: 'https://via.placeholder.com/150',
      ),
      AdminUser(
        id: 'admin_005',
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '+234-************',
        role: AdminRole.mallAdmin,
        status: AdminStatus.pending,
        permissions: ['view_analytics'],
        assignedMalls: [],
        createdAt: now.subtract(const Duration(days: 7)),
        lastLogin: now.subtract(const Duration(days: 7)),
        profileImage: 'https://via.placeholder.com/150',
      ),
    ]);

    // Generate mock activities
    for (int i = 0; i < 50; i++) {
      _activities.add(AdminActivity(
        id: 'activity_${i.toString().padLeft(3, '0')}',
        adminId: _admins[random.nextInt(_admins.length)].id,
        adminName: _admins[random.nextInt(_admins.length)].name,
        action: _getRandomAction(random),
        description: _getRandomDescription(random),
        module: _getRandomModule(random),
        metadata: {'ip': '192.168.1.${random.nextInt(255)}'},
        timestamp: now.subtract(Duration(hours: random.nextInt(168))), // Last week
        ipAddress: '192.168.1.${random.nextInt(255)}',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ));
    }
  }

  String _getRandomAction(Random random) {
    const actions = ['Login', 'Logout', 'Create User', 'Update Settings', 'View Report', 'Delete Record'];
    return actions[random.nextInt(actions.length)];
  }

  String _getRandomDescription(Random random) {
    const descriptions = [
      'User logged into the system',
      'Updated system configuration',
      'Generated financial report',
      'Created new admin user',
      'Modified user permissions',
      'Accessed audit logs'
    ];
    return descriptions[random.nextInt(descriptions.length)];
  }

  String _getRandomModule(Random random) {
    const modules = ['Authentication', 'User Management', 'Reports', 'Settings', 'Security', 'Analytics'];
    return modules[random.nextInt(modules.length)];
  }

  Future<List<AdminUser>> getAllAdmins() async {
    await initialize();
    await Future.delayed(const Duration(milliseconds: 300));
    return List.from(_admins);
  }

  Future<List<AdminUser>> getActiveAdmins() async {
    await initialize();
    await Future.delayed(const Duration(milliseconds: 200));
    return _admins.where((admin) => admin.status == AdminStatus.active).toList();
  }

  Future<AdminUser?> getAdminById(String id) async {
    await initialize();
    try {
      return _admins.firstWhere((admin) => admin.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<bool> createAdmin(AdminUser admin) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _admins.add(admin);
    return true;
  }

  Future<bool> updateAdmin(AdminUser admin) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final index = _admins.indexWhere((a) => a.id == admin.id);
    if (index != -1) {
      _admins[index] = admin;
      return true;
    }
    return false;
  }

  Future<bool> deleteAdmin(String adminId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final index = _admins.indexWhere((a) => a.id == adminId);
    if (index != -1) {
      _admins.removeAt(index);
      return true;
    }
    return false;
  }

  Future<bool> updateAdminStatus(String adminId, AdminStatus status) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final index = _admins.indexWhere((a) => a.id == adminId);
    if (index != -1) {
      final admin = _admins[index];
      _admins[index] = AdminUser(
        id: admin.id,
        name: admin.name,
        email: admin.email,
        phone: admin.phone,
        role: admin.role,
        status: status,
        permissions: admin.permissions,
        assignedMalls: admin.assignedMalls,
        createdAt: admin.createdAt,
        lastLogin: admin.lastLogin,
        profileImage: admin.profileImage,
        metadata: admin.metadata,
      );
      return true;
    }
    return false;
  }

  Future<List<AdminActivity>> getRecentActivities({int limit = 20}) async {
    await initialize();
    await Future.delayed(const Duration(milliseconds: 250));
    _activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return _activities.take(limit).toList();
  }

  Future<Map<String, int>> getAdminStats() async {
    await initialize();
    await Future.delayed(const Duration(milliseconds: 200));
    
    final activeCount = _admins.where((a) => a.status == AdminStatus.active).length;
    final inactiveCount = _admins.where((a) => a.status == AdminStatus.inactive).length;
    final pendingCount = _admins.where((a) => a.status == AdminStatus.pending).length;
    final suspendedCount = _admins.where((a) => a.status == AdminStatus.suspended).length;

    return {
      'total': _admins.length,
      'active': activeCount,
      'inactive': inactiveCount,
      'pending': pendingCount,
      'suspended': suspendedCount,
    };
  }

  Future<List<String>> getAvailablePermissions() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return [
      'all_permissions',
      'manage_stores',
      'view_analytics',
      'manage_users',
      'system_config',
      'backup_restore',
      'security_settings',
      'security_monitoring',
      'audit_logs',
      'incident_response',
      'financial_reports',
      'export_data',
      'import_data',
    ];
  }

  Future<List<String>> getAvailableMalls() async {
    await Future.delayed(const Duration(milliseconds: 100));
    return [
      'mall_001',
      'mall_002',
      'mall_003',
      'mall_004',
    ];
  }
}