# RULES FOR IMPLEMENTING CODE CHANGES
~~~START_OF_CODING_RULES~~~

## Rule 1: Scope of your coding task
You must implement everything mentioned in the instructions that is related to this file. It can happen that instruction mention code changes needed in this file on multiple places and all of them have to be implemented now. We will not make any other changes to this file before the review and finishing this task.

## Rule 2: Output format
You must output the COMPLETE NEW VERSION of this file in following format:
---start_of_format---
```
the full contents of the updated file, without skipping over any content
```
---end_of_format---

## Rule 3: Comprehensive Codebase Insight
It's crucial to grasp the full scope of the codebase related to your tasks to avert mistakes. Check the initial conversation message for a list of files. Pay a lot of attention to files that are directly included in the file you are currently modifying or that are importing your file.
Consider these examples to guide your approach and thought process:
---start_of_examples---
- UI components or templates: Instead of placing scripts directly on specific pages, integrating them in the <head> section or as reusable partials enhances application-wide consistency and reusability.
- Database operations: Be careful not to execute an action, like password hashing, both in a routing function and a model's pre('save') hook, which could lead to redundancy and errors.
- Adding backend logic: Prior to creating new functions, verify if an equivalent function exists in the codebase that you could import and use, preventing unnecessary code duplication and keeping the project efficient.
---end_of_examples---

## Rule 4: Coding principles
Write high-quality code, first organize it logically with clear, meaningful names for variables, functions, and classes. Aim for simplicity and adhere to the DRY (Don't Repeat Yourself) principle to avoid code duplication. Pay attention not to duplicate the code that's present in other files, contents of which are below. Ensure your codebase is structured and modular for easy navigation and updates.

If the instructions have comments like `// ..add code here...` or `# placeholder for code`, instead of copying the comment, interpret the instructions and output the relevant code.

Your reply MUST NOT omit any code in the new implementation or substitute anything with comments like `// .. rest of the code goes here ..` or `# insert existing code here`, because I will overwrite the existing file with the content you provide. Output ONLY the content for this file, without additional explanation, suggestions or notes. Your output MUST start with ``` and MUST end with ``` and include only the complete file contents.

When working with configuration files (e.g. config.json, .env,...), for hardcoded configuration values that the user needs to change, mark the line that needs user configuration with `INPUT_REQUIRED {config_description}` comment,  where `config_description` is a description of the value that needs to be set by the user. Use appropriate syntax for comments in the file you're saving (for example `// INPUT_REQUIRED {config_description}` in JavaScript). NEVER ask the user to write code or provide implementation, even if the instructions suggest it! If the file type doesn't support comments (eg JSON), don't add any.

## Rule 5: Logging
Whenever you write code, make sure to log code execution so that when a developer looks at the CLI output, they can understand what is happening on the server. If the description above mentions the exact code that needs to be added but doesn't contain enough logs, you need to add the logs handlers inside that code yourself.

## Rule 6: Error handling
Whenever you write code, make sure to add error handling for all edge cases you can think of because this app will be used in production so there shouldn't be any crashes. Whenever you log the error, you **MUST** log the entire error message and trace and not only the error message. If the description above mentions the exact code that needs to be added but doesn't contain enough error handlers, you need to add the error handlers inside that code yourself.

{% if state.has_frontend() %}
## Rule 7: Showing errors on the frontend
If there is an error in the API request, log the error with `console.error(error)` and return the error message to the frontend by throwing an error in the client/api/<FILE> file that makes the actual API request. In the .tsx file that called the API function, catch the error and show the error message to the user by showing `error.message` inside the toast's `description` value.
---example_for_rule_7---
For example, let's say a client needs to submit some answer to the backend. In the client/api/<FILE>.ts file you would catch the error and return it like this:
```
try {
    const response = await api.post(`/submit/answer/`, data);
    return response.data;
} catch (error) {
    console.error(error);
    throw new Error(error?.response?.data?.error || error.message);
}
```

And in the .tsx file, catch the error and show it like this:
```
const onSubmit = async (data) => {
    try {
      setSubmitting(true)
      await submitAnswer(data)
      toast({
        title: "Success",
        description: "Answer submitted successfully"
      })
    } catch (error) {
      console.error("Login error:", error.message)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to submit answers"
      })
    } finally {
      setSubmitting(false)
    }
  }
```
~~~END_OF_CODING_RULES~~~
{% endif %}
