import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:intl/intl.dart';

import '../../../providers/transaction_provider.dart';
import '../../../shared/themes/customer_theme.dart';

import '../widgets/modern_ui_components.dart';

class PaymentSuccessScreen extends ConsumerWidget {
  final String transactionId;

  const PaymentSuccessScreen({
    super.key,
    required this.transactionId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Theme(
      data: CustomerTheme.lightTheme,
      child: Scaffold(
        backgroundColor: CustomerTheme.backgroundLight,
        appBar: AppBar(
          title: const Text('Payment Successful'),
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: CustomerTheme.textPrimary,
        ),
        body: FutureBuilder(
          future: ref
              .read(transactionRepositoryProvider)
              .getTransaction(transactionId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                          CustomerTheme.primaryTeal),
                    ),
                    const SizedBox(height: CustomerTheme.spacingM),
                    Text(
                      'Loading receipt...',
                      style: CustomerTheme.bodyLarge.copyWith(
                        color: CustomerTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            if (snapshot.hasError || !snapshot.hasData) {
              return _buildErrorState(context);
            }

            final transaction = snapshot.data!;

            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    CustomerTheme.successGreen.withOpacity(0.05),
                    CustomerTheme.backgroundLight,
                  ],
                ),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(CustomerTheme.spacingL),
                child: Column(
                  children: [
                    // Success animation with modern design
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: CustomerTheme.successGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: CustomerTheme.successGreen.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check_rounded,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: CustomerTheme.spacingXL),

                    // Success message
                    Text(
                      'Payment Successful!',
                      style: CustomerTheme.headingLarge.copyWith(
                        color: CustomerTheme.successGreen,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: CustomerTheme.spacingS),

                    Text(
                      'Your payment has been processed successfully',
                      style: CustomerTheme.bodyLarge.copyWith(
                        color: CustomerTheme.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: CustomerTheme.spacingXXL),

                    // Enhanced QR Code for receipt verification
                    ModernCard(
                      gradient: LinearGradient(
                        colors: [
                          CustomerTheme.primaryTeal.withOpacity(0.05),
                          CustomerTheme.primaryTeal.withOpacity(0.02),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(
                                    CustomerTheme.spacingS),
                                decoration: BoxDecoration(
                                  color: CustomerTheme.primaryTeal
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(
                                      CustomerTheme.smallRadius),
                                ),
                                child: Icon(
                                  Icons.qr_code_2,
                                  color: CustomerTheme.primaryTeal,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: CustomerTheme.spacingM),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Digital Receipt',
                                      style:
                                          CustomerTheme.headingSmall.copyWith(
                                        color: CustomerTheme.textPrimary,
                                      ),
                                    ),
                                    Text(
                                      'QR Code for verification',
                                      style: CustomerTheme.bodySmall.copyWith(
                                        color: CustomerTheme.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: CustomerTheme.spacingXL),
                          Container(
                            padding:
                                const EdgeInsets.all(CustomerTheme.spacingL),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  CustomerTheme.largeRadius),
                              boxShadow: [
                                BoxShadow(
                                  color: CustomerTheme.primaryTeal
                                      .withOpacity(0.1),
                                  blurRadius: 15,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: QrImageView(
                              data: transaction.id,
                              version: QrVersions.auto,
                              size: 200.0,
                              backgroundColor: Colors.white,
                              foregroundColor: CustomerTheme.textPrimary,
                            ),
                          ),
                          const SizedBox(height: CustomerTheme.spacingL),
                          Container(
                            padding:
                                const EdgeInsets.all(CustomerTheme.spacingM),
                            decoration: BoxDecoration(
                              color: CustomerTheme.infoBlue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                  CustomerTheme.mediumRadius),
                              border: Border.all(
                                color: CustomerTheme.infoBlue.withOpacity(0.2),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: CustomerTheme.infoBlue,
                                  size: 20,
                                ),
                                const SizedBox(width: CustomerTheme.spacingS),
                                Expanded(
                                  child: Text(
                                    'Show this QR code to the sales representative for verification and receipt confirmation',
                                    style: CustomerTheme.bodySmall.copyWith(
                                      color: CustomerTheme.infoBlue,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Enhanced Transaction details
                    ModernCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(
                                    CustomerTheme.spacingS),
                                decoration: BoxDecoration(
                                  color:
                                      CustomerTheme.accentBlue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(
                                      CustomerTheme.smallRadius),
                                ),
                                child: const Icon(
                                  Icons.receipt_long,
                                  color: CustomerTheme.accentBlue,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: CustomerTheme.spacingM),
                              Text(
                                'Transaction Details',
                                style: CustomerTheme.headingSmall.copyWith(
                                  color: CustomerTheme.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: CustomerTheme.spacingL),
                          _buildModernDetailRow(
                            'Transaction ID',
                            transaction.id.substring(0, 8),
                            Icons.tag,
                          ),
                          _buildModernDetailRow(
                            'Date & Time',
                            DateFormat('MMM dd, yyyy • HH:mm')
                                .format(transaction.createdAt),
                            Icons.schedule,
                          ),
                          _buildModernDetailRow(
                            'Items',
                            '${transaction.totalItems} item${transaction.totalItems != 1 ? 's' : ''}',
                            Icons.shopping_bag_outlined,
                          ),
                          _buildModernDetailRow(
                            'Subtotal',
                            '₦${transaction.subtotal.toStringAsFixed(2)}',
                            Icons.calculate_outlined,
                          ),
                          _buildModernDetailRow(
                            'Tax (7.5%)',
                            '₦${transaction.tax.toStringAsFixed(2)}',
                            Icons.percent,
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(
                                vertical: CustomerTheme.spacingM),
                            height: 1,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.transparent,
                                  CustomerTheme.borderLight,
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),
                          _buildModernDetailRow(
                            'Total Paid',
                            '₦${transaction.total.toStringAsFixed(2)}',
                            Icons.payment,
                            isTotal: true,
                          ),
                          if (transaction.paystackReference != null)
                            _buildModernDetailRow(
                              'Payment Reference',
                              transaction.paystackReference!,
                              Icons.confirmation_number_outlined,
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Enhanced Action buttons
                    Column(
                      children: [
                        ModernButton(
                          text: 'Continue Shopping',
                          icon: Icons.shopping_cart_outlined,
                          gradient: CustomerTheme.primaryGradient,
                          onPressed: () => context.go('/customer'),
                          width: double.infinity,
                          height: 56,
                        ),
                        const SizedBox(height: CustomerTheme.spacingM),
                        ModernButton(
                          text: 'View Purchase History',
                          icon: Icons.history,
                          isOutlined: true,
                          backgroundColor: CustomerTheme.primaryTeal,
                          onPressed: () => context.go('/customer/history'),
                          width: double.infinity,
                          height: 48,
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Help text
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue[700],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Keep this receipt safe. You may need to show it for verification or returns.',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.blue[700],
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Colors.green : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernDetailRow(String label, String value, IconData icon,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: CustomerTheme.spacingS),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(CustomerTheme.spacingXS),
            decoration: BoxDecoration(
              color: isTotal
                  ? CustomerTheme.successGreen.withOpacity(0.1)
                  : CustomerTheme.textTertiary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(CustomerTheme.smallRadius),
            ),
            child: Icon(
              icon,
              size: 16,
              color: isTotal
                  ? CustomerTheme.successGreen
                  : CustomerTheme.textTertiary,
            ),
          ),
          const SizedBox(width: CustomerTheme.spacingM),
          Expanded(
            child: Text(
              label,
              style: isTotal
                  ? CustomerTheme.labelLarge.copyWith(
                      color: CustomerTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    )
                  : CustomerTheme.bodyMedium.copyWith(
                      color: CustomerTheme.textSecondary,
                    ),
            ),
          ),
          Text(
            value,
            style: isTotal
                ? CustomerTheme.headingSmall.copyWith(
                    color: CustomerTheme.successGreen,
                    fontWeight: FontWeight.bold,
                  )
                : CustomerTheme.labelLarge.copyWith(
                    color: CustomerTheme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Receipt',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          const Text(
            'Unable to load transaction details',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/customer'),
            child: const Text('Go to Dashboard'),
          ),
        ],
      ),
    );
  }
}
