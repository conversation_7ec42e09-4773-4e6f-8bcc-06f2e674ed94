import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';
import '../models/cart_item_model.dart';
import '../services/firebase_service.dart';
import '../core/enums/transaction_status.dart';
import 'auth_provider.dart';

// Transaction repository
class TransactionRepository {
  final FirebaseService _firebase = FirebaseService.instance;

  // Get transactions for a specific customer
  Stream<List<TransactionModel>> getCustomerTransactions(String customerId) {
    return _firebase.transactionsCollection
        .where('customerId', isEqualTo: customerId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TransactionModel.fromFirestore(doc))
            .toList());
  }

  // Get transactions for a specific mall
  Stream<List<TransactionModel>> getMallTransactions(String mallId) {
    return _firebase.transactionsCollection
        .where('mallId', isEqualTo: mallId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TransactionModel.fromFirestore(doc))
            .toList());
  }

  // Get transactions for a specific merchant
  Stream<List<TransactionModel>> getMerchantTransactions(String merchantId) {
    return _firebase.transactionsCollection
        .where('merchantId', isEqualTo: merchantId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TransactionModel.fromFirestore(doc))
            .toList());
  }

  // Get transactions by date range
  Stream<List<TransactionModel>> getTransactionsByDateRange(
    String mallId,
    DateTime startDate,
    DateTime endDate,
  ) {
    return _firebase.transactionsCollection
        .where('mallId', isEqualTo: mallId)
        .where('createdAt', isGreaterThanOrEqualTo: startDate)
        .where('createdAt', isLessThanOrEqualTo: endDate)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TransactionModel.fromFirestore(doc))
            .toList());
  }

  // Create new transaction
  Future<TransactionModel> createTransaction({
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String mallId,
    required String merchantId,
    required List<CartItemModel> items,
    required double subtotal,
    required double tax,
    required double total,
  }) async {
    try {
      final transaction = TransactionModel(
        id: const Uuid().v4(),
        customerId: customerId,
        customerName: customerName,
        customerEmail: customerEmail,
        mallId: mallId,
        merchantId: merchantId,
        items: items,
        subtotal: subtotal,
        tax: tax,
        total: total,
        status: TransactionStatus.pending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firebase.transactionsCollection
          .doc(transaction.id)
          .set(transaction.toFirestore());

      return transaction;
    } catch (e) {
      throw Exception('Failed to create transaction: $e');
    }
  }

  // Update transaction status
  Future<void> updateTransactionStatus(
    String transactionId,
    TransactionStatus status, {
    String? paystackReference,
    String? receiptQrCode,
    String? verifiedBy,
    Map<String, dynamic>? paymentMetadata,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status.value,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (paystackReference != null) {
        updateData['paystackReference'] = paystackReference;
      }

      if (receiptQrCode != null) {
        updateData['receiptQrCode'] = receiptQrCode;
      }

      if (verifiedBy != null) {
        updateData['verifiedBy'] = verifiedBy;
        updateData['verifiedAt'] = FieldValue.serverTimestamp();
      }

      if (paymentMetadata != null) {
        updateData['paymentMetadata'] = paymentMetadata;
      }

      await _firebase.transactionsCollection
          .doc(transactionId)
          .update(updateData);
    } catch (e) {
      throw Exception('Failed to update transaction: $e');
    }
  }

  // Get transaction by ID
  Future<TransactionModel?> getTransaction(String transactionId) async {
    try {
      final doc =
          await _firebase.transactionsCollection.doc(transactionId).get();

      if (doc.exists) {
        return TransactionModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get transaction: $e');
    }
  }

  // Get transaction by QR code
  Future<TransactionModel?> getTransactionByQrCode(String qrCode) async {
    try {
      final query = await _firebase.transactionsCollection
          .where('receiptQrCode', isEqualTo: qrCode)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return TransactionModel.fromFirestore(query.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get transaction by QR code: $e');
    }
  }

  // Get daily sales summary
  Future<Map<String, dynamic>> getDailySalesSummary(
    String mallId,
    DateTime date,
  ) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final query = await _firebase.transactionsCollection
          .where('mallId', isEqualTo: mallId)
          .where('createdAt', isGreaterThanOrEqualTo: startOfDay)
          .where('createdAt', isLessThan: endOfDay)
          .where('status', whereIn: ['completed', 'verified']).get();

      final transactions =
          query.docs.map((doc) => TransactionModel.fromFirestore(doc)).toList();

      final totalSales = transactions.fold<double>(
        0.0,
        (total, transaction) => total + transaction.total,
      );

      final totalTransactions = transactions.length;
      final totalItems = transactions.fold<int>(
        0,
        (total, transaction) => total + transaction.totalItems,
      );

      return {
        'totalSales': totalSales,
        'totalTransactions': totalTransactions,
        'totalItems': totalItems,
        'transactions': transactions,
      };
    } catch (e) {
      throw Exception('Failed to get daily sales summary: $e');
    }
  }
}

// Transaction repository provider
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  return TransactionRepository();
});

// Customer transactions provider
final customerTransactionsProvider =
    StreamProvider.family<List<TransactionModel>, String>((ref, customerId) {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getCustomerTransactions(customerId);
});

// Mall transactions provider
final mallTransactionsProvider =
    StreamProvider.family<List<TransactionModel>, String>((ref, mallId) {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getMallTransactions(mallId);
});

// Merchant transactions provider
final merchantTransactionsProvider =
    StreamProvider.family<List<TransactionModel>, String>((ref, merchantId) {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getMerchantTransactions(merchantId);
});

// Current user transactions provider (simplified for demo)
final currentUserTransactionsProvider =
    StreamProvider<List<TransactionModel>>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  // For demo, always return transactions for demo merchant
  return repository.getMerchantTransactions('demo-merchant');
});

// Transaction controller
class TransactionController
    extends StateNotifier<AsyncValue<TransactionModel?>> {
  TransactionController(this._repository) : super(const AsyncValue.data(null));

  final TransactionRepository _repository;

  Future<TransactionModel?> createTransaction({
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String mallId,
    required String merchantId,
    required List<CartItemModel> items,
    required double subtotal,
    required double tax,
    required double total,
  }) async {
    state = const AsyncValue.loading();

    try {
      final transaction = await _repository.createTransaction(
        customerId: customerId,
        customerName: customerName,
        customerEmail: customerEmail,
        mallId: mallId,
        merchantId: merchantId,
        items: items,
        subtotal: subtotal,
        tax: tax,
        total: total,
      );

      state = AsyncValue.data(transaction);
      return transaction;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  Future<void> updateTransactionStatus(
    String transactionId,
    TransactionStatus status, {
    String? paystackReference,
    String? receiptQrCode,
    String? verifiedBy,
    Map<String, dynamic>? paymentMetadata,
  }) async {
    try {
      await _repository.updateTransactionStatus(
        transactionId,
        status,
        paystackReference: paystackReference,
        receiptQrCode: receiptQrCode,
        verifiedBy: verifiedBy,
        paymentMetadata: paymentMetadata,
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<TransactionModel?> getTransactionByQrCode(String qrCode) async {
    try {
      return await _repository.getTransactionByQrCode(qrCode);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }
}

// Transaction controller provider
final transactionControllerProvider =
    StateNotifierProvider<TransactionController, AsyncValue<TransactionModel?>>(
        (ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return TransactionController(repository);
});
