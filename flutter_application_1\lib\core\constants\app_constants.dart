class AppConstants {
  // App Info
  static const String appName = 'Mall Management System';
  static const String appVersion = '1.0.0';
  
  // User Roles
  static const String roleCustomer = 'customer';
  static const String roleSalesRep = 'sales_rep';
  static const String roleMerchant = 'merchant';
  static const String roleAdmin = 'admin';
  static const String roleBoss = 'boss';
  
  // Collections
  static const String usersCollection = 'users';
  static const String merchantsCollection = 'merchants';
  static const String productsCollection = 'products';
  static const String transactionsCollection = 'transactions';
  static const String receiptsCollection = 'receipts';
  static const String mallsCollection = 'malls';
  static const String salesRepsCollection = 'salesReps';
  static const String adminsCollection = 'admins';
  
  // Storage Paths
  static const String productImagesPath = 'product_images';
  static const String receiptImagesPath = 'receipt_images';
  static const String profileImagesPath = 'profile_images';
  
  // Shared Preferences Keys
  static const String userRoleKey = 'user_role';
  static const String userIdKey = 'user_id';
  static const String currentMallIdKey = 'current_mall_id';
  static const String themeKey = 'theme_mode';
  
  // Payment
  static const String paystackPublicKey = 'pk_test_your_paystack_public_key';
  
  // Location
  static const double locationRadiusMeters = 500.0; // 500 meters radius for mall detection
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxProductNameLength = 100;
  static const int maxDescriptionLength = 500;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
}
