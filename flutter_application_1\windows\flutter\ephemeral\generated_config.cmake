# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\my app\\flutter_windows_3.22.3-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\my money\\flutter_application_1" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\my app\\flutter_windows_3.22.3-stable\\flutter"
  "PROJECT_DIR=C:\\my money\\flutter_application_1"
  "FLUTTER_ROOT=C:\\my app\\flutter_windows_3.22.3-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\my money\\flutter_application_1\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\my money\\flutter_application_1"
  "FLUTTER_TARGET=C:\\my money\\flutter_application_1\\lib/demo_main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8yMzVkYjkxMWJhMjc5NzIyZjVlNjg1ZjM4YjBlZDMwZmE3ZTg1NzBhLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\my money\\flutter_application_1\\.dart_tool\\package_config.json"
)
