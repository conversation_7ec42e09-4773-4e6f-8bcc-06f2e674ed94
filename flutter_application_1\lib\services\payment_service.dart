import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../core/constants/app_constants.dart';

class PaymentService {
  static const String _baseUrl = 'https://api.paystack.co';
  
  // For demo purposes, we'll simulate payment processing
  // In production, you would integrate with actual Paystack SDK
  
  Future<Map<String, dynamic>> processPayment({
    required double amount,
    required String email,
    required String reference,
  }) async {
    try {
      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, randomly succeed or fail
      final random = Random();
      final success = random.nextBool() && random.nextBool(); // 75% success rate
      
      if (success) {
        return {
          'success': true,
          'reference': reference,
          'amount': amount,
          'currency': 'NGN',
          'status': 'success',
          'gateway_response': 'Successful',
          'paid_at': DateTime.now().toIso8601String(),
          'channel': 'card',
          'fees': amount * 0.015, // 1.5% fee
          'authorization': {
            'authorization_code': 'AUTH_${_generateRandomString(10)}',
            'bin': '408408',
            'last4': '4081',
            'exp_month': '12',
            'exp_year': '2030',
            'channel': 'card',
            'card_type': 'visa',
            'bank': 'TEST BANK',
            'country_code': 'NG',
            'brand': 'visa',
          },
          'customer': {
            'id': _generateRandomString(8),
            'email': email,
          },
        };
      } else {
        return {
          'success': false,
          'message': 'Payment was declined by your bank',
          'reference': reference,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
        'reference': reference,
      };
    }
  }
  
  // Initialize payment (for actual Paystack integration)
  Future<Map<String, dynamic>> initializePayment({
    required double amount,
    required String email,
    required String reference,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/transaction/initialize'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.paystackPublicKey}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'amount': (amount * 100).toInt(), // Convert to kobo
          'email': email,
          'reference': reference,
          'currency': 'NGN',
          'metadata': metadata,
        }),
      );

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['status'] == true) {
        return {
          'success': true,
          'authorization_url': data['data']['authorization_url'],
          'access_code': data['data']['access_code'],
          'reference': data['data']['reference'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to initialize payment',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }
  
  // Verify payment
  Future<Map<String, dynamic>> verifyPayment(String reference) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/transaction/verify/$reference'),
        headers: {
          'Authorization': 'Bearer ${AppConstants.paystackPublicKey}',
        },
      );

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['status'] == true) {
        final transactionData = data['data'];
        return {
          'success': true,
          'status': transactionData['status'],
          'reference': transactionData['reference'],
          'amount': transactionData['amount'] / 100, // Convert from kobo
          'currency': transactionData['currency'],
          'paid_at': transactionData['paid_at'],
          'channel': transactionData['channel'],
          'fees': transactionData['fees'] / 100,
          'authorization': transactionData['authorization'],
          'customer': transactionData['customer'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Payment verification failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }
  
  // Get payment history
  Future<Map<String, dynamic>> getPaymentHistory({
    int page = 1,
    int perPage = 50,
    String? customer,
    String? status,
    DateTime? from,
    DateTime? to,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'perPage': perPage.toString(),
      };
      
      if (customer != null) queryParams['customer'] = customer;
      if (status != null) queryParams['status'] = status;
      if (from != null) queryParams['from'] = from.toIso8601String();
      if (to != null) queryParams['to'] = to.toIso8601String();
      
      final uri = Uri.parse('$_baseUrl/transaction').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${AppConstants.paystackPublicKey}',
        },
      );

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['status'] == true) {
        return {
          'success': true,
          'data': data['data'],
          'meta': data['meta'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to fetch payment history',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }
  
  // Calculate fees
  double calculateFees(double amount) {
    // Paystack fees: 1.5% + ₦100 (capped at ₦2000)
    final percentageFee = amount * 0.015;
    final totalFee = percentageFee + 100;
    return totalFee > 2000 ? 2000 : totalFee;
  }
  
  // Generate random string for demo purposes
  String _generateRandomString(int length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
  
  // Format amount for display
  String formatAmount(double amount) {
    return '₦${amount.toStringAsFixed(2)}';
  }
  
  // Validate email
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  // Generate payment reference
  String generateReference() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return 'MMS_${timestamp}_$random';
  }
}
