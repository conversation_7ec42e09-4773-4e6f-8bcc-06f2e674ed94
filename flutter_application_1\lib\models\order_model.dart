import 'package:cloud_firestore/cloud_firestore.dart';
import 'cart_item_model.dart';

enum OrderStatus {
  pending('pending', 'Pending'),
  confirmed('confirmed', 'Confirmed'),
  processing('processing', 'Processing'),
  shipped('shipped', 'Shipped'),
  delivered('delivered', 'Delivered'),
  cancelled('cancelled', 'Cancelled'),
  refunded('refunded', 'Refunded');

  const OrderStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static OrderStatus fromString(String value) {
    return OrderStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => OrderStatus.pending,
    );
  }

  bool get canBeCancelled => this == OrderStatus.pending || this == OrderStatus.confirmed;
  bool get canBeProcessed => this == OrderStatus.confirmed;
  bool get canBeShipped => this == OrderStatus.processing;
  bool get canBeDelivered => this == OrderStatus.shipped;
  bool get isCompleted => this == OrderStatus.delivered;
  bool get isCancelled => this == OrderStatus.cancelled;
}

class OrderModel {
  final String id;
  final String customerId;
  final String customerName;
  final String customerEmail;
  final String customerPhone;
  final String merchantId;
  final String mallId;
  final List<CartItemModel> items;
  final double subtotal;
  final double tax;
  final double shippingFee;
  final double total;
  final OrderStatus status;
  final String? notes;
  final String? trackingNumber;
  final String? shippingAddress;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? confirmedAt;
  final DateTime? shippedAt;
  final DateTime? deliveredAt;
  final String? processedBy;
  final Map<String, dynamic>? metadata;

  OrderModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.customerPhone,
    required this.merchantId,
    required this.mallId,
    required this.items,
    required this.subtotal,
    this.tax = 0.0,
    this.shippingFee = 0.0,
    required this.total,
    this.status = OrderStatus.pending,
    this.notes,
    this.trackingNumber,
    this.shippingAddress,
    required this.createdAt,
    required this.updatedAt,
    this.confirmedAt,
    this.shippedAt,
    this.deliveredAt,
    this.processedBy,
    this.metadata,
  });

  int get totalItems => items.fold(0, (total, item) => total + item.quantity);
  double get totalWeight => items.fold(0.0, (total, item) => total + (item.quantity * 1.0)); // Assuming 1kg per item
  
  factory OrderModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return OrderModel(
      id: doc.id,
      customerId: data['customerId'] ?? '',
      customerName: data['customerName'] ?? '',
      customerEmail: data['customerEmail'] ?? '',
      customerPhone: data['customerPhone'] ?? '',
      merchantId: data['merchantId'] ?? '',
      mallId: data['mallId'] ?? '',
      items: (data['items'] as List<dynamic>?)
              ?.map((item) => CartItemModel.fromJson(item))
              .toList() ??
          [],
      subtotal: (data['subtotal'] ?? 0.0).toDouble(),
      tax: (data['tax'] ?? 0.0).toDouble(),
      shippingFee: (data['shippingFee'] ?? 0.0).toDouble(),
      total: (data['total'] ?? 0.0).toDouble(),
      status: OrderStatus.fromString(data['status'] ?? 'pending'),
      notes: data['notes'],
      trackingNumber: data['trackingNumber'],
      shippingAddress: data['shippingAddress'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      confirmedAt: data['confirmedAt'] != null ? (data['confirmedAt'] as Timestamp).toDate() : null,
      shippedAt: data['shippedAt'] != null ? (data['shippedAt'] as Timestamp).toDate() : null,
      deliveredAt: data['deliveredAt'] != null ? (data['deliveredAt'] as Timestamp).toDate() : null,
      processedBy: data['processedBy'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'customerPhone': customerPhone,
      'merchantId': merchantId,
      'mallId': mallId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shippingFee': shippingFee,
      'total': total,
      'status': status.value,
      'notes': notes,
      'trackingNumber': trackingNumber,
      'shippingAddress': shippingAddress,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'confirmedAt': confirmedAt != null ? Timestamp.fromDate(confirmedAt!) : null,
      'shippedAt': shippedAt != null ? Timestamp.fromDate(shippedAt!) : null,
      'deliveredAt': deliveredAt != null ? Timestamp.fromDate(deliveredAt!) : null,
      'processedBy': processedBy,
      'metadata': metadata,
    };
  }

  OrderModel copyWith({
    String? customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? merchantId,
    String? mallId,
    List<CartItemModel>? items,
    double? subtotal,
    double? tax,
    double? shippingFee,
    double? total,
    OrderStatus? status,
    String? notes,
    String? trackingNumber,
    String? shippingAddress,
    DateTime? updatedAt,
    DateTime? confirmedAt,
    DateTime? shippedAt,
    DateTime? deliveredAt,
    String? processedBy,
    Map<String, dynamic>? metadata,
  }) {
    return OrderModel(
      id: id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      merchantId: merchantId ?? this.merchantId,
      mallId: mallId ?? this.mallId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shippingFee: shippingFee ?? this.shippingFee,
      total: total ?? this.total,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      shippedAt: shippedAt ?? this.shippedAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      processedBy: processedBy ?? this.processedBy,
      metadata: metadata ?? this.metadata,
    );
  }
}
