{
  "name": "{{ folder_name }}",
  "type": "module",
  "version": "0.0.1",
  "description": "{{ project_name|escape_string}}",
  "main": "server.js",
  "author": "",
  "license": "UNLICENSED",
  "dependencies": {
    "axios": "^1.7.2",
    "@radix-ui/react-label": "^2.1.0",
    "@radix-ui/react-slot": "^1.1.0",
    "bcrypt": "*",
    "bull": "*",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.1",
    "cors": "*",
    "dotenv": "*",
    "express": "*",
    "jsonschema": "*",
{% if options.db_type == 'nosql' %}
    "mongoose": "*",
    "validator": "*",
{% endif %}
    "lucide-react": "^0.395.0",
    "nodemailer": "*",
    "pino": "*",
    "tailwind-merge": "^2.3.0",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@prisma/client": "*",
    "@types/node": "^20.14.6",
    "@types/react": "^18.2.64",
    "@types/react-dom": "^18.2.21",
    "@vitejs/plugin-react": "^4.2.1",
    "autoprefixer": "^10.4.19",
    "babel": "*",
    "babel-preset-env": "*",
    "babel-preset-jest": "*",
    "concurrently": "*",
    "eslint": "*",
    "eslint-config-airbnb-base": "*",
    "eslint-plugin-import": "*",
    "eslint-plugin-react": "^7.34.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.5",
    "jest": "*",
    "nodemon": "*",
    "pino-pretty": "*",
    "postcss": "^8.4.38",
    "prettier": "*",
{% if options.db_type == 'sql' %}
    "prisma": "*",
{% endif %}
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "*",
    "supertest": "*",
    "tailwindcss": "^3.4.4",
    "vite": "^5.1.6"
  },
  "scripts": {
    "start:api": "node server.js",
    "dev:api": "nodemon -w api -w .env -w server.js server | pino-pretty -clt -i 'hostname,pid'",
    "lint:api": "eslint .",
    "prettier:api": "prettier -w .",
    "test:api": "jest --roots test --verbose",
    "coverage:api": "jest --roots test --verbose --coverage",
    "watch-test:api": "jest --roots test --verbose --watch",
    "dev:ui": "vite",
    "build:ui": "vite build",
    "lint:ui": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0",
    "preview:ui": "vite preview",
    "dev": "concurrently -n api,ui \"npm:dev:api\" \"npm:dev:ui\""
  }
}
