import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';

class MallModel {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String city;
  final String state;
  final String country;
  final String? description;
  final String? imageUrl;
  final String adminId; // Admin who manages this mall
  final List<String> merchantIds;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? settings; // Mall-specific settings

  MallModel({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.state,
    required this.country,
    this.description,
    this.imageUrl,
    required this.adminId,
    this.merchantIds = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.settings,
  });

  String get fullAddress => '$address, $city, $state, $country';
  int get merchantCount => merchantIds.length;

  factory MallModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MallModel(
      id: doc.id,
      name: data['name'] ?? '',
      address: data['address'] ?? '',
      latitude: (data['latitude'] ?? 0.0).toDouble(),
      longitude: (data['longitude'] ?? 0.0).toDouble(),
      city: data['city'] ?? '',
      state: data['state'] ?? '',
      country: data['country'] ?? '',
      description: data['description'],
      imageUrl: data['imageUrl'],
      adminId: data['adminId'] ?? '',
      merchantIds: data['merchantIds'] != null
          ? List<String>.from(data['merchantIds'])
          : [],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      settings: data['settings'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'state': state,
      'country': country,
      'description': description,
      'imageUrl': imageUrl,
      'adminId': adminId,
      'merchantIds': merchantIds,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'settings': settings,
    };
  }

  MallModel copyWith({
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    String? city,
    String? state,
    String? country,
    String? description,
    String? imageUrl,
    String? adminId,
    List<String>? merchantIds,
    bool? isActive,
    DateTime? updatedAt,
    Map<String, dynamic>? settings,
  }) {
    return MallModel(
      id: id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      adminId: adminId ?? this.adminId,
      merchantIds: merchantIds ?? this.merchantIds,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      settings: settings ?? this.settings,
    );
  }

  // Calculate distance from a given point
  double distanceFrom(double lat, double lng) {
    // Using Haversine formula for distance calculation
    const double earthRadius = 6371000; // Earth's radius in meters

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(lat) *
            math.cos(latitude) *
            math.sin(dLng / 2) *
            math.sin(dLng / 2);

    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}
