enum AdminRole {
  superAdmin,
  mallAdmin,
  systemAdmin,
  securityAdmin,
}

enum AdminStatus {
  active,
  inactive,
  suspended,
  pending,
}

class AdminUser {
  final String id;
  final String name;
  final String email;
  final String phone;
  final AdminRole role;
  final AdminStatus status;
  final List<String> permissions;
  final List<String> assignedMalls;
  final DateTime createdAt;
  final DateTime lastLogin;
  final String profileImage;
  final Map<String, dynamic> metadata;

  AdminUser({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.status,
    required this.permissions,
    required this.assignedMalls,
    required this.createdAt,
    required this.lastLogin,
    this.profileImage = '',
    this.metadata = const {},
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      role: AdminRole.values.firstWhere(
        (e) => e.toString() == 'AdminRole.${json['role']}',
        orElse: () => AdminRole.mallAdmin,
      ),
      status: AdminStatus.values.firstWhere(
        (e) => e.toString() == 'AdminStatus.${json['status']}',
        orElse: () => AdminStatus.pending,
      ),
      permissions: List<String>.from(json['permissions'] ?? []),
      assignedMalls: List<String>.from(json['assignedMalls'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLogin: DateTime.parse(json['lastLogin'] ?? DateTime.now().toIso8601String()),
      profileImage: json['profileImage'] ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'role': role.toString().split('.').last,
      'status': status.toString().split('.').last,
      'permissions': permissions,
      'assignedMalls': assignedMalls,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin.toIso8601String(),
      'profileImage': profileImage,
      'metadata': metadata,
    };
  }

  String get roleDisplayName {
    switch (role) {
      case AdminRole.superAdmin:
        return 'Super Admin';
      case AdminRole.mallAdmin:
        return 'Mall Admin';
      case AdminRole.systemAdmin:
        return 'System Admin';
      case AdminRole.securityAdmin:
        return 'Security Admin';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case AdminStatus.active:
        return 'Active';
      case AdminStatus.inactive:
        return 'Inactive';
      case AdminStatus.suspended:
        return 'Suspended';
      case AdminStatus.pending:
        return 'Pending';
    }
  }

  bool get isActive => status == AdminStatus.active;
}

class AdminActivity {
  final String id;
  final String adminId;
  final String adminName;
  final String action;
  final String description;
  final String module;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;
  final String ipAddress;
  final String userAgent;

  AdminActivity({
    required this.id,
    required this.adminId,
    required this.adminName,
    required this.action,
    required this.description,
    required this.module,
    required this.metadata,
    required this.timestamp,
    required this.ipAddress,
    required this.userAgent,
  });

  factory AdminActivity.fromJson(Map<String, dynamic> json) {
    return AdminActivity(
      id: json['id'] ?? '',
      adminId: json['adminId'] ?? '',
      adminName: json['adminName'] ?? '',
      action: json['action'] ?? '',
      description: json['description'] ?? '',
      module: json['module'] ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      ipAddress: json['ipAddress'] ?? '',
      userAgent: json['userAgent'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'adminId': adminId,
      'adminName': adminName,
      'action': action,
      'description': description,
      'module': module,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
      'ipAddress': ipAddress,
      'userAgent': userAgent,
    };
  }
}