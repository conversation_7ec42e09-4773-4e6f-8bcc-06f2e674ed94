import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/constants/app_constants.dart';
import 'shared/themes/app_theme.dart';
import 'features/merchant/screens/simple_inventory_dashboard.dart';

/// Demo version of the app that bypasses Firebase for testing
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  runApp(const ProviderScope(child: DemoMallManagementApp()));
}

class DemoMallManagementApp extends ConsumerWidget {
  const DemoMallManagementApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      home: const SimpleInventoryDashboard(),
    );
  }
}
