import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget optimized for external barcode scanner devices (USB/Bluetooth)
/// Automatically receives barcode data as keyboard input and validates retail formats
class ExternalBarcodeInput extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final Function(String barcode)? onBarcodeScanned;
  final bool autoSubmit;
  final bool showScanIndicator;
  final VoidCallback? onManualEntry;
  final String? Function(String?)? validator;

  const ExternalBarcodeInput({
    super.key,
    required this.controller,
    this.label = 'Barcode',
    this.hint = 'Scan barcode or enter manually',
    this.onBarcodeScanned,
    this.autoSubmit = false,
    this.showScanIndicator = true,
    this.onManualEntry,
    this.validator,
  });

  @override
  State<ExternalBarcodeInput> createState() => _ExternalBarcodeInputState();
}

class _ExternalBarcodeInputState extends State<ExternalBarcodeInput>
    with TickerProviderStateMixin {
  final FocusNode _focusNode = FocusNode();
  bool _isScanning = false;
  bool _hasValidBarcode = false;
  String? _lastScannedBarcode;
  
  late AnimationController _pulseController;
  late AnimationController _successController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupBarcodeDetection();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _successController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  void _setupBarcodeDetection() {
    widget.controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _onFocusChanged() {
    setState(() {
      _isScanning = _focusNode.hasFocus;
    });
    
    if (_focusNode.hasFocus) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
    }
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    
    // Detect if this looks like a barcode scan (rapid input)
    if (text.isNotEmpty && _isValidRetailBarcode(text)) {
      _handleBarcodeDetected(text);
    }
  }

  void _handleBarcodeDetected(String barcode) {
    if (_lastScannedBarcode == barcode) return;
    
    setState(() {
      _hasValidBarcode = true;
      _lastScannedBarcode = barcode;
    });

    // Success animation
    _successController.forward().then((_) {
      _successController.reverse();
    });

    // Haptic feedback
    HapticFeedback.lightImpact();

    // Notify parent
    widget.onBarcodeScanned?.call(barcode);

    // Auto-submit if enabled
    if (widget.autoSubmit) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _focusNode.unfocus();
      });
    }
  }

  bool _isValidRetailBarcode(String code) {
    final cleanCode = code.trim();
    if (cleanCode.isEmpty || cleanCode.length < 4) return false;

    // EAN-13 (13 digits) - Most common international format
    if (cleanCode.length == 13 && RegExp(r'^\d{13}$').hasMatch(cleanCode)) {
      return _validateEAN13Checksum(cleanCode);
    }

    // UPC-A (12 digits) - US/Canada standard
    if (cleanCode.length == 12 && RegExp(r'^\d{12}$').hasMatch(cleanCode)) {
      return _validateUPCAChecksum(cleanCode);
    }

    // EAN-8 (8 digits) - Compact products
    if (cleanCode.length == 8 && RegExp(r'^\d{8}$').hasMatch(cleanCode)) {
      return _validateEAN8Checksum(cleanCode);
    }

    // UPC-E (6-8 digits) - US compact format
    if ((cleanCode.length == 6 || cleanCode.length == 8) &&
        RegExp(r'^\d{6,8}$').hasMatch(cleanCode)) {
      return true;
    }

    // Code 128 (variable length, alphanumeric)
    if (cleanCode.length >= 6 &&
        cleanCode.length <= 48 &&
        RegExp(r'^[A-Za-z0-9\-\.\s\(\)\/\+%\$]{6,48}$').hasMatch(cleanCode)) {
      return true;
    }

    // General numeric barcode (6-18 digits)
    if (cleanCode.length >= 6 &&
        cleanCode.length <= 18 &&
        RegExp(r'^\d+$').hasMatch(cleanCode)) {
      return true;
    }

    return false;
  }

  bool _validateEAN13Checksum(String code) {
    if (code.length != 13) return false;
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      final digit = int.tryParse(code[i]) ?? 0;
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    final checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == (int.tryParse(code[12]) ?? -1);
  }

  bool _validateUPCAChecksum(String code) {
    if (code.length != 12) return false;
    int sum = 0;
    for (int i = 0; i < 11; i++) {
      final digit = int.tryParse(code[i]) ?? 0;
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    final checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == (int.tryParse(code[11]) ?? -1);
  }

  bool _validateEAN8Checksum(String code) {
    if (code.length != 8) return false;
    int sum = 0;
    for (int i = 0; i < 7; i++) {
      final digit = int.tryParse(code[i]) ?? 0;
      sum += (i % 2 == 0) ? digit * 3 : digit;
    }
    final checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit == (int.tryParse(code[7]) ?? -1);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _successController.dispose();
    _focusNode.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Input field with scanning indicator
        AnimatedBuilder(
          animation: Listenable.merge([_pulseController, _successController]),
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _hasValidBarcode
                      ? Colors.green
                      : _isScanning
                          ? Color.lerp(
                              Colors.blue,
                              Colors.blue.withOpacity(0.3),
                              _pulseController.value,
                            )!
                          : Colors.grey.shade300,
                  width: _hasValidBarcode || _isScanning ? 2 : 1,
                ),
                boxShadow: _isScanning
                    ? [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: _pulseController.value * 2,
                        ),
                      ]
                    : null,
              ),
              child: TextFormField(
                controller: widget.controller,
                focusNode: _focusNode,
                validator: widget.validator,
                decoration: InputDecoration(
                  labelText: widget.label,
                  hintText: widget.hint,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  prefixIcon: Icon(
                    _hasValidBarcode
                        ? Icons.check_circle
                        : _isScanning
                            ? Icons.qr_code_scanner
                            : Icons.qr_code,
                    color: _hasValidBarcode
                        ? Colors.green
                        : _isScanning
                            ? Colors.blue
                            : Colors.grey,
                  ),
                  suffixIcon: widget.onManualEntry != null
                      ? IconButton(
                          onPressed: widget.onManualEntry,
                          icon: const Icon(Icons.keyboard),
                          tooltip: 'Manual Entry',
                        )
                      : null,
                ),
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,
                onFieldSubmitted: (value) {
                  if (_isValidRetailBarcode(value)) {
                    _handleBarcodeDetected(value);
                  }
                },
              ),
            );
          },
        ),

        // Status indicator
        if (widget.showScanIndicator) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                _hasValidBarcode
                    ? Icons.check_circle
                    : _isScanning
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                size: 16,
                color: _hasValidBarcode
                    ? Colors.green
                    : _isScanning
                        ? Colors.blue
                        : Colors.grey,
              ),
              const SizedBox(width: 8),
              Text(
                _hasValidBarcode
                    ? 'Valid barcode detected'
                    : _isScanning
                        ? 'Ready for barcode scanner input'
                        : 'Tap to activate barcode scanning',
                style: TextStyle(
                  fontSize: 12,
                  color: _hasValidBarcode
                      ? Colors.green
                      : _isScanning
                          ? Colors.blue
                          : Colors.grey,
                  fontWeight: _isScanning ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ],

        // Format support info
        if (_isScanning) ...[
          const SizedBox(height: 4),
          Text(
            'Supports: UPC-A, EAN-13, EAN-8, Code 128, and more',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }
}
