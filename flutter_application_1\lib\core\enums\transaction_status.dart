enum TransactionStatus {
  pending('pending', 'Pending'),
  completed('completed', 'Completed'),
  failed('failed', 'Failed'),
  cancelled('cancelled', 'Cancelled'),
  verified('verified', 'Verified');

  const TransactionStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static TransactionStatus fromString(String value) {
    return TransactionStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TransactionStatus.pending,
    );
  }

  bool get isPending => this == TransactionStatus.pending;
  bool get isCompleted => this == TransactionStatus.completed;
  bool get isFailed => this == TransactionStatus.failed;
  bool get isCancelled => this == TransactionStatus.cancelled;
  bool get isVerified => this == TransactionStatus.verified;

  bool get isSuccessful => isCompleted || isVerified;
  bool get canBeVerified => isCompleted;
}
