import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/merchant.dart';

// Admin State
class AdminState {
  final List<Merchant> merchants;
  final List<String> recentActivities;
  final Map<String, dynamic> analytics;
  final bool isLoading;

  AdminState({
    this.merchants = const [],
    this.recentActivities = const [],
    this.analytics = const {},
    this.isLoading = false,
  });

  AdminState copyWith({
    List<Merchant>? merchants,
    List<String>? recentActivities,
    Map<String, dynamic>? analytics,
    bool? isLoading,
  }) {
    return AdminState(
      merchants: merchants ?? this.merchants,
      recentActivities: recentActivities ?? this.recentActivities,
      analytics: analytics ?? this.analytics,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class AdminNotifier extends StateNotifier<AdminState> {
  AdminNotifier() : super(AdminState());

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void addMerchant(Merchant merchant) {
    final newMerchants = [...state.merchants, merchant];
    state = state.copyWith(merchants: newMerchants);
  }

  void updateMerchantStatus(String merchantId, String newStatus) {
    final updatedMerchants = state.merchants.map((m) {
      if (m.id == merchantId) {
        return Merchant(
          id: m.id,
          name: m.name,
          email: m.email,
          phone: m.phone,
          category: m.category,
          address: m.address,
          status: newStatus,
        );
      }
      return m;
    }).toList();

    state = state.copyWith(merchants: updatedMerchants);
  }

  void addActivity(String activity) {
    final newActivities = [activity, ...state.recentActivities];
    state = state.copyWith(recentActivities: newActivities);
  }

  void updateAnalytics(Map<String, dynamic> newAnalytics) {
    state = state.copyWith(analytics: newAnalytics);
  }
}

final adminProvider = StateNotifierProvider<AdminNotifier, AdminState>(
  (ref) => AdminNotifier(),
);
