import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../models/product_model.dart';
import '../../../shared/widgets/animated_card.dart';
import '../../../shared/widgets/enhanced_buttons.dart';

class EnhancedProductCard extends StatefulWidget {
  final ProductModel product;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onUpdateStock;
  final VoidCallback? onTap;

  const EnhancedProductCard({
    super.key,
    required this.product,
    this.onEdit,
    this.onDelete,
    this.onUpdateStock,
    this.onTap,
  });

  @override
  State<EnhancedProductCard> createState() => _EnhancedProductCardState();
}

class _EnhancedProductCardState extends State<EnhancedProductCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  Color _getStockColor() {
    if (widget.product.stockQuantity <= 0) {
      return Colors.red;
    } else if (widget.product.stockQuantity <=
        widget.product.lowStockThreshold) {
      return Colors.orange;
    }
    return Colors.green;
  }

  IconData _getStockIcon() {
    if (widget.product.stockQuantity <= 0) {
      return Icons.error;
    } else if (widget.product.stockQuantity <=
        widget.product.lowStockThreshold) {
      return Icons.warning;
    }
    return Icons.check_circle;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedCard(
      onTap: widget.onTap,
      child: Column(
        children: [
          // Main content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header row with image and basic info
                Row(
                  children: [
                    // Product image with enhanced styling
                    Hero(
                      tag: 'product_image_${widget.product.id}',
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: widget.product.imageUrl != null
                              ? CachedNetworkImage(
                                  imageUrl: widget.product.imageUrl!,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color: Colors.grey[200],
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                    color: Colors.grey[200],
                                    child: const Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey,
                                      size: 32,
                                    ),
                                  ),
                                )
                              : Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.shopping_bag,
                                    color: Colors.grey,
                                    size: 32,
                                  ),
                                ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Product info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Product name with enhanced typography
                          Text(
                            widget.product.name,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: 4),

                          // Category and barcode
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  widget.product.category,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'SKU: ${widget.product.barcode}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          // Price and stock status
                          Row(
                            children: [
                              Text(
                                '₦${widget.product.pricePerUnit.toStringAsFixed(2)}',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: _getStockColor().withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      _getStockIcon(),
                                      size: 16,
                                      color: _getStockColor(),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${widget.product.stockQuantity}',
                                      style: TextStyle(
                                        color: _getStockColor(),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Expand/collapse button
                    IconButtonEnhanced(
                      onPressed: _toggleExpanded,
                      icon: _isExpanded ? Icons.expand_less : Icons.expand_more,
                      tooltip: _isExpanded ? 'Show less' : 'Show more',
                    ),
                  ],
                ),

                // Expandable content
                SizeTransition(
                  sizeFactor: _expandAnimation,
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      // Detailed information
                      if (widget.product.description.isNotEmpty) ...[
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Description',
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            widget.product.description,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Stock details
                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoCard(
                              'Stock Quantity',
                              '${widget.product.stockQuantity} ${widget.product.unitType}s',
                              Icons.inventory,
                              _getStockColor(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildInfoCard(
                              'Total Value',
                              '₦${widget.product.totalStockValue.toStringAsFixed(2)}',
                              Icons.attach_money,
                              Colors.green,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoCard(
                              'Per Carton',
                              '₦${widget.product.pricePerCarton.toStringAsFixed(2)}',
                              Icons.inventory_2,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildInfoCard(
                              'Units/Carton',
                              '${widget.product.unitsPerCarton}',
                              Icons.apps,
                              Colors.purple,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: EnhancedButton(
                              onPressed: widget.onUpdateStock,
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              icon: Icons.add_circle,
                              child: const Text('Update Stock'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: EnhancedButton(
                              onPressed: widget.onEdit,
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                              icon: Icons.edit,
                              child: const Text('Edit'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          EnhancedButton(
                            onPressed: widget.onDelete,
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            icon: Icons.delete,
                            child: const Text('Delete'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
