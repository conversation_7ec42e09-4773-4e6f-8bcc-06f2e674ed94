enum ReportType {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
}

enum ReportStatus {
  generating,
  completed,
  failed,
  scheduled,
}

class FinancialReport {
  final String id;
  final String title;
  final ReportType type;
  final ReportStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime generatedAt;
  final String generatedBy;
  final Map<String, dynamic> data;
  final List<String> mallIds;
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final String filePath;

  FinancialReport({
    required this.id,
    required this.title,
    required this.type,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.generatedAt,
    required this.generatedBy,
    required this.data,
    required this.mallIds,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    this.filePath = '',
  });

  factory FinancialReport.fromJson(Map<String, dynamic> json) {
    return FinancialReport(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      type: ReportType.values.firstWhere(
        (e) => e.toString() == 'ReportType.${json['type']}',
        orElse: () => ReportType.monthly,
      ),
      status: ReportStatus.values.firstWhere(
        (e) => e.toString() == 'ReportStatus.${json['status']}',
        orElse: () => ReportStatus.generating,
      ),
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toIso8601String()),
      generatedAt: DateTime.parse(json['generatedAt'] ?? DateTime.now().toIso8601String()),
      generatedBy: json['generatedBy'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      mallIds: List<String>.from(json['mallIds'] ?? []),
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      totalExpenses: (json['totalExpenses'] ?? 0).toDouble(),
      netProfit: (json['netProfit'] ?? 0).toDouble(),
      filePath: json['filePath'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'generatedAt': generatedAt.toIso8601String(),
      'generatedBy': generatedBy,
      'data': data,
      'mallIds': mallIds,
      'totalRevenue': totalRevenue,
      'totalExpenses': totalExpenses,
      'netProfit': netProfit,
      'filePath': filePath,
    };
  }

  String get typeDisplayName {
    switch (type) {
      case ReportType.daily:
        return 'Daily Report';
      case ReportType.weekly:
        return 'Weekly Report';
      case ReportType.monthly:
        return 'Monthly Report';
      case ReportType.quarterly:
        return 'Quarterly Report';
      case ReportType.yearly:
        return 'Yearly Report';
      case ReportType.custom:
        return 'Custom Report';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case ReportStatus.generating:
        return 'Generating';
      case ReportStatus.completed:
        return 'Completed';
      case ReportStatus.failed:
        return 'Failed';
      case ReportStatus.scheduled:
        return 'Scheduled';
    }
  }

  double get profitMargin {
    if (totalRevenue == 0) return 0;
    return (netProfit / totalRevenue) * 100;
  }
}

class FinancialKPI {
  final String name;
  final double value;
  final double previousValue;
  final String unit;
  final bool isPositive;
  final DateTime calculatedAt;

  FinancialKPI({
    required this.name,
    required this.value,
    required this.previousValue,
    required this.unit,
    required this.isPositive,
    required this.calculatedAt,
  });

  factory FinancialKPI.fromJson(Map<String, dynamic> json) {
    return FinancialKPI(
      name: json['name'] ?? '',
      value: (json['value'] ?? 0).toDouble(),
      previousValue: (json['previousValue'] ?? 0).toDouble(),
      unit: json['unit'] ?? '',
      isPositive: json['isPositive'] ?? true,
      calculatedAt: DateTime.parse(json['calculatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  double get changePercentage {
    if (previousValue == 0) return 0;
    return ((value - previousValue) / previousValue) * 100;
  }

  String get formattedValue {
    if (unit == '₦') {
      return '₦${value.toStringAsFixed(2)}';
    } else if (unit == '%') {
      return '${value.toStringAsFixed(1)}%';
    }
    return '${value.toStringAsFixed(0)} $unit';
  }
}

class ExpenseCategory {
  final String id;
  final String name;
  final double amount;
  final double budgetAmount;
  final String description;
  final DateTime period;

  ExpenseCategory({
    required this.id,
    required this.name,
    required this.amount,
    required this.budgetAmount,
    required this.description,
    required this.period,
  });

  factory ExpenseCategory.fromJson(Map<String, dynamic> json) {
    return ExpenseCategory(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      budgetAmount: (json['budgetAmount'] ?? 0).toDouble(),
      description: json['description'] ?? '',
      period: DateTime.parse(json['period'] ?? DateTime.now().toIso8601String()),
    );
  }

  double get budgetUtilization {
    if (budgetAmount == 0) return 0;
    return (amount / budgetAmount) * 100;
  }

  bool get isOverBudget => amount > budgetAmount;
}