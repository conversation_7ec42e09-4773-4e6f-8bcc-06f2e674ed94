import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

import '../models/product_model.dart';
import '../services/firebase_service.dart';
import 'auth_provider.dart';
import 'demo_product_provider.dart';

// Product repository
class ProductRepository {
  final FirebaseService _firebase = FirebaseService.instance;

  // Get products for a specific merchant and mall
  Stream<List<ProductModel>> getProducts(String merchantId, String mallId) {
    return _firebase.getProductsQuery(merchantId, mallId).snapshots().map(
          (snapshot) => snapshot.docs
              .map((doc) => ProductModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Get product by barcode
  Future<ProductModel?> getProductByBarcode(
      String barcode, String mallId) async {
    try {
      final query = await _firebase.productsCollection
          .where('barcode', isEqualTo: barcode)
          .where('mallId', isEqualTo: mallId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return ProductModel.fromFirestore(query.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product: $e');
    }
  }

  // Add new product
  Future<void> addProduct(ProductModel product) async {
    try {
      await _firebase.productsCollection
          .doc(product.id)
          .set(product.toFirestore());
    } catch (e) {
      throw Exception('Failed to add product: $e');
    }
  }

  // Update product
  Future<void> updateProduct(ProductModel product) async {
    try {
      await _firebase.productsCollection
          .doc(product.id)
          .update(product.toFirestore());
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product
  Future<void> deleteProduct(String productId) async {
    try {
      await _firebase.productsCollection.doc(productId).update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  // Update stock quantity
  Future<void> updateStock(String productId, int newQuantity) async {
    try {
      await _firebase.productsCollection.doc(productId).update({
        'stockQuantity': newQuantity,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update stock: $e');
    }
  }

  // Get low stock products
  Stream<List<ProductModel>> getLowStockProducts(
      String merchantId, String mallId) {
    return _firebase.productsCollection
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProductModel.fromFirestore(doc))
            .where((product) => product.isLowStock)
            .toList());
  }

  // Search products
  Stream<List<ProductModel>> searchProducts(
    String merchantId,
    String mallId,
    String searchTerm,
  ) {
    return _firebase.productsCollection
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProductModel.fromFirestore(doc))
            .where((product) =>
                product.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
                product.barcode.contains(searchTerm) ||
                product.category
                    .toLowerCase()
                    .contains(searchTerm.toLowerCase()))
            .toList());
  }
}

// Product repository provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  return ProductRepository();
});

// Products stream provider
final productsProvider = StreamProvider.family<List<ProductModel>,
    ({String merchantId, String mallId})>((ref, params) {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getProducts(params.merchantId, params.mallId);
});

// Current user's products provider (simplified for demo)
final currentUserProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  // For demo, use the demo product provider directly
  final demoProvider = ref.watch(demoProductRepositoryProvider);
  return demoProvider.getProducts('demo-merchant', 'demo-mall');
});

// Low stock products provider
final lowStockProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(productRepositoryProvider);

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return repository.getLowStockProducts(userData!.id, userData.mallId!);
      }
      return Stream.value(<ProductModel>[]);
    },
    loading: () => Stream.value(<ProductModel>[]),
    error: (_, __) => Stream.value(<ProductModel>[]),
  );
});

// Product controller
class ProductController extends StateNotifier<AsyncValue<void>> {
  ProductController(this._repository) : super(const AsyncValue.data(null));

  final ProductRepository _repository;

  Future<void> addProduct({
    required String barcode,
    required String name,
    required String description,
    required String merchantId,
    required String mallId,
    required String unitType,
    required int unitsPerCarton,
    required double pricePerCarton,
    required int stockQuantity,
    required String createdBy,
    int lowStockThreshold = 5,
    String? imageUrl,
    String category = 'General',
  }) async {
    state = const AsyncValue.loading();

    try {
      final product = ProductModel(
        id: const Uuid().v4(),
        barcode: barcode,
        name: name,
        description: description,
        merchantId: merchantId,
        mallId: mallId,
        unitType: unitType,
        unitsPerCarton: unitsPerCarton,
        pricePerCarton: pricePerCarton,
        stockQuantity: stockQuantity,
        lowStockThreshold: lowStockThreshold,
        imageUrl: imageUrl,
        category: category,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: createdBy,
      );

      await _repository.addProduct(product);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateProduct(ProductModel product) async {
    state = const AsyncValue.loading();

    try {
      await _repository.updateProduct(product);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteProduct(String productId) async {
    state = const AsyncValue.loading();

    try {
      await _repository.deleteProduct(productId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateStock(String productId, int newQuantity) async {
    state = const AsyncValue.loading();

    try {
      await _repository.updateStock(productId, newQuantity);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<ProductModel?> getProductByBarcode(
      String barcode, String mallId) async {
    try {
      return await _repository.getProductByBarcode(barcode, mallId);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }
}

// Product controller provider
final productControllerProvider =
    StateNotifierProvider<ProductController, AsyncValue<void>>((ref) {
  final repository = ref.watch(productRepositoryProvider);
  return ProductController(repository);
});

// Search products provider
final searchProductsProvider =
    StreamProvider.family<List<ProductModel>, String>((ref, searchTerm) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(productRepositoryProvider);

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true &&
          userData?.mallId != null &&
          searchTerm.isNotEmpty) {
        return repository.searchProducts(
            userData!.id, userData.mallId!, searchTerm);
      }
      return Stream.value(<ProductModel>[]);
    },
    loading: () => Stream.value(<ProductModel>[]),
    error: (_, __) => Stream.value(<ProductModel>[]),
  );
});
