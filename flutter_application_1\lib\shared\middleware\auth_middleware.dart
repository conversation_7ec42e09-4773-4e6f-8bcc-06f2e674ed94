import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/enums/user_role.dart';
import '../../providers/auth_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/user_model.dart';

/// Authentication middleware for handling route access control
class AuthMiddleware {
  /// Check if user is authenticated
  static Future<bool> isAuthenticated(WidgetRef ref) async {
    try {
      final authState = ref.read(authControllerProvider);
      final demoUser = ref.read(demoUserProvider);

      final currentUser = authState.when(
            data: (user) => user,
            loading: () => null,
            error: (_, __) => null,
          ) ??
          demoUser;

      return currentUser != null;
    } catch (e) {
      return false;
    }
  }

  /// Get current user
  static UserModel? getCurrentUser(WidgetRef ref) {
    try {
      final authState = ref.read(authControllerProvider);
      final demoUser = ref.read(demoUserProvider);

      return authState.when(
            data: (user) => user,
            loading: () => null,
            error: (_, __) => null,
          ) ??
          demoUser;
    } catch (e) {
      return null;
    }
  }

  /// Check if user has specific role
  static bool hasRole(WidgetRef ref, UserRole requiredRole) {
    final user = getCurrentUser(ref);
    return user?.role == requiredRole;
  }

  /// Check if user has any of the specified roles
  static bool hasAnyRole(WidgetRef ref, List<UserRole> roles) {
    final user = getCurrentUser(ref);
    return user != null && roles.contains(user.role);
  }

  /// Check if user can access web interface
  static bool canAccessWeb(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.hasWebAccess ?? false;
  }

  /// Check if user can access mobile interface
  static bool canAccessMobile(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.hasMobileAccess ?? false;
  }

  /// Check if user can manage products
  static bool canManageProducts(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.canManageProducts ?? false;
  }

  /// Check if user can view analytics
  static bool canViewAnalytics(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.canViewAnalytics ?? false;
  }

  /// Check if user can manage merchants
  static bool canManageMerchants(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.canManageMerchants ?? false;
  }

  /// Check if user can manage admins
  static bool canManageAdmins(WidgetRef ref) {
    final user = getCurrentUser(ref);
    return user?.role.canManageAdmins ?? false;
  }

  /// Get appropriate dashboard route for user role
  static String getDashboardRoute(UserRole? role) {
    if (role == null) return '/login';

    switch (role) {
      case UserRole.customer:
        return '/customer';
      case UserRole.salesRep:
        return '/sales-rep';
      case UserRole.merchant:
        return '/merchant';
      case UserRole.admin:
        return '/admin';
      case UserRole.boss:
        return '/boss';
    }
  }

  /// Redirect user to appropriate dashboard
  static void redirectToDashboard(BuildContext context, WidgetRef ref) {
    final user = getCurrentUser(ref);
    final route = getDashboardRoute(user?.role);
    context.go(route);
  }

  /// Sign out user and redirect to login
  static Future<void> signOut(BuildContext context, WidgetRef ref) async {
    try {
      // Clear demo user
      ref.read(demoUserProvider.notifier).state = null;

      // Sign out from Firebase
      await ref.read(authControllerProvider.notifier).signOut();

      // Redirect to login
      if (context.mounted) {
        context.go('/login');
      }
    } catch (e) {
      // Even if sign out fails, redirect to login
      if (context.mounted) {
        context.go('/login');
      }
    }
  }

  /// Check if route requires specific permissions
  static bool routeRequiresPermission(String path, String permission) {
    final routePermissions = {
      '/merchant/inventory': ['manage_products'],
      '/admin': ['manage_merchants'],
      '/boss': ['manage_admins'],
    };

    final requiredPermissions = routePermissions[path];
    return requiredPermissions?.contains(permission) ?? false;
  }

  /// Validate user session
  static Future<bool> validateSession(WidgetRef ref) async {
    try {
      final user = getCurrentUser(ref);
      if (user == null) return false;

      // For demo users, always valid
      if (user.email.contains('demo.com')) {
        return true;
      }

      // For Firebase users, check if still authenticated
      final authState = ref.read(authControllerProvider);
      return authState.when(
        data: (userData) => userData != null,
        loading: () => false,
        error: (_, __) => false,
      );
    } catch (e) {
      return false;
    }
  }

  /// Get user permissions based on role
  static List<String> getUserPermissions(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return ['view_products', 'make_purchases', 'view_history'];
      case UserRole.salesRep:
        return ['verify_receipts', 'view_sales_reports', 'manage_customers'];
      case UserRole.merchant:
        return ['manage_products', 'view_analytics', 'manage_inventory'];
      case UserRole.admin:
        return [
          'manage_merchants',
          'view_mall_analytics',
          'manage_users',
          'view_audit_trails'
        ];
      case UserRole.boss:
        return [
          'manage_admins',
          'view_all_analytics',
          'manage_all_users',
          'view_financial_reports',
          'manage_system_settings'
        ];
    }
  }

  /// Check if user has specific permission
  static bool hasPermission(WidgetRef ref, String permission) {
    final user = getCurrentUser(ref);
    if (user == null) return false;

    final userPermissions = getUserPermissions(user.role);
    return userPermissions.contains(permission);
  }
}

/// Provider for authentication middleware
final authMiddlewareProvider = Provider<AuthMiddleware>((ref) {
  return AuthMiddleware();
});

/// Provider to check if user is authenticated
final isAuthenticatedProvider = FutureProvider<bool>((ref) async {
  try {
    final authState = ref.read(authControllerProvider);
    final demoUser = ref.read(demoUserProvider);

    final currentUser = authState.when(
          data: (user) => user,
          loading: () => null,
          error: (_, __) => null,
        ) ??
        demoUser;

    return currentUser != null;
  } catch (e) {
    return false;
  }
});

/// Provider to get current user
final currentUserMiddlewareProvider = Provider<UserModel?>((ref) {
  try {
    final authState = ref.read(authControllerProvider);
    final demoUser = ref.read(demoUserProvider);

    return authState.when(
          data: (user) => user,
          loading: () => null,
          error: (_, __) => null,
        ) ??
        demoUser;
  } catch (e) {
    return null;
  }
});

/// Provider to check web access
final canAccessWebProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserMiddlewareProvider);
  return user?.role.hasWebAccess ?? false;
});

/// Provider to check mobile access
final canAccessMobileProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserMiddlewareProvider);
  return user?.role.hasMobileAccess ?? false;
});
