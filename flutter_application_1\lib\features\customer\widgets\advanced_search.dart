import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/customer_theme.dart';
import 'premium_ui_components.dart';

/// Advanced Search Component - Inspired by Amazon, Shopify, Google
/// Features smart suggestions, filters, voice search, and real-time results

class AdvancedSearchScreen extends ConsumerStatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  ConsumerState<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends ConsumerState<AdvancedSearchScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  bool _isSearching = false;
  List<String> _suggestions = [];
  List<String> _recentSearches = [];
  Map<String, bool> _filters = {
    'In Stock': false,
    'On Sale': false,
    'New Arrivals': false,
    'Popular': false,
  };

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _fadeController = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: CustomerTheme.emphasizedCurve,
    ));
    
    _loadRecentSearches();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _loadRecentSearches() {
    // TODO: Load from local storage
    _recentSearches = [
      'iPhone 15',
      'Samsung Galaxy',
      'Nike Air Max',
      'MacBook Pro',
      'AirPods Pro',
    ];
  }

  void _onSearchChanged(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
    });
    
    if (query.isNotEmpty) {
      _generateSuggestions(query);
    } else {
      setState(() {
        _suggestions.clear();
      });
    }
  }

  void _generateSuggestions(String query) {
    // TODO: Implement real-time search suggestions from API
    final mockSuggestions = [
      '$query Pro',
      '$query Max',
      '$query Plus',
      '$query Mini',
      '$query Air',
    ];
    
    setState(() {
      _suggestions = mockSuggestions;
    });
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;
    
    HapticFeedback.lightImpact();
    
    // Add to recent searches
    setState(() {
      _recentSearches.removeWhere((item) => item.toLowerCase() == query.toLowerCase());
      _recentSearches.insert(0, query);
      if (_recentSearches.length > 10) {
        _recentSearches = _recentSearches.take(10).toList();
      }
    });
    
    // TODO: Implement actual search with filters
    Navigator.of(context).pop(query);
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
      _suggestions.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CustomerTheme.backgroundSecondary,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SafeArea(
          child: Column(
            children: [
              _buildSearchHeader(),
              Expanded(
                child: _isSearching ? _buildSuggestions() : _buildSearchHome(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(CustomerTheme.spacing16),
      decoration: BoxDecoration(
        color: CustomerTheme.surfaceElevated,
        boxShadow: CustomerTheme.shadowSM,
      ),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back),
                color: CustomerTheme.textPrimary,
              ),
              Expanded(
                child: PremiumSearchBar(
                  controller: _searchController,
                  hintText: 'Search products, brands, categories...',
                  onChanged: _onSearchChanged,
                  onSubmitted: _performSearch,
                  showFilter: true,
                  onFilterTap: _showFilters,
                ),
              ),
              if (_isSearching) ...[
                const SizedBox(width: CustomerTheme.spacing8),
                IconButton(
                  onPressed: _clearSearch,
                  icon: const Icon(Icons.clear),
                  color: CustomerTheme.textSecondary,
                ),
              ],
            ],
          ),
          if (_hasActiveFilters()) ...[
            const SizedBox(height: CustomerTheme.spacing12),
            _buildActiveFilters(),
          ],
        ],
      ),
    );
  }

  Widget _buildSuggestions() {
    return ListView.builder(
      padding: const EdgeInsets.all(CustomerTheme.spacing16),
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return PremiumCard(
          margin: const EdgeInsets.only(bottom: CustomerTheme.spacing8),
          padding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacing16,
            vertical: CustomerTheme.spacing12,
          ),
          onTap: () => _performSearch(suggestion),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: CustomerTheme.textTertiary,
                size: 20,
              ),
              const SizedBox(width: CustomerTheme.spacing12),
              Expanded(
                child: Text(
                  suggestion,
                  style: CustomerTheme.bodyMedium.copyWith(
                    color: CustomerTheme.textPrimary,
                  ),
                ),
              ),
              Icon(
                Icons.north_west,
                color: CustomerTheme.textTertiary,
                size: 16,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchHome() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(CustomerTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_recentSearches.isNotEmpty) ...[
            _buildSectionHeader('Recent Searches', Icons.history),
            const SizedBox(height: CustomerTheme.spacing12),
            _buildRecentSearches(),
            const SizedBox(height: CustomerTheme.spacing24),
          ],
          _buildSectionHeader('Quick Filters', Icons.tune),
          const SizedBox(height: CustomerTheme.spacing12),
          _buildQuickFilters(),
          const SizedBox(height: CustomerTheme.spacing24),
          _buildSectionHeader('Popular Categories', Icons.trending_up),
          const SizedBox(height: CustomerTheme.spacing12),
          _buildPopularCategories(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: CustomerTheme.primaryTeal,
          size: 20,
        ),
        const SizedBox(width: CustomerTheme.spacing8),
        Text(
          title,
          style: CustomerTheme.headingSmall.copyWith(
            color: CustomerTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentSearches() {
    return Wrap(
      spacing: CustomerTheme.spacing8,
      runSpacing: CustomerTheme.spacing8,
      children: _recentSearches.map((search) {
        return PremiumCard(
          padding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacing12,
            vertical: CustomerTheme.spacing8,
          ),
          backgroundColor: CustomerTheme.surfaceContainer,
          onTap: () => _performSearch(search),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.history,
                color: CustomerTheme.textTertiary,
                size: 16,
              ),
              const SizedBox(width: CustomerTheme.spacing8),
              Text(
                search,
                style: CustomerTheme.bodyMedium.copyWith(
                  color: CustomerTheme.textPrimary,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuickFilters() {
    return Wrap(
      spacing: CustomerTheme.spacing8,
      runSpacing: CustomerTheme.spacing8,
      children: _filters.entries.map((entry) {
        final isActive = entry.value;
        return PremiumCard(
          padding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacing16,
            vertical: CustomerTheme.spacing12,
          ),
          backgroundColor: isActive 
              ? CustomerTheme.primaryTealSurface 
              : CustomerTheme.surfaceContainer,
          onTap: () {
            setState(() {
              _filters[entry.key] = !entry.value;
            });
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isActive ? Icons.check_circle : Icons.circle_outlined,
                color: isActive 
                    ? CustomerTheme.primaryTeal 
                    : CustomerTheme.textTertiary,
                size: 16,
              ),
              const SizedBox(width: CustomerTheme.spacing8),
              Text(
                entry.key,
                style: CustomerTheme.bodyMedium.copyWith(
                  color: isActive 
                      ? CustomerTheme.primaryTeal 
                      : CustomerTheme.textPrimary,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPopularCategories() {
    final categories = [
      {'name': 'Electronics', 'icon': Icons.devices, 'color': CustomerTheme.accentBlue},
      {'name': 'Fashion', 'icon': Icons.checkroom, 'color': CustomerTheme.accentPurple},
      {'name': 'Home & Garden', 'icon': Icons.home, 'color': CustomerTheme.accentOrange},
      {'name': 'Sports', 'icon': Icons.sports_basketball, 'color': CustomerTheme.successGreen},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: CustomerTheme.spacing12,
        mainAxisSpacing: CustomerTheme.spacing12,
        childAspectRatio: 1.5,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return PremiumCard(
          gradient: LinearGradient(
            colors: [
              (category['color'] as Color).withOpacity(0.1),
              (category['color'] as Color).withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          onTap: () => _performSearch(category['name'] as String),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                category['icon'] as IconData,
                color: category['color'] as Color,
                size: 32,
              ),
              const SizedBox(height: CustomerTheme.spacing8),
              Text(
                category['name'] as String,
                style: CustomerTheme.bodyMedium.copyWith(
                  color: CustomerTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  bool _hasActiveFilters() {
    return _filters.values.any((isActive) => isActive);
  }

  Widget _buildActiveFilters() {
    final activeFilters = _filters.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    return Wrap(
      spacing: CustomerTheme.spacing8,
      children: activeFilters.map((filter) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacing8,
            vertical: CustomerTheme.spacing4,
          ),
          decoration: BoxDecoration(
            color: CustomerTheme.primaryTeal,
            borderRadius: BorderRadius.circular(CustomerTheme.spacing12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                filter,
                style: CustomerTheme.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: CustomerTheme.spacing4),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _filters[filter] = false;
                  });
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _showFilters() {
    // TODO: Implement advanced filter modal
    HapticFeedback.lightImpact();
  }
}
