import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/customer_theme.dart';

/// Professional Navigation System - Inspired by Apple Pay, Google Pay, WeChat
/// Features smooth animations, haptic feedback, and industry-standard design

class ProfessionalBottomNavigation extends ConsumerStatefulWidget {
  final List<NavigationItem> items;
  final Function(int) onTap;
  final int currentIndex;

  const ProfessionalBottomNavigation({
    super.key,
    required this.items,
    required this.onTap,
    required this.currentIndex,
  });

  @override
  ConsumerState<ProfessionalBottomNavigation> createState() =>
      _ProfessionalBottomNavigationState();
}

class _ProfessionalBottomNavigationState
    extends ConsumerState<ProfessionalBottomNavigation>
    with TickerProviderStateMixin {
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: CustomerTheme.mediumAnimation,
        vsync: this,
      ),
    );

    _scaleAnimations = _animationControllers
        .map((controller) => Tween<double>(
              begin: 1.0,
              end: 1.2,
            ).animate(CurvedAnimation(
              parent: controller,
              curve: CustomerTheme.emphasizedCurve,
            )))
        .toList();

    _slideAnimations = _animationControllers
        .map((controller) => Tween<double>(
              begin: 0.0,
              end: -4.0,
            ).animate(CurvedAnimation(
              parent: controller,
              curve: CustomerTheme.emphasizedCurve,
            )))
        .toList();

    // Animate current item
    if (widget.currentIndex < _animationControllers.length) {
      _animationControllers[widget.currentIndex].forward();
    }
  }

  @override
  void didUpdateWidget(ProfessionalBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      // Reset previous animation
      if (oldWidget.currentIndex < _animationControllers.length) {
        _animationControllers[oldWidget.currentIndex].reverse();
      }
      // Start new animation
      if (widget.currentIndex < _animationControllers.length) {
        _animationControllers[widget.currentIndex].forward();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: CustomerTheme.largeTouchTarget + CustomerTheme.spacing16 * 2,
      decoration: BoxDecoration(
        color: CustomerTheme.surfaceElevated,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: widget.items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = index == widget.currentIndex;

            return Expanded(
              child: _buildNavigationItem(item, index, isSelected),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildNavigationItem(NavigationItem item, int index, bool isSelected) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimations[index],
        _slideAnimations[index],
      ]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimations[index].value),
          child: Transform.scale(
            scale: _scaleAnimations[index].value,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _onItemTap(index),
                borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
                splashColor: item.color.withOpacity(0.1),
                highlightColor: item.color.withOpacity(0.05),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: CustomerTheme.spacing8,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildIcon(item, isSelected),
                      const SizedBox(height: CustomerTheme.spacing4),
                      _buildLabel(item, isSelected),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildIcon(NavigationItem item, bool isSelected) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Background circle for selected state
        AnimatedContainer(
          duration: CustomerTheme.mediumAnimation,
          curve: CustomerTheme.emphasizedCurve,
          width: isSelected ? 32 : 0,
          height: isSelected ? 32 : 0,
          decoration: BoxDecoration(
            color: item.color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
        ),
        // Icon with badge
        Stack(
          clipBehavior: Clip.none,
          children: [
            AnimatedContainer(
              duration: CustomerTheme.mediumAnimation,
              curve: CustomerTheme.emphasizedCurve,
              child: Icon(
                isSelected ? item.selectedIcon ?? item.icon : item.icon,
                color: isSelected ? item.color : CustomerTheme.textTertiary,
                size: 24,
              ),
            ),
            if (item.badgeCount != null && item.badgeCount! > 0)
              Positioned(
                right: -6,
                top: -6,
                child: _buildBadge(item.badgeCount!),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildLabel(NavigationItem item, bool isSelected) {
    return AnimatedDefaultTextStyle(
      duration: CustomerTheme.mediumAnimation,
      curve: CustomerTheme.emphasizedCurve,
      style: CustomerTheme.labelMedium.copyWith(
        color: isSelected ? item.color : CustomerTheme.textTertiary,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      child: Text(item.label),
    );
  }

  Widget _buildBadge(int count) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: CustomerTheme.spacing4,
        vertical: CustomerTheme.spacing2,
      ),
      decoration: BoxDecoration(
        color: CustomerTheme.errorRed,
        borderRadius: BorderRadius.circular(10),
        boxShadow: CustomerTheme.shadowXS,
      ),
      constraints: const BoxConstraints(
        minWidth: 16,
        minHeight: 16,
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: CustomerTheme.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 10,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _onItemTap(int index) {
    HapticFeedback.lightImpact();
    widget.onTap(index);
  }
}

/// Navigation Item Model
class NavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final Color color;
  final int? badgeCount;

  const NavigationItem({
    required this.icon,
    this.selectedIcon,
    required this.label,
    required this.color,
    this.badgeCount,
  });
}

/// Professional App Bar - Inspired by Stripe/Square
class ProfessionalAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final bool centerTitle;
  final double elevation;

  const ProfessionalAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showBackButton = false,
    this.onBackPressed,
    this.backgroundColor,
    this.centerTitle = true,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: CustomerTheme.headingSmall.copyWith(
          color: CustomerTheme.textPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? CustomerTheme.surfaceElevated,
      elevation: elevation,
      leading: leading ??
          (showBackButton
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  color: CustomerTheme.textPrimary,
                )
              : null),
      actions: actions,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Progress Indicator Component - Inspired by Uber/Airbnb
class ProfessionalProgressIndicator extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepLabels;
  final Color? activeColor;
  final Color? inactiveColor;

  const ProfessionalProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.stepLabels = const [],
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<ProfessionalProgressIndicator> createState() =>
      _ProfessionalProgressIndicatorState();
}

class _ProfessionalProgressIndicatorState
    extends State<ProfessionalProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.currentStep / widget.totalSteps,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: CustomerTheme.emphasizedCurve,
    ));
    _animationController.forward();
  }

  @override
  void didUpdateWidget(ProfessionalProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.currentStep / widget.totalSteps,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: CustomerTheme.emphasizedCurve,
      ));
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor:
                  widget.inactiveColor ?? CustomerTheme.borderSecondary,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.activeColor ?? CustomerTheme.primaryTeal,
              ),
              minHeight: 4,
            );
          },
        ),
        if (widget.stepLabels.isNotEmpty) ...[
          const SizedBox(height: CustomerTheme.spacing8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: widget.stepLabels.asMap().entries.map((entry) {
              final index = entry.key;
              final label = entry.value;
              final isActive = index <= widget.currentStep;

              return Text(
                label,
                style: CustomerTheme.bodySmall.copyWith(
                  color: isActive
                      ? CustomerTheme.textPrimary
                      : CustomerTheme.textTertiary,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}
