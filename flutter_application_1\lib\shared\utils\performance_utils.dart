import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance Utilities - Fortune 500 Level Optimization
/// Ensures 60fps animations, efficient memory usage, and smooth user experience

class PerformanceUtils {
  static const Duration _debounceDelay = Duration(milliseconds: 300);
  static const Duration _throttleDelay = Duration(milliseconds: 100);
  
  /// Debounce function calls to prevent excessive API calls
  static void debounce(String key, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(_debounceDelay, callback);
  }
  
  /// Throttle function calls for smooth scrolling and animations
  static void throttle(String key, VoidCallback callback) {
    if (_throttleTimers[key]?.isActive ?? false) return;
    
    callback();
    _throttleTimers[key] = Timer(_throttleDelay, () {});
  }
  
  /// Optimize image loading with proper caching
  static ImageProvider optimizedImageProvider(String imageUrl) {
    return NetworkImage(
      imageUrl,
      headers: {
        'Cache-Control': 'max-age=3600',
      },
    );
  }
  
  /// Pre-cache critical images for better performance
  static Future<void> precacheImages(BuildContext context, List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        await precacheImage(optimizedImageProvider(url), context);
      } catch (e) {
        if (kDebugMode) {
          print('Failed to precache image: $url - $e');
        }
      }
    }
  }
  
  /// Optimize list performance with proper item extent
  static Widget optimizedListView({
    required List<Widget> children,
    double? itemExtent,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView.builder(
      itemCount: children.length,
      itemExtent: itemExtent,
      physics: physics ?? const BouncingScrollPhysics(),
      padding: padding,
      itemBuilder: (context, index) => children[index],
    );
  }
  
  /// Memory-efficient grid view
  static Widget optimizedGridView({
    required List<Widget> children,
    required SliverGridDelegate gridDelegate,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
  }) {
    return GridView.builder(
      itemCount: children.length,
      gridDelegate: gridDelegate,
      physics: physics ?? const BouncingScrollPhysics(),
      padding: padding,
      itemBuilder: (context, index) => children[index],
    );
  }
  
  /// Optimize animations for 60fps performance
  static AnimationController createOptimizedController({
    required Duration duration,
    required TickerProvider vsync,
    double? value,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
      value: value,
    );
  }
  
  /// Smooth haptic feedback with performance consideration
  static void performHapticFeedback(HapticFeedbackType type) {
    if (!kIsWeb) {
      switch (type) {
        case HapticFeedbackType.light:
          HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          HapticFeedback.selectionClick();
          break;
      }
    }
  }
  
  /// Optimize text rendering for better performance
  static Text optimizedText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      textAlign: textAlign,
      softWrap: maxLines != 1,
    );
  }
  
  /// Memory-efficient image widget
  static Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ?? 
            SizedBox(
              width: width,
              height: height,
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[200],
              child: const Icon(Icons.error_outline),
            );
      },
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }
  
  /// Efficient scroll controller with memory management
  static ScrollController createOptimizedScrollController({
    double initialScrollOffset = 0.0,
    bool keepScrollOffset = true,
  }) {
    return ScrollController(
      initialScrollOffset: initialScrollOffset,
      keepScrollOffset: keepScrollOffset,
    );
  }
  
  /// Performance monitoring for debug builds
  static void logPerformance(String operation, Duration duration) {
    if (kDebugMode) {
      final ms = duration.inMilliseconds;
      if (ms > 16) { // More than one frame at 60fps
        print('⚠️ Performance Warning: $operation took ${ms}ms');
      } else {
        print('✅ Performance OK: $operation took ${ms}ms');
      }
    }
  }
  
  /// Measure and log function execution time
  static Future<T> measurePerformance<T>(
    String operation,
    Future<T> Function() function,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      logPerformance(operation, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      logPerformance('$operation (failed)', stopwatch.elapsed);
      rethrow;
    }
  }
  
  /// Clean up resources to prevent memory leaks
  static void cleanup() {
    _debounceTimers.values.forEach((timer) => timer.cancel());
    _throttleTimers.values.forEach((timer) => timer.cancel());
    _debounceTimers.clear();
    _throttleTimers.clear();
  }
  
  // Private members
  static final Map<String, Timer> _debounceTimers = {};
  static final Map<String, Timer> _throttleTimers = {};
}

/// Haptic feedback types for better UX
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final String name;

  const PerformanceMonitor({
    super.key,
    required this.child,
    required this.name,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  late final Stopwatch _stopwatch;

  @override
  void initState() {
    super.initState();
    _stopwatch = Stopwatch()..start();
  }

  @override
  void dispose() {
    _stopwatch.stop();
    PerformanceUtils.logPerformance(
      'Widget ${widget.name} lifecycle',
      _stopwatch.elapsed,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Optimized container with performance considerations
class OptimizedContainer extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final BoxConstraints? constraints;
  final AlignmentGeometry? alignment;

  const OptimizedContainer({
    super.key,
    this.child,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.constraints,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    // Use RepaintBoundary for complex decorations
    if (decoration != null || color != null) {
      return RepaintBoundary(
        child: Container(
          padding: padding,
          margin: margin,
          color: color,
          decoration: decoration,
          width: width,
          height: height,
          constraints: constraints,
          alignment: alignment,
          child: child,
        ),
      );
    }

    // Simple container without RepaintBoundary for better performance
    return Container(
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      constraints: constraints,
      alignment: alignment,
      child: child,
    );
  }
}

/// Timer import for debounce/throttle functionality
class Timer {
  final Duration duration;
  final VoidCallback callback;
  bool _isActive = false;

  Timer(this.duration, this.callback) {
    _isActive = true;
    Future.delayed(duration, () {
      if (_isActive) {
        callback();
        _isActive = false;
      }
    });
  }

  bool get isActive => _isActive;

  void cancel() {
    _isActive = false;
  }
}
