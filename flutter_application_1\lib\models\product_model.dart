import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  final String id;
  final String barcode;
  final String name;
  final String description;
  final String merchantId;
  final String mallId;
  final String unitType; // carton, kg, unit, etc.
  final int unitsPerCarton; // breakdown (e.g., 1 carton = 12 units)
  final double pricePerCarton;
  final double pricePerUnit; // auto-calculated
  final int stockQuantity; // in cartons
  final int lowStockThreshold;
  final String? imageUrl;
  final String category;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy; // merchant user ID

  ProductModel({
    required this.id,
    required this.barcode,
    required this.name,
    required this.description,
    required this.merchantId,
    required this.mallId,
    required this.unitType,
    required this.unitsPerCarton,
    required this.pricePerCarton,
    required this.stockQuantity,
    this.lowStockThreshold = 5,
    this.imageUrl,
    this.category = 'General',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  }) : pricePerUnit = pricePerCarton / unitsPerCarton;

  bool get isLowStock => stockQuantity <= lowStockThreshold;
  bool get isOutOfStock => stockQuantity <= 0;
  int get totalUnits => stockQuantity * unitsPerCarton;
  double get totalStockValue => stockQuantity * pricePerCarton;

  factory ProductModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProductModel(
      id: doc.id,
      barcode: data['barcode'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      merchantId: data['merchantId'] ?? '',
      mallId: data['mallId'] ?? '',
      unitType: data['unitType'] ?? 'unit',
      unitsPerCarton: data['unitsPerCarton'] ?? 1,
      pricePerCarton: (data['pricePerCarton'] ?? 0.0).toDouble(),
      stockQuantity: data['stockQuantity'] ?? 0,
      lowStockThreshold: data['lowStockThreshold'] ?? 5,
      imageUrl: data['imageUrl'],
      category: data['category'] ?? 'General',
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'barcode': barcode,
      'name': name,
      'description': description,
      'merchantId': merchantId,
      'mallId': mallId,
      'unitType': unitType,
      'unitsPerCarton': unitsPerCarton,
      'pricePerCarton': pricePerCarton,
      'pricePerUnit': pricePerUnit,
      'stockQuantity': stockQuantity,
      'lowStockThreshold': lowStockThreshold,
      'imageUrl': imageUrl,
      'category': category,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
    };
  }

  ProductModel copyWith({
    String? barcode,
    String? name,
    String? description,
    String? unitType,
    int? unitsPerCarton,
    double? pricePerCarton,
    int? stockQuantity,
    int? lowStockThreshold,
    String? imageUrl,
    String? category,
    bool? isActive,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      description: description ?? this.description,
      merchantId: merchantId,
      mallId: mallId,
      unitType: unitType ?? this.unitType,
      unitsPerCarton: unitsPerCarton ?? this.unitsPerCarton,
      pricePerCarton: pricePerCarton ?? this.pricePerCarton,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      createdBy: createdBy,
    );
  }
}
