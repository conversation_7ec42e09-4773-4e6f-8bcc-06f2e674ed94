import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../core/enums/user_role.dart';
import 'firebase_service.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  final FirebaseService _firebase = FirebaseService.instance;

  // Auth state stream
  Stream<User?> get authStateChanges => _firebase.authStateChanges;

  // Current user
  User? get currentUser => _firebase.currentUser;
  bool get isSignedIn => _firebase.isSignedIn;
  String? get currentUserId => _firebase.currentUserId;

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Check for demo users first - bypass Firebase completely
    if (password == 'password' && email.contains('demo.com')) {
      if (email == '<EMAIL>') {
        return _createDemoUser('customer', email, 'Customer', 'Demo');
      } else if (email == '<EMAIL>') {
        return _createDemoUser('merchant', email, 'Merchant', 'Demo');
      } else if (email == '<EMAIL>') {
        return _createDemoUser('sales_rep', email, 'Sales', 'Rep');
      } else if (email == '<EMAIL>') {
        return _createDemoUser('admin', email, 'Admin', 'Demo');
      } else if (email == '<EMAIL>') {
        return _createDemoUser('boss', email, 'Boss', 'Demo');
      }
      // If it's a demo.com email but not recognized, return error
      throw 'Invalid demo credentials. Please use the provided demo emails.';
    }

    // For non-demo users, try Firebase authentication
    try {
      final UserCredential result =
          await _firebase.auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (result.user != null) {
        return await getUserData(result.user!.uid);
      }
      return null;
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Register new user
  Future<UserModel?> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required UserRole role,
    String? phoneNumber,
    String? mallId,
    String? merchantId,
    String? adminId,
    Map<String, dynamic>? securityQuestions,
  }) async {
    // Check if it's a demo email - don't allow registration for demo emails
    if (email.contains('demo.com')) {
      throw 'Demo emails cannot be used for registration. Please use a different email address.';
    }

    try {
      final UserCredential result =
          await _firebase.auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (result.user != null) {
        final user = UserModel(
          id: result.user!.uid,
          email: email.trim(),
          firstName: firstName.trim(),
          lastName: lastName.trim(),
          role: role,
          phoneNumber: phoneNumber?.trim(),
          mallId: mallId,
          merchantId: merchantId,
          adminId: adminId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          securityQuestions: securityQuestions,
        );

        await _firebase.usersCollection
            .doc(result.user!.uid)
            .set(user.toFirestore());

        // Update display name
        await result.user!
            .updateDisplayName('${firstName.trim()} ${lastName.trim()}');

        return user;
      }
      return null;
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Get user data
  Future<UserModel?> getUserData(String userId) async {
    try {
      final doc = await _firebase.usersCollection.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Update user data
  Future<void> updateUserData(UserModel user) async {
    try {
      await _firebase.usersCollection.doc(user.id).update(user.toFirestore());
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _firebase.auth.signOut();
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _firebase.auth.sendPasswordResetEmail(email: email.trim());
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = _firebase.currentUser;
      if (user != null) {
        await user.updatePassword(newPassword);
      } else {
        throw 'No user is currently signed in.';
      }
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Update email
  Future<void> updateEmail(String newEmail) async {
    try {
      final user = _firebase.currentUser;
      if (user != null) {
        await user.updateEmail(newEmail.trim());

        // Update email in Firestore as well
        await _firebase.usersCollection.doc(user.uid).update({
          'email': newEmail.trim(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        throw 'No user is currently signed in.';
      }
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      final user = _firebase.currentUser;
      if (user != null) {
        // Delete user data from Firestore
        await _firebase.usersCollection.doc(user.uid).delete();

        // Delete the user account
        await user.delete();
      } else {
        throw 'No user is currently signed in.';
      }
    } catch (e) {
      throw _firebase.getFirebaseErrorMessage(e);
    }
  }

  // Check if user has specific role
  Future<bool> hasRole(UserRole role) async {
    try {
      final user = await getUserData(currentUserId!);
      return user?.role == role;
    } catch (e) {
      return false;
    }
  }

  // Verify security questions (for sales reps and admins)
  Future<bool> verifySecurityQuestions(
    String userId,
    Map<String, String> answers,
  ) async {
    try {
      final user = await getUserData(userId);
      if (user?.securityQuestions == null) return false;

      final storedQuestions = user!.securityQuestions!;

      for (final entry in answers.entries) {
        final storedAnswer = storedQuestions[entry.key];
        if (storedAnswer == null ||
            storedAnswer.toString().toLowerCase() !=
                entry.value.toLowerCase()) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Create demo user for testing
  UserModel _createDemoUser(
      String roleString, String email, String firstName, String lastName) {
    UserRole role;
    switch (roleString) {
      case 'customer':
        role = UserRole.customer;
        break;
      case 'merchant':
        role = UserRole.merchant;
        break;
      case 'sales_rep':
        role = UserRole.salesRep;
        break;
      case 'admin':
        role = UserRole.admin;
        break;
      case 'boss':
        role = UserRole.boss;
        break;
      default:
        role = UserRole.customer;
    }

    return UserModel(
      id: 'demo_${roleString}_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      firstName: firstName,
      lastName: lastName,
      role: role,
      phoneNumber: '+234 ************',
      mallId: 'demo-mall-001',
      merchantId: role == UserRole.merchant ? 'demo-merchant-001' : null,
      adminId: role == UserRole.admin ? 'demo-admin-001' : null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isActive: true,
    );
  }
}
