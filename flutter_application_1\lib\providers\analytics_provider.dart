import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;

import '../models/analytics_model.dart';
import '../models/product_model.dart';
import '../models/order_model.dart';
import '../models/transaction_model.dart';
import 'auth_provider.dart';
import 'product_provider.dart';
import 'order_provider.dart';
import 'transaction_provider.dart';

// Analytics service for calculating metrics
class AnalyticsService {
  // Calculate sales metrics from orders and transactions
  SalesMetrics calculateSalesMetrics(
    List<OrderModel> orders,
    List<TransactionModel> transactions,
    DateTime periodStart,
    DateTime periodEnd,
  ) {
    // Filter orders and transactions by date range
    final filteredOrders = orders
        .where((order) =>
            order.createdAt.isAfter(periodStart) &&
            order.createdAt.isBefore(periodEnd))
        .toList();

    final filteredTransactions = transactions
        .where((transaction) =>
            transaction.createdAt.isAfter(periodStart) &&
            transaction.createdAt.isBefore(periodEnd))
        .toList();

    // Calculate metrics from orders (primary source)
    final totalRevenue =
        filteredOrders.fold<double>(0, (sum, order) => sum + order.total);
    final totalOrders = filteredOrders.length;
    final totalItems =
        filteredOrders.fold<int>(0, (sum, order) => sum + order.totalItems);

    // Estimate profit (assuming 30% margin for demo)
    final totalProfit = totalRevenue * 0.3;
    final averageOrderValue =
        totalOrders > 0 ? totalRevenue / totalOrders : 0.0;
    final profitMargin =
        totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0.0;

    return SalesMetrics(
      totalRevenue: totalRevenue,
      totalProfit: totalProfit,
      totalOrders: totalOrders,
      totalItems: totalItems,
      averageOrderValue: averageOrderValue,
      profitMargin: profitMargin,
      periodStart: periodStart,
      periodEnd: periodEnd,
    );
  }

  // Calculate inventory metrics from products
  InventoryMetrics calculateInventoryMetrics(List<ProductModel> products) {
    final totalProducts = products.length;
    final lowStockProducts =
        products.where((p) => p.isLowStock && !p.isOutOfStock).length;
    final outOfStockProducts = products.where((p) => p.isOutOfStock).length;
    final totalInventoryValue = products.fold<double>(
        0, (sum, product) => sum + product.totalStockValue);
    final averageStockLevel = totalProducts > 0
        ? products.fold<int>(0, (sum, product) => sum + product.stockQuantity) /
            totalProducts
        : 0.0;

    // Category breakdown
    final categoryBreakdown = <String, int>{};
    for (final product in products) {
      categoryBreakdown[product.category] =
          (categoryBreakdown[product.category] ?? 0) + 1;
    }

    // Generate demo top selling products
    final topSellingProducts = _generateTopSellingProducts(products);

    // Generate demo slow moving products
    final slowMovingProducts = _generateSlowMovingProducts(products);

    return InventoryMetrics(
      totalProducts: totalProducts,
      lowStockProducts: lowStockProducts,
      outOfStockProducts: outOfStockProducts,
      totalInventoryValue: totalInventoryValue,
      averageStockLevel: averageStockLevel,
      categoryBreakdown: categoryBreakdown,
      topSellingProducts: topSellingProducts,
      slowMovingProducts: slowMovingProducts,
    );
  }

  // Calculate customer metrics from orders
  CustomerMetrics calculateCustomerMetrics(List<OrderModel> orders) {
    final customerMap = <String, List<OrderModel>>{};

    // Group orders by customer
    for (final order in orders) {
      customerMap.putIfAbsent(order.customerId, () => []).add(order);
    }

    final totalCustomers = customerMap.length;
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

    final newCustomers = customerMap.values.where((customerOrders) {
      final firstOrder = customerOrders
          .reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b);
      return firstOrder.createdAt.isAfter(thirtyDaysAgo);
    }).length;

    final returningCustomers = customerMap.values
        .where((customerOrders) => customerOrders.length > 1)
        .length;

    final customerRetentionRate =
        totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0.0;

    final totalRevenue =
        orders.fold<double>(0, (sum, order) => sum + order.total);
    final averageCustomerValue =
        totalCustomers > 0 ? totalRevenue / totalCustomers : 0.0;

    // Generate top customers
    final topCustomers = _generateTopCustomers(customerMap);

    return CustomerMetrics(
      totalCustomers: totalCustomers,
      newCustomers: newCustomers,
      returningCustomers: returningCustomers,
      customerRetentionRate: customerRetentionRate,
      averageCustomerValue: averageCustomerValue,
      topCustomers: topCustomers,
    );
  }

  // Generate sales trend data
  List<SalesDataPoint> generateSalesTrend(List<OrderModel> orders, int days) {
    final now = DateTime.now();
    final trendData = <SalesDataPoint>[];

    for (int i = days - 1; i >= 0; i--) {
      final date =
          DateTime(now.year, now.month, now.day).subtract(Duration(days: i));
      final nextDate = date.add(const Duration(days: 1));

      final dayOrders = orders
          .where((order) =>
              order.createdAt.isAfter(date) &&
              order.createdAt.isBefore(nextDate))
          .toList();

      final dayRevenue =
          dayOrders.fold<double>(0, (sum, order) => sum + order.total);

      trendData.add(SalesDataPoint(
        date: date,
        revenue: dayRevenue,
        orders: dayOrders.length,
      ));
    }

    return trendData;
  }

  // Generate category performance data
  Map<String, double> generateCategoryPerformance(List<OrderModel> orders) {
    final categoryRevenue = <String, double>{};

    for (final order in orders) {
      for (final item in order.items) {
        // For demo, we'll use product names to infer categories
        String category = 'General';
        if (item.name.toLowerCase().contains('samsung') ||
            item.name.toLowerCase().contains('iphone') ||
            item.name.toLowerCase().contains('phone')) {
          category = 'Electronics';
        } else if (item.name.toLowerCase().contains('apple') ||
            item.name.toLowerCase().contains('food')) {
          category = 'Food';
        } else if (item.name.toLowerCase().contains('shirt') ||
            item.name.toLowerCase().contains('fashion')) {
          category = 'Fashion';
        }

        categoryRevenue[category] =
            (categoryRevenue[category] ?? 0) + item.totalPrice;
      }
    }

    return categoryRevenue;
  }

  // Helper method to generate demo top selling products
  List<TopSellingProduct> _generateTopSellingProducts(
      List<ProductModel> products) {
    final random = math.Random();
    return products.take(5).map((product) {
      final quantitySold = random.nextInt(100) + 10;
      final revenue = quantitySold * product.pricePerUnit;
      final profitMargin = 20 + random.nextDouble() * 30; // 20-50% margin

      return TopSellingProduct(
        productId: product.id,
        productName: product.name,
        category: product.category,
        quantitySold: quantitySold,
        revenue: revenue,
        profitMargin: profitMargin,
      );
    }).toList();
  }

  // Helper method to generate demo slow moving products
  List<SlowMovingProduct> _generateSlowMovingProducts(
      List<ProductModel> products) {
    final random = math.Random();
    return products.where((p) => p.stockQuantity > 0).take(3).map((product) {
      final daysSinceLastSale = random.nextInt(60) + 30; // 30-90 days

      return SlowMovingProduct(
        productId: product.id,
        productName: product.name,
        category: product.category,
        currentStock: product.stockQuantity,
        daysSinceLastSale: daysSinceLastSale,
        inventoryValue: product.totalStockValue,
      );
    }).toList();
  }

  // Helper method to generate demo top customers
  List<TopCustomer> _generateTopCustomers(
      Map<String, List<OrderModel>> customerMap) {
    final topCustomers = customerMap.entries.map((entry) {
      final customerId = entry.key;
      final customerOrders = entry.value;
      final totalSpent =
          customerOrders.fold<double>(0, (sum, order) => sum + order.total);
      final lastOrder = customerOrders
          .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);

      return TopCustomer(
        customerId: customerId,
        customerName: customerOrders.first.customerName,
        customerEmail: customerOrders.first.customerEmail,
        totalOrders: customerOrders.length,
        totalSpent: totalSpent,
        lastOrderDate: lastOrder.createdAt,
      );
    }).toList();

    // Sort by total spent and take top 5
    topCustomers.sort((a, b) => b.totalSpent.compareTo(a.totalSpent));
    return topCustomers.take(5).toList();
  }
}

// Demo Analytics Repository
class DemoAnalyticsRepository {
  final AnalyticsService _analyticsService = AnalyticsService();

  Future<DashboardAnalytics> getDashboardAnalytics(
    String merchantId,
    String mallId,
    List<ProductModel> products,
    List<OrderModel> orders,
    List<TransactionModel> transactions,
  ) async {
    await Future.delayed(const Duration(milliseconds: 800)); // Simulate loading

    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    // Calculate metrics
    final salesMetrics = _analyticsService.calculateSalesMetrics(
        orders, transactions, thirtyDaysAgo, now);

    final inventoryMetrics =
        _analyticsService.calculateInventoryMetrics(products);

    final customerMetrics = _analyticsService.calculateCustomerMetrics(orders);

    final salesTrend = _analyticsService.generateSalesTrend(orders, 30);

    final categoryPerformance =
        _analyticsService.generateCategoryPerformance(orders);

    return DashboardAnalytics(
      salesMetrics: salesMetrics,
      inventoryMetrics: inventoryMetrics,
      customerMetrics: customerMetrics,
      salesTrend: salesTrend,
      categoryPerformance: categoryPerformance,
      lastUpdated: now,
    );
  }

  Future<SalesMetrics> getSalesMetrics(
    String merchantId,
    String mallId,
    DateTime startDate,
    DateTime endDate,
    List<OrderModel> orders,
    List<TransactionModel> transactions,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _analyticsService.calculateSalesMetrics(
        orders, transactions, startDate, endDate);
  }

  Future<InventoryMetrics> getInventoryMetrics(
    String merchantId,
    String mallId,
    List<ProductModel> products,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _analyticsService.calculateInventoryMetrics(products);
  }

  Future<CustomerMetrics> getCustomerMetrics(
    String merchantId,
    String mallId,
    List<OrderModel> orders,
  ) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _analyticsService.calculateCustomerMetrics(orders);
  }
}

// Providers
final analyticsServiceProvider =
    Provider<AnalyticsService>((ref) => AnalyticsService());
final demoAnalyticsRepositoryProvider =
    Provider<DemoAnalyticsRepository>((ref) => DemoAnalyticsRepository());

// Dashboard analytics provider
final dashboardAnalyticsProvider = FutureProvider<DashboardAnalytics>((ref) async {
  // Always return demo/mock data for UI/UX review
  final repository = ref.watch(demoAnalyticsRepositoryProvider);
  // Use demo IDs and empty lists for products/orders/transactions
  return await repository.getDashboardAnalytics(
    'demo-merchant',
    'demo-mall',
    [], // products
    [], // orders
    [], // transactions
  );
});

// Sales metrics provider with date range
final salesMetricsProvider =
    FutureProvider.family<SalesMetrics, ({DateTime start, DateTime end})>(
        (ref, params) async {
  final user = ref.watch(currentUserProvider);
  final orders = await ref.watch(currentUserOrdersProvider.future);
  final transactions = await ref.watch(currentUserTransactionsProvider.future);
  final repository = ref.watch(demoAnalyticsRepositoryProvider);

  return await user.when(
    data: (userData) async {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return await repository.getSalesMetrics(
          userData!.id,
          userData.mallId!,
          params.start,
          params.end,
          orders,
          transactions,
        );
      }
      return SalesMetrics.empty(params.start, params.end);
    },
    loading: () async => SalesMetrics.empty(params.start, params.end),
    error: (_, __) async => SalesMetrics.empty(params.start, params.end),
  );
});

// Inventory metrics provider
final inventoryMetricsProvider = FutureProvider<InventoryMetrics>((ref) async {
  final user = ref.watch(currentUserProvider);
  final products = await ref.watch(currentUserProductsProvider.future);
  final repository = ref.watch(demoAnalyticsRepositoryProvider);

  return await user.when(
    data: (userData) async {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return await repository.getInventoryMetrics(
          userData!.id,
          userData.mallId!,
          products,
        );
      }
      return InventoryMetrics.empty();
    },
    loading: () async => InventoryMetrics.empty(),
    error: (_, __) async => InventoryMetrics.empty(),
  );
});

// Customer metrics provider
final customerMetricsProvider = FutureProvider<CustomerMetrics>((ref) async {
  final user = ref.watch(currentUserProvider);
  final orders = await ref.watch(currentUserOrdersProvider.future);
  final repository = ref.watch(demoAnalyticsRepositoryProvider);

  return await user.when(
    data: (userData) async {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return await repository.getCustomerMetrics(
          userData!.id,
          userData.mallId!,
          orders,
        );
      }
      return CustomerMetrics.empty();
    },
    loading: () async => CustomerMetrics.empty(),
    error: (_, __) async => CustomerMetrics.empty(),
  );
});
