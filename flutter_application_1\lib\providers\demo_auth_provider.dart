import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_model.dart';
import '../core/enums/user_role.dart';

// Demo user state provider
final demoUserProvider = StateNotifierProvider<DemoUserNotifier, UserModel?>((ref) {
  return DemoUserNotifier();
});

class DemoUserNotifier extends StateNotifier<UserModel?> {
  DemoUserNotifier() : super(null);

  // Demo login
  void loginDemoUser(String email) {
    UserModel? user;
    
    switch (email) {
      case '<EMAIL>':
        user = _createDemoUser('customer', email, 'Customer', 'Demo', UserRole.customer);
        break;
      case '<EMAIL>':
        user = _createDemoUser('merchant', email, 'Merchant', 'Demo', UserRole.merchant);
        break;
      case '<EMAIL>':
        user = _createDemoUser('salesrep', email, 'Sales', 'Rep', UserRole.salesRep);
        break;
      case '<EMAIL>':
        user = _createDemoUser('admin', email, 'Admin', 'Demo', UserRole.admin);
        break;
      case '<EMAIL>':
        user = _createDemoUser('boss', email, 'Boss', 'Demo', UserRole.boss);
        break;
    }
    
    if (user != null) {
      state = user;
    }
  }

  // Demo logout
  void logout() {
    state = null;
  }

  // Create demo user
  UserModel _createDemoUser(String roleString, String email, String firstName, String lastName, UserRole role) {
    return UserModel(
      id: 'demo_${roleString}_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      firstName: firstName,
      lastName: lastName,
      role: role,
      phoneNumber: '+234 ************',
      mallId: 'demo-mall-001',
      merchantId: role == UserRole.merchant ? 'demo-merchant-001' : null,
      adminId: role == UserRole.admin ? 'demo-admin-001' : null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isActive: true,
    );
  }
}

// Check if user is demo user
final isDemoUserProvider = Provider<bool>((ref) {
  final user = ref.watch(demoUserProvider);
  return user?.email.contains('demo.com') ?? false;
});
