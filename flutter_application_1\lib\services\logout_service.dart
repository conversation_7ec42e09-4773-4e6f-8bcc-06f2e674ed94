import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_provider.dart';
import '../providers/demo_auth_provider.dart';
import '../providers/role_based_auth_provider.dart';

/// Centralized logout service to ensure consistent logout behavior across all dashboards
class LogoutService {
  /// Perform complete logout with proper state clearing
  static Future<void> performLogout({
    required BuildContext context,
    required WidgetRef ref,
    bool showLoadingDialog = true,
    String? customMessage,
  }) async {
    try {
      debugPrint('🔄 Starting logout process...');

      // Show loading dialog if requested
      if (showLoadingDialog && context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => _buildLoadingDialog(customMessage),
        );
      }

      // Step 1: Clear demo user state immediately and verify
      debugPrint('🔄 Step 1: Clearing demo user state...');
      await _clearDemoUserState(ref);
      await Future.delayed(const Duration(milliseconds: 150));

      // Step 2: Clear Firebase authentication and verify
      debugPrint('🔄 Step 2: Clearing Firebase authentication...');
      await _clearFirebaseAuth(ref);
      await Future.delayed(const Duration(milliseconds: 200));

      // Step 3: Force invalidate all providers multiple times
      debugPrint('🔄 Step 3: Invalidating all providers...');
      await _clearAllProviders(ref);
      await Future.delayed(const Duration(milliseconds: 150));

      // Step 4: Additional provider invalidation to ensure clearing
      debugPrint('🔄 Step 4: Additional provider invalidation...');
      _forceInvalidateAllProviders(ref);
      await Future.delayed(const Duration(milliseconds: 100));

      // Step 5: Force provider refresh with extended verification
      debugPrint('🔄 Step 5: Force provider refresh...');
      await _forceProviderRefresh(ref);
      await Future.delayed(const Duration(milliseconds: 200));

      // Step 6: Navigate to login immediately after clearing state
      debugPrint('🔄 Step 6: Navigating to login page...');
      if (context.mounted) {
        context.go('/login');
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // Step 6: Comprehensive logout verification with multiple attempts
      debugPrint('🔄 Step 6: Verifying logout completion...');
      bool isLogoutComplete = false;
      for (int attempt = 1; attempt <= 3; attempt++) {
        isLogoutComplete = await _verifyLogoutComplete(ref);
        if (isLogoutComplete) {
          debugPrint('✅ Logout verified successfully on attempt $attempt');
          break;
        }
        debugPrint(
            '⚠️ Logout verification failed on attempt $attempt, retrying...');

        if (attempt < 3) {
          // Additional clearing attempt
          _forceInvalidateAllProviders(ref);
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      // Step 7: Emergency logout if verification still fails
      if (!isLogoutComplete) {
        debugPrint(
            '🚨 Logout verification failed after 3 attempts, executing emergency logout...');
        await _emergencyLogout(ref);
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // Close loading dialog if it was shown
      if (showLoadingDialog && context.mounted) {
        Navigator.of(context).pop();
      }

      // Step 8: Final verification and user feedback
      debugPrint('🔄 Step 8: Final verification...');
      final finalCheck = await _verifyLogoutComplete(ref);
      if (context.mounted) {
        if (finalCheck) {
          debugPrint('✅ Logout completed successfully');
          _showLogoutSuccessMessage(context);
        } else {
          debugPrint('❌ Critical: Logout may not have completed successfully');
          // Force one more emergency logout
          await _emergencyLogout(ref);
          // Ensure we're still on login page
          if (context.mounted) {
            context.go('/login');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error during logout: $e');

      // Close loading dialog if open
      if (showLoadingDialog && context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (context.mounted) {
        _showLogoutErrorMessage(context, e.toString());
      }

      // Execute emergency logout as fallback
      try {
        await _emergencyLogout(ref);
      } catch (emergencyError) {
        debugPrint('❌ Emergency logout also failed: $emergencyError');
      }

      // Still navigate to login as last resort
      if (context.mounted) {
        context.go('/login');
      }
    }
  }

  /// Clear demo user state
  static Future<void> _clearDemoUserState(WidgetRef ref) async {
    try {
      // Use the logout method to properly clear demo user state
      ref.read(demoUserProvider.notifier).logout();

      // Wait a moment to ensure state is cleared
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify the state is cleared
      final currentDemoUser = ref.read(demoUserProvider);
      if (currentDemoUser != null) {
        debugPrint('Warning: Demo user state not fully cleared');
      }
    } catch (e) {
      // Continue even if demo user clearing fails
      debugPrint('Error clearing demo user state: $e');
    }
  }

  /// Clear Firebase authentication
  static Future<void> _clearFirebaseAuth(WidgetRef ref) async {
    try {
      await ref.read(authControllerProvider.notifier).signOut();

      // Wait a moment to ensure Firebase auth is cleared
      await Future.delayed(const Duration(milliseconds: 200));

      // Verify Firebase auth is cleared
      final authState = ref.read(authControllerProvider);
      authState.when(
        data: (user) {
          if (user != null) {
            debugPrint('Warning: Firebase auth state not fully cleared');
          }
        },
        loading: () => debugPrint('Firebase auth still loading'),
        error: (_, __) => debugPrint('Firebase auth error during logout'),
      );
    } catch (e) {
      // Continue even if Firebase sign out fails
      debugPrint('Error clearing Firebase auth: $e');
    }
  }

  /// Clear all related providers
  static Future<void> _clearAllProviders(WidgetRef ref) async {
    try {
      // Invalidate core authentication providers
      ref.invalidate(currentUserProvider);
      ref.invalidate(authStateProvider);
      ref.invalidate(authControllerProvider);
      ref.invalidate(demoUserProvider);
      ref.invalidate(roleBasedAuthProvider);
      ref.invalidate(unifiedCurrentUserProvider);
      ref.invalidate(isAuthenticatedUnifiedProvider);

      // Invalidate role-specific providers
      ref.invalidate(isCustomerUnifiedProvider);
      ref.invalidate(isSalesRepUnifiedProvider);
      ref.invalidate(isMerchantUnifiedProvider);
      ref.invalidate(isAdminUnifiedProvider);
      ref.invalidate(isBossUnifiedProvider);

      // Invalidate permission providers
      ref.invalidate(hasWebAccessProvider);
      ref.invalidate(hasMobileAccessProvider);
      ref.invalidate(canManageProductsProvider);
      ref.invalidate(canViewAnalyticsProvider);
      ref.invalidate(canManageMerchantsProvider);
      ref.invalidate(canManageAdminsProvider);

      // Invalidate user info providers
      ref.invalidate(userDisplayNameProvider);
      ref.invalidate(userRoleDisplayNameProvider);
      ref.invalidate(userDashboardRouteProvider);

      // Wait for provider invalidation to complete
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      debugPrint('Error invalidating providers: $e');
    }
  }

  /// Force complete provider refresh with multiple attempts
  static Future<void> _forceProviderRefresh(WidgetRef ref) async {
    try {
      debugPrint('🔄 Starting provider refresh...');

      // Multiple refresh attempts to ensure state is cleared
      for (int i = 0; i < 5; i++) {
        debugPrint('🔄 Provider refresh attempt ${i + 1}/5...');

        // Force refresh the role-based auth provider
        try {
          await ref.read(roleBasedAuthProvider.notifier).refresh();
        } catch (e) {
          debugPrint('⚠️ Role-based auth refresh failed: $e');
        }

        // Additional invalidation during refresh
        _forceInvalidateAllProviders(ref);

        // Wait between attempts with increasing delay
        await Future.delayed(Duration(milliseconds: 100 + (i * 50)));

        // Check if authentication is cleared
        try {
          final isAuthenticated = ref.read(isAuthenticatedUnifiedProvider);
          final unifiedUser = ref.read(unifiedCurrentUserProvider);
          final demoUser = ref.read(demoUserProvider);
          final authUser = ref.read(authControllerProvider).value;

          if (!isAuthenticated &&
              unifiedUser == null &&
              demoUser == null &&
              authUser == null) {
            debugPrint(
                '✅ Authentication state cleared after ${i + 1} attempts');
            break;
          }

          debugPrint('⚠️ Authentication state still active:');
          debugPrint('  - isAuthenticated: $isAuthenticated');
          debugPrint('  - unifiedUser: ${unifiedUser != null}');
          debugPrint('  - demoUser: ${demoUser != null}');
          debugPrint('  - authUser: ${authUser != null}');
        } catch (e) {
          debugPrint('⚠️ Error checking authentication state: $e');
        }

        if (i == 4) {
          debugPrint(
              '❌ Warning: Authentication state still active after 5 refresh attempts');
        }
      }
    } catch (e) {
      debugPrint('❌ Error in force provider refresh: $e');
    }
  }

  /// Verify that logout has completed successfully
  static Future<bool> _verifyLogoutComplete(WidgetRef ref) async {
    try {
      debugPrint('🔍 Verifying logout completion...');

      // Wait a moment for all async operations to complete
      await Future.delayed(const Duration(milliseconds: 300));

      // Check all authentication states with detailed logging
      final authUser = ref.read(authControllerProvider).value;
      final demoUser = ref.read(demoUserProvider);
      final isAuthenticated = ref.read(isAuthenticatedUnifiedProvider);
      final unifiedUser = ref.read(unifiedCurrentUserProvider);

      // Additional checks for role-specific providers
      final isAdmin = ref.read(isAdminUnifiedProvider);
      final roleBasedState = ref.read(roleBasedAuthProvider);

      // Verify all states are cleared
      final isLoggedOut = authUser == null &&
          demoUser == null &&
          !isAuthenticated &&
          unifiedUser == null &&
          !isAdmin &&
          !roleBasedState.isAuthenticated;

      debugPrint('🔍 Logout verification results:');
      debugPrint(
          '  - Auth user: ${authUser != null} ${authUser?.email ?? 'null'}');
      debugPrint(
          '  - Demo user: ${demoUser != null} ${demoUser?.email ?? 'null'}');
      debugPrint('  - Is authenticated (unified): $isAuthenticated');
      debugPrint(
          '  - Unified user: ${unifiedUser != null} ${unifiedUser?.email ?? 'null'}');
      debugPrint('  - Is admin: $isAdmin');
      debugPrint(
          '  - Role-based auth state: ${roleBasedState.isAuthenticated}');
      debugPrint(
          '  - Overall logout status: ${isLoggedOut ? '✅ SUCCESS' : '❌ FAILED'}');

      if (!isLoggedOut) {
        debugPrint('❌ Logout verification failed - some state still persists');
      } else {
        debugPrint('✅ Logout verification successful - all state cleared');
      }

      return isLoggedOut;
    } catch (e) {
      debugPrint('❌ Error verifying logout completion: $e');
      return false;
    }
  }

  /// Emergency logout method for when normal logout fails
  static Future<void> _emergencyLogout(WidgetRef ref) async {
    try {
      debugPrint('🚨 Executing emergency logout...');

      // Force clear demo user multiple times with verification
      for (int i = 0; i < 5; i++) {
        debugPrint('🚨 Emergency demo user clear attempt ${i + 1}/5...');
        ref.read(demoUserProvider.notifier).logout();

        await Future.delayed(const Duration(milliseconds: 100));

        // Verify demo user is cleared
        final demoUser = ref.read(demoUserProvider);
        if (demoUser == null) {
          debugPrint('✅ Demo user cleared on attempt ${i + 1}');
          break;
        }
      }

      // Force Firebase sign out multiple times
      for (int i = 0; i < 5; i++) {
        debugPrint('🚨 Emergency Firebase signout attempt ${i + 1}/5...');
        try {
          await ref.read(authControllerProvider.notifier).signOut();
          await Future.delayed(const Duration(milliseconds: 100));

          // Verify Firebase auth is cleared
          final authUser = ref.read(authControllerProvider).value;
          if (authUser == null) {
            debugPrint('✅ Firebase auth cleared on attempt ${i + 1}');
            break;
          }
        } catch (e) {
          debugPrint('⚠️ Firebase signout attempt ${i + 1} failed: $e');
        }
      }

      // Force invalidate all providers multiple times
      for (int i = 0; i < 3; i++) {
        debugPrint('🚨 Emergency provider invalidation attempt ${i + 1}/3...');
        _forceInvalidateAllProviders(ref);
        await Future.delayed(const Duration(milliseconds: 150));
      }

      // Force role-based auth signout and clear
      try {
        await ref.read(roleBasedAuthProvider.notifier).signOut();
        await Future.delayed(const Duration(milliseconds: 100));

        // Force clear as backup
        ref.read(roleBasedAuthProvider.notifier).forceClear();
        debugPrint('✅ Role-based auth signout and force clear completed');
      } catch (e) {
        debugPrint('⚠️ Role-based auth signout failed: $e');
        // Try force clear anyway
        try {
          ref.read(roleBasedAuthProvider.notifier).forceClear();
          debugPrint('✅ Role-based auth force clear completed as fallback');
        } catch (clearError) {
          debugPrint('⚠️ Role-based auth force clear also failed: $clearError');
        }
      }

      debugPrint('🚨 Emergency logout completed');
    } catch (e) {
      debugPrint('❌ Error in emergency logout: $e');
    }
  }

  /// Force invalidate all providers immediately
  static void _forceInvalidateAllProviders(WidgetRef ref) {
    try {
      // Core authentication providers
      ref.invalidate(currentUserProvider);
      ref.invalidate(authStateProvider);
      ref.invalidate(authControllerProvider);
      ref.invalidate(demoUserProvider);
      ref.invalidate(roleBasedAuthProvider);
      ref.invalidate(unifiedCurrentUserProvider);
      ref.invalidate(isAuthenticatedUnifiedProvider);

      // Role-specific providers (unified)
      ref.invalidate(isCustomerUnifiedProvider);
      ref.invalidate(isSalesRepUnifiedProvider);
      ref.invalidate(isMerchantUnifiedProvider);
      ref.invalidate(isAdminUnifiedProvider);
      ref.invalidate(isBossUnifiedProvider);

      // Additional role-specific providers if they exist
      try {
        ref.invalidate(isCustomerProvider);
        ref.invalidate(isSalesRepProvider);
        ref.invalidate(isMerchantProvider);
        ref.invalidate(isAdminProvider);
        ref.invalidate(isBossProvider);
      } catch (e) {
        // These providers might not exist, continue
      }

      // Access providers
      ref.invalidate(hasWebAccessProvider);
      ref.invalidate(hasMobileAccessProvider);

      // Permission providers
      ref.invalidate(canManageProductsProvider);
      ref.invalidate(canViewAnalyticsProvider);
      ref.invalidate(canManageMerchantsProvider);
      ref.invalidate(canManageAdminsProvider);

      // User info providers
      ref.invalidate(userDisplayNameProvider);
      ref.invalidate(userRoleDisplayNameProvider);
      ref.invalidate(userDashboardRouteProvider);

      // Additional user info providers if they exist
      try {
        ref.invalidate(currentUserIdProvider);
        ref.invalidate(currentMallIdProvider);
        ref.invalidate(currentMerchantIdProvider);
      } catch (e) {
        // These providers might not exist, continue
      }

      // Demo user provider
      ref.invalidate(isDemoUserProvider);
    } catch (e) {
      debugPrint('Error force invalidating providers: $e');
    }
  }

  /// Build loading dialog
  static Widget _buildLoadingDialog(String? customMessage) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              customMessage ?? 'Signing out...',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait while we clear your session',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show logout success message
  static void _showLogoutSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('Successfully signed out'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Show logout error message
  static void _showLogoutErrorMessage(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text('Error signing out: $error'),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Quick logout without loading dialog (for emergency cases)
  static Future<void> quickLogout(BuildContext context, WidgetRef ref) async {
    await performLogout(
      context: context,
      ref: ref,
      showLoadingDialog: false,
    );
  }

  /// Check if user is currently authenticated
  static bool isUserAuthenticated(WidgetRef ref) {
    final authUser = ref.read(authControllerProvider).value;
    final demoUser = ref.read(demoUserProvider);
    return authUser != null || demoUser != null;
  }

  /// Get current user for logout verification
  static String? getCurrentUserEmail(WidgetRef ref) {
    final authUser = ref.read(authControllerProvider).value;
    final demoUser = ref.read(demoUserProvider);
    return authUser?.email ?? demoUser?.email;
  }

  /// Verify logout completion
  static bool verifyLogoutComplete(WidgetRef ref) {
    final authUser = ref.read(authControllerProvider).value;
    final demoUser = ref.read(demoUserProvider);
    final isAuthenticated = ref.read(isAuthenticatedUnifiedProvider);

    return authUser == null && demoUser == null && !isAuthenticated;
  }

  /// Force complete logout (emergency method)
  static Future<void> forceLogout(BuildContext context, WidgetRef ref) async {
    try {
      // Clear demo user directly
      ref.read(demoUserProvider.notifier).logout();

      // Clear Firebase auth
      await ref.read(authControllerProvider.notifier).signOut();

      // Invalidate all providers
      ref.invalidate(currentUserProvider);
      ref.invalidate(authStateProvider);
      ref.invalidate(authControllerProvider);
      ref.invalidate(demoUserProvider);
      ref.invalidate(roleBasedAuthProvider);
      ref.invalidate(unifiedCurrentUserProvider);
      ref.invalidate(isAuthenticatedUnifiedProvider);

      // Navigate to login
      if (context.mounted) {
        context.go('/login');
      }
    } catch (e) {
      debugPrint('Error in force logout: $e');
      // Still navigate to login as last resort
      if (context.mounted) {
        context.go('/login');
      }
    }
  }
}

/// Provider for logout service
final logoutServiceProvider = Provider<LogoutService>((ref) {
  return LogoutService();
});
