import 'package:flutter/material.dart';

/// Standalone Inventory Management Demo
/// Completely self-contained without external dependencies
void main() {
  runApp(const StandaloneInventoryApp());
}

class StandaloneInventoryApp extends StatelessWidget {
  const StandaloneInventoryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple Inventory Management',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      debugShowCheckedModeBanner: false,
      home: const StandaloneInventoryDashboard(),
    );
  }
}

class StandaloneInventoryDashboard extends StatefulWidget {
  const StandaloneInventoryDashboard({super.key});

  @override
  State<StandaloneInventoryDashboard> createState() =>
      _StandaloneInventoryDashboardState();
}

class _StandaloneInventoryDashboardState
    extends State<StandaloneInventoryDashboard> {
  // Sample data
  List<Map<String, dynamic>> products = [
    {
      'id': '1',
      'name': 'Samsung Galaxy S24',
      'barcode': '123456789012',
      'category': 'Electronics',
      'price': 299999.99,
      'stock': 15,
    },
    {
      'id': '2',
      'name': 'Nike Air Max',
      'barcode': '234567890123',
      'category': 'Sports',
      'price': 45000.00,
      'stock': 8,
    },
    {
      'id': '3',
      'name': 'MacBook Pro',
      'barcode': '345678901234',
      'category': 'Electronics',
      'price': 850000.00,
      'stock': 0,
    },
    {
      'id': '4',
      'name': 'Adidas T-Shirt',
      'barcode': '456789012345',
      'category': 'Clothing',
      'price': 12500.00,
      'stock': 25,
    },
  ];

  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedStockStatus = 'All';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Inventory Management',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        actions: [
          IconButton(
            onPressed: _showAddProductDialog,
            icon: const Icon(Icons.add, color: Colors.blue),
            tooltip: 'Add Product',
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Bar
          _buildSearchAndFilters(),

          // Product List
          Expanded(
            child: _buildProductList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: 'Search products by name or barcode...',
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Filter Row
          Row(
            children: [
              // Category Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: [
                    'All',
                    'Electronics',
                    'Clothing',
                    'Sports',
                    'Food',
                    'Books',
                    'Home'
                  ]
                      .map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value ?? 'All';
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),

              // Stock Status Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStockStatus,
                  decoration: InputDecoration(
                    labelText: 'Stock Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: ['All', 'In Stock', 'Low Stock', 'Out of Stock']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStockStatus = value ?? 'All';
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductList() {
    final filteredProducts = _filterProducts();

    if (filteredProducts.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      color: Colors.white,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProducts.length,
        itemBuilder: (context, index) {
          final product = filteredProducts[index];
          return _buildProductCard(product);
        },
      ),
    );
  }

  List<Map<String, dynamic>> _filterProducts() {
    return products.where((product) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!product['name'].toLowerCase().contains(query) &&
            !product['barcode'].toLowerCase().contains(query)) {
          return false;
        }
      }

      // Category filter
      if (_selectedCategory != 'All' &&
          product['category'] != _selectedCategory) {
        return false;
      }

      // Stock status filter
      if (_selectedStockStatus != 'All') {
        final stock = product['stock'] as int;
        if (_selectedStockStatus == 'In Stock' && stock <= 0) {
          return false;
        }
        if (_selectedStockStatus == 'Low Stock' && (stock > 10 || stock <= 0)) {
          return false;
        }
        if (_selectedStockStatus == 'Out of Stock' && stock > 0) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Product Image Placeholder
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.inventory_2,
                color: Colors.grey,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),

            // Product Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Barcode: ${product['barcode']}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      _buildCategoryChip(product['category']),
                      const SizedBox(width: 8),
                      _buildStockBadge(product['stock']),
                    ],
                  ),
                ],
              ),
            ),

            // Price and Actions
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '₦${product['price'].toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () => _showEditProductDialog(product),
                      icon: const Icon(Icons.edit, size: 20),
                      tooltip: 'Edit Product',
                    ),
                    IconButton(
                      onPressed: () => _showDeleteProductDialog(product),
                      icon:
                          const Icon(Icons.delete, size: 20, color: Colors.red),
                      tooltip: 'Delete Product',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        category,
        style: TextStyle(
          fontSize: 12,
          color: Colors.blue[800],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStockBadge(int stockQuantity) {
    Color color;
    String text;

    if (stockQuantity <= 0) {
      color = Colors.red;
      text = 'Out of Stock';
    } else if (stockQuantity <= 10) {
      color = Colors.orange;
      text = 'Low Stock ($stockQuantity)';
    } else {
      color = Colors.green;
      text = 'In Stock ($stockQuantity)';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No products found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first product to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddProductDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Product'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddProductDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Product feature - Demo Mode'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showEditProductDialog(Map<String, dynamic> product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${product['name']} - Demo Mode'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showDeleteProductDialog(Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${product['name']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                products.removeWhere((p) => p['id'] == product['id']);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${product['name']} deleted successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
