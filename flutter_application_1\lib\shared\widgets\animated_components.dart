import 'package:flutter/material.dart';
import 'dart:ui';
import '../theme/animation_theme.dart';

/// Advanced animated components with micro-interactions and visual effects
/// Implements modern design patterns and sophisticated animations

/// Glassmorphism Card with Advanced Animations
class GlassmorphicCard extends StatefulWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool enableHover;
  final double borderRadius;
  final double elevation;

  const GlassmorphicCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.onTap,
    this.enableHover = true,
    this.borderRadius = 16.0,
    this.elevation = 2.0,
  });

  @override
  State<GlassmorphicCard> createState() => _GlassmorphicCardState();
}

class _GlassmorphicCardState extends State<GlassmorphicCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _tapController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _glowAnimation;

  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: AnimationTheme.fast,
      vsync: this,
    );
    
    _tapController = AnimationController(
      duration: AnimationTheme.ultraFast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: AnimationTheme.hoverScale,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AnimationTheme.gentleCurve,
    ));

    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation * 2,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AnimationTheme.gentleCurve,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AnimationTheme.gentleCurve,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  void _onHoverEnter() {
    if (!widget.enableHover) return;
    setState(() => _isHovered = true);
    _hoverController.forward();
  }

  void _onHoverExit() {
    if (!widget.enableHover) return;
    setState(() => _isHovered = false);
    _hoverController.reverse();
  }

  void _onTapDown() {
    _tapController.forward();
  }

  void _onTapUp() {
    _tapController.reverse();
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _tapController]),
      builder: (context, _) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          child: MouseRegion(
            onEnter: (_) => _onHoverEnter(),
            onExit: (_) => _onHoverExit(),
            child: GestureDetector(
              onTapDown: (_) => _onTapDown(),
              onTapUp: (_) => _onTapUp(),
              onTapCancel: () => _tapController.reverse(),
              child: Transform.scale(
                scale: _scaleAnimation.value * 
                       (1.0 - (_tapController.value * 0.02)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    boxShadow: [
                      ...AnimationTheme.elevationShadows(_elevationAnimation.value),
                      if (_isHovered)
                        BoxShadow(
                          color: theme.colorScheme.primary.withOpacity(
                            0.1 * _glowAnimation.value,
                          ),
                          blurRadius: 20 * _glowAnimation.value,
                          spreadRadius: 2 * _glowAnimation.value,
                        ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: AnimationTheme.glassBlur,
                        sigmaY: AnimationTheme.glassBlur,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: AnimationTheme.glassGradient(context),
                          borderRadius: BorderRadius.circular(widget.borderRadius),
                          border: Border.all(
                            color: theme.colorScheme.outline.withOpacity(
                              AnimationTheme.glassStrokeOpacity,
                            ),
                            width: 1,
                          ),
                        ),
                        padding: widget.padding,
                        child: widget.child,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Animated Button with Sophisticated Micro-interactions
class AnimatedActionButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final double borderRadius;
  final bool isLoading;
  final IconData? icon;

  const AnimatedActionButton({
    super.key,
    required this.child,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius = 12.0,
    this.isLoading = false,
    this.icon,
  });

  @override
  State<AnimatedActionButton> createState() => _AnimatedActionButtonState();
}

class _AnimatedActionButtonState extends State<AnimatedActionButton>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late AnimationController _loadingController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: AnimationTheme.fast,
      vsync: this,
    );
    
    _pressController = AnimationController(
      duration: AnimationTheme.ultraFast,
      vsync: this,
    );

    _loadingController = AnimationController(
      duration: AnimationTheme.slow,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: AnimationTheme.hoverScale,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AnimationTheme.gentleCurve,
    ));

    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AnimationTheme.gentleCurve,
    ));

    if (widget.isLoading) {
      _loadingController.repeat();
    }
  }

  @override
  void didUpdateWidget(AnimatedActionButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _loadingController.repeat();
      } else {
        _loadingController.stop();
      }
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.primary;
    final foregroundColor = widget.foregroundColor ?? theme.colorScheme.onPrimary;

    _colorAnimation = ColorTween(
      begin: backgroundColor,
      end: backgroundColor.withOpacity(0.9),
    ).animate(_hoverController);

    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _pressController, _loadingController]),
      builder: (context, _) {
        return MouseRegion(
          onEnter: (_) => _hoverController.forward(),
          onExit: (_) => _hoverController.reverse(),
          child: GestureDetector(
            onTapDown: (_) => _pressController.forward(),
            onTapUp: (_) {
              _pressController.reverse();
              if (!widget.isLoading) widget.onPressed?.call();
            },
            onTapCancel: () => _pressController.reverse(),
            child: Transform.scale(
              scale: _scaleAnimation.value * 
                     (1.0 - (_pressController.value * 0.05)),
              child: Container(
                padding: widget.padding ?? const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: _colorAnimation.value,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  boxShadow: AnimationTheme.elevationShadows(
                    _elevationAnimation.value,
                    color: backgroundColor,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.isLoading) ...[
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation(foregroundColor),
                        ),
                      ),
                      const SizedBox(width: 8),
                    ] else if (widget.icon != null) ...[
                      Icon(
                        widget.icon,
                        color: foregroundColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                    ],
                    DefaultTextStyle(
                      style: TextStyle(
                        color: foregroundColor,
                        fontWeight: FontWeight.w600,
                      ),
                      child: widget.child,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Staggered Animation Container for Sequential Reveals
class StaggeredAnimationContainer extends StatefulWidget {
  final List<Widget> children;
  final Duration staggerDelay;
  final Duration animationDuration;
  final Axis direction;
  final bool autoStart;

  const StaggeredAnimationContainer({
    super.key,
    required this.children,
    this.staggerDelay = AnimationTheme.staggerDelay,
    this.animationDuration = AnimationTheme.medium,
    this.direction = Axis.vertical,
    this.autoStart = true,
  });

  @override
  State<StaggeredAnimationContainer> createState() => _StaggeredAnimationContainerState();
}

class _StaggeredAnimationContainerState extends State<StaggeredAnimationContainer>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: AnimationTheme.defaultCurve,
      ));
    }).toList();

    if (widget.autoStart) {
      _startStaggeredAnimation();
    }
  }

  void _startStaggeredAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(widget.staggerDelay * i, () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.direction == Axis.vertical
        ? Column(
            children: _buildAnimatedChildren(),
          )
        : Row(
            children: _buildAnimatedChildren(),
          );
  }

  List<Widget> _buildAnimatedChildren() {
    return List.generate(widget.children.length, (index) {
      return AnimatedBuilder(
        animation: _animations[index],
        builder: (context, _) {
          return AnimationTheme.fadeInUp(
            controller: _controllers[index],
            child: widget.children[index],
          );
        },
      );
    });
  }
}
