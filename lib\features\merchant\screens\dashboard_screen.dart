import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/themes/merchant_theme.dart';
import '../../../providers/analytics_provider.dart';
import '../../../providers/order_provider.dart';
import '../../../providers/product_provider.dart';
import '../../../models/analytics_model.dart';
import '../../../models/order_model.dart';
import 'modern_inventory_dashboard.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: MerchantTheme.slowAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardAnalytics = ref.watch(dashboardAnalyticsProvider);
    final pendingOrders = ref.watch(pendingOrdersProvider);
    final lowStockProducts = ref.watch(lowStockProductsProvider);
    final padding = MerchantTheme.getResponsivePadding(context);

    return Theme(
      data: MerchantTheme.lightTheme,
      child: Scaffold(
        backgroundColor: MerchantTheme.neutral50,
        body: Container(
          decoration: const BoxDecoration(
            gradient: MerchantTheme.backgroundGradient,
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: CustomScrollView(
              slivers: [
                // Header Section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(padding, padding * 2, padding, padding),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => MerchantTheme.primaryGradient.createShader(bounds),
                              child: Text(
                                'Dashboard Overview',
                                style: MerchantTheme.headline1,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Welcome back! Here\'s what\'s happening with your business today.',
                              style: MerchantTheme.bodyLarge,
                            ),
                          ],
                        ),
                        const Spacer(),
                        CircleAvatar(
                          radius: 28,
                          backgroundColor: MerchantTheme.primaryBlue,
                          child: Icon(Icons.store, color: Colors.white, size: 32),
                        ),
                      ],
                    ),
                  ),
                ),
                // Quick Stats Cards
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: padding),
                    child: dashboardAnalytics.when(
                      data: (analytics) => _buildQuickStatsCards(analytics, context),
                      loading: () => _buildLoadingStatsCards(context),
                      error: (error, stack) => _buildErrorCard('Failed to load analytics'),
                    ),
                  ),
                ),
                // Charts and Trends Section (placeholder for now)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: padding),
                    child: dashboardAnalytics.when(
                      data: (analytics) => _buildChartsSection(analytics),
                      loading: () => _buildLoadingChartsSection(),
                      error: (error, stack) => _buildErrorCard('Failed to load charts'),
                    ),
                  ),
                ),
                // Quick Actions
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(padding),
                    child: _buildQuickActionsSection(context),
                  ),
                ),
                // Recent Orders Section (placeholder for now)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(padding),
                    child: _buildRecentOrdersCard([]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStatsCards(DashboardAnalytics analytics, BuildContext context) {
    final stats = [
      {
        'title': 'Total Revenue',
        'value': '₦${analytics.salesMetrics.totalRevenue.toStringAsFixed(0)}',
        'subtitle': 'Last 30 days',
        'icon': Icons.trending_up,
        'gradient': MerchantTheme.successGradient,
        'change': '+12.5%',
        'isPositive': true,
      },
      {
        'title': 'Total Orders',
        'value': '${analytics.salesMetrics.totalOrders}',
        'subtitle': 'This month',
        'icon': Icons.shopping_cart,
        'gradient': MerchantTheme.infoGradient,
        'change': '+8.2%',
        'isPositive': true,
      },
      {
        'title': 'Products',
        'value': '${analytics.inventoryMetrics.totalProducts}',
        'subtitle': 'In inventory',
        'icon': Icons.inventory_2,
        'gradient': MerchantTheme.primaryGradient,
        'change': '+3 new',
        'isPositive': true,
      },
      {
        'title': 'Low Stock',
        'value': '${analytics.inventoryMetrics.lowStockProducts}',
        'subtitle': 'Need attention',
        'icon': Icons.warning,
        'gradient': MerchantTheme.warningGradient,
        'change': '-2 items',
        'isPositive': false,
      },
    ];

    final crossAxisCount = MerchantTheme.getResponsiveGridCrossAxisCount(context);
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: MerchantTheme.spacing24,
        mainAxisSpacing: MerchantTheme.spacing24,
        childAspectRatio: 1.2,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return Container(
          decoration: BoxDecoration(
            gradient: stat['gradient'] as LinearGradient,
            borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
            boxShadow: MerchantTheme.cardShadow,
          ),
          padding: const EdgeInsets.all(MerchantTheme.spacing24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(stat['icon'] as IconData, color: Colors.white, size: 28),
              const SizedBox(height: MerchantTheme.spacing16),
              Text(stat['value'] as String, style: MerchantTheme.headline2.copyWith(color: Colors.white)),
              const SizedBox(height: MerchantTheme.spacing8),
              Text(stat['title'] as String, style: MerchantTheme.bodyLarge.copyWith(color: Colors.white70)),
              const Spacer(),
              Row(
                children: [
                  Icon(
                    (stat['isPositive'] as bool) ? Icons.arrow_upward : Icons.arrow_downward,
                    color: (stat['isPositive'] as bool) ? Colors.greenAccent : Colors.redAccent,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    stat['change'] as String,
                    style: MerchantTheme.bodySmall.copyWith(
                      color: (stat['isPositive'] as bool) ? Colors.greenAccent : Colors.redAccent,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingStatsCards(BuildContext context) {
    final crossAxisCount = MerchantTheme.getResponsiveGridCrossAxisCount(context);
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: MerchantTheme.spacing24,
        mainAxisSpacing: MerchantTheme.spacing24,
        childAspectRatio: 1.2,
      ),
      itemCount: 4,
      itemBuilder: (context, index) => Container(
        decoration: BoxDecoration(
          color: MerchantTheme.neutral100,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
          boxShadow: MerchantTheme.cardShadow,
        ),
        padding: const EdgeInsets.all(MerchantTheme.spacing24),
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildErrorCard(String message) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing24),
      decoration: BoxDecoration(
        color: MerchantTheme.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: MerchantTheme.errorRed),
          const SizedBox(width: MerchantTheme.spacing12),
          Expanded(child: Text(message, style: MerchantTheme.bodyLarge)),
        ],
      ),
    );
  }

  Widget _buildChartsSection(DashboardAnalytics analytics) {
    // Placeholder for charts (implement with fl_chart or similar)
    return Container(
      height: 220,
      margin: const EdgeInsets.only(top: MerchantTheme.spacing24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: const Center(
        child: Text('Charts and Trends Coming Soon!', style: MerchantTheme.bodyLarge),
      ),
    );
  }

  Widget _buildLoadingChartsSection() {
    return Container(
      height: 220,
      margin: const EdgeInsets.only(top: MerchantTheme.spacing24),
      decoration: BoxDecoration(
        color: MerchantTheme.neutral100,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context) {
    final actions = [
      {
        'title': 'Add Product',
        'subtitle': 'Add new inventory item',
        'icon': Icons.add_box,
        'color': MerchantTheme.primaryBlue,
        'onTap': () {
          // TODO: Implement add product dialog
        },
      },
      {
        'title': 'Process Order',
        'subtitle': 'Handle pending orders',
        'icon': Icons.assignment_turned_in,
        'color': MerchantTheme.successGreen,
        'onTap': () {
          // TODO: Implement process order
        },
      },
      {
        'title': 'View Reports',
        'subtitle': 'Check analytics & reports',
        'icon': Icons.analytics,
        'color': MerchantTheme.secondaryBlue,
        'onTap': () {
          // TODO: Implement view reports
        },
      },
      {
        'title': 'Manage Suppliers',
        'subtitle': 'Update supplier info',
        'icon': Icons.people,
        'color': MerchantTheme.warningOrange,
        'onTap': () {
          // TODO: Implement manage suppliers
        },
      },
    ];

    final crossAxisCount = MerchantTheme.getResponsiveGridCrossAxisCount(context);
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: MerchantTheme.spacing24,
        mainAxisSpacing: MerchantTheme.spacing24,
        childAspectRatio: 2.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return GestureDetector(
          onTap: action['onTap'] as VoidCallback,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
              boxShadow: MerchantTheme.cardShadow,
            ),
            padding: const EdgeInsets.all(MerchantTheme.spacing24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(MerchantTheme.spacing12),
                  decoration: BoxDecoration(
                    color: (action['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
                  ),
                  child: Icon(action['icon'] as IconData, color: action['color'] as Color, size: 28),
                ),
                const SizedBox(width: MerchantTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(action['title'] as String, style: MerchantTheme.headline4),
                      const SizedBox(height: MerchantTheme.spacing4),
                      Text(action['subtitle'] as String, style: MerchantTheme.bodySmall),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentOrdersCard(List<OrderModel> orders) {
    // Placeholder for recent orders (implement with real data)
    return Container(
      height: 220,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: const Center(
        child: Text('Recent Orders Coming Soon!', style: MerchantTheme.bodyLarge),
      ),
    );
  }
} 