import 'package:cloud_firestore/cloud_firestore.dart';

enum SupplierStatus {
  active('active', 'Active'),
  inactive('inactive', 'Inactive'),
  suspended('suspended', 'Suspended'),
  pending('pending', 'Pending Approval');

  const SupplierStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static SupplierStatus fromString(String value) {
    return SupplierStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => SupplierStatus.pending,
    );
  }

  bool get isActive => this == SupplierStatus.active;
  bool get canPlaceOrders => this == SupplierStatus.active;
}

class SupplierModel {
  final String id;
  final String name;
  final String contactPerson;
  final String email;
  final String phone;
  final String? alternatePhone;
  final String address;
  final String? website;
  final String merchantId;
  final String mallId;
  final SupplierStatus status;
  final List<String> productCategories;
  final String? taxId;
  final String? registrationNumber;
  final Map<String, dynamic> paymentTerms; // e.g., {"days": 30, "type": "net"}
  final double creditLimit;
  final double currentBalance;
  final double totalPurchases;
  final int totalOrders;
  final double averageRating;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final Map<String, dynamic>? metadata;

  SupplierModel({
    required this.id,
    required this.name,
    required this.contactPerson,
    required this.email,
    required this.phone,
    this.alternatePhone,
    required this.address,
    this.website,
    required this.merchantId,
    required this.mallId,
    this.status = SupplierStatus.pending,
    this.productCategories = const [],
    this.taxId,
    this.registrationNumber,
    this.paymentTerms = const {"days": 30, "type": "net"},
    this.creditLimit = 0.0,
    this.currentBalance = 0.0,
    this.totalPurchases = 0.0,
    this.totalOrders = 0,
    this.averageRating = 0.0,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.metadata,
  });

  bool get hasOutstandingBalance => currentBalance > 0;
  bool get isOverCreditLimit => currentBalance > creditLimit;
  double get availableCredit => creditLimit - currentBalance;
  double get averageOrderValue => totalOrders > 0 ? totalPurchases / totalOrders : 0.0;

  factory SupplierModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SupplierModel(
      id: doc.id,
      name: data['name'] ?? '',
      contactPerson: data['contactPerson'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'] ?? '',
      alternatePhone: data['alternatePhone'],
      address: data['address'] ?? '',
      website: data['website'],
      merchantId: data['merchantId'] ?? '',
      mallId: data['mallId'] ?? '',
      status: SupplierStatus.fromString(data['status'] ?? 'pending'),
      productCategories: List<String>.from(data['productCategories'] ?? []),
      taxId: data['taxId'],
      registrationNumber: data['registrationNumber'],
      paymentTerms: Map<String, dynamic>.from(data['paymentTerms'] ?? {"days": 30, "type": "net"}),
      creditLimit: (data['creditLimit'] ?? 0.0).toDouble(),
      currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
      totalPurchases: (data['totalPurchases'] ?? 0.0).toDouble(),
      totalOrders: data['totalOrders'] ?? 0,
      averageRating: (data['averageRating'] ?? 0.0).toDouble(),
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'contactPerson': contactPerson,
      'email': email,
      'phone': phone,
      'alternatePhone': alternatePhone,
      'address': address,
      'website': website,
      'merchantId': merchantId,
      'mallId': mallId,
      'status': status.value,
      'productCategories': productCategories,
      'taxId': taxId,
      'registrationNumber': registrationNumber,
      'paymentTerms': paymentTerms,
      'creditLimit': creditLimit,
      'currentBalance': currentBalance,
      'totalPurchases': totalPurchases,
      'totalOrders': totalOrders,
      'averageRating': averageRating,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }

  SupplierModel copyWith({
    String? name,
    String? contactPerson,
    String? email,
    String? phone,
    String? alternatePhone,
    String? address,
    String? website,
    SupplierStatus? status,
    List<String>? productCategories,
    String? taxId,
    String? registrationNumber,
    Map<String, dynamic>? paymentTerms,
    double? creditLimit,
    double? currentBalance,
    double? totalPurchases,
    int? totalOrders,
    double? averageRating,
    String? notes,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return SupplierModel(
      id: id,
      name: name ?? this.name,
      contactPerson: contactPerson ?? this.contactPerson,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      alternatePhone: alternatePhone ?? this.alternatePhone,
      address: address ?? this.address,
      website: website ?? this.website,
      merchantId: merchantId,
      mallId: mallId,
      status: status ?? this.status,
      productCategories: productCategories ?? this.productCategories,
      taxId: taxId ?? this.taxId,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      totalPurchases: totalPurchases ?? this.totalPurchases,
      totalOrders: totalOrders ?? this.totalOrders,
      averageRating: averageRating ?? this.averageRating,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy,
      metadata: metadata ?? this.metadata,
    );
  }
}
