import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../firebase_options.dart';

class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();

  FirebaseService._();

  // Firebase instances
  FirebaseAuth get auth => FirebaseAuth.instance;
  FirebaseFirestore get firestore => FirebaseFirestore.instance;
  FirebaseStorage get storage => FirebaseStorage.instance;

  // Initialize Firebase
  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Enable offline persistence for Firestore
    FirebaseFirestore.instance.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
  }

  // Auth state stream
  Stream<User?> get authStateChanges => auth.authStateChanges();

  // Current user
  User? get currentUser => auth.currentUser;
  bool get isSignedIn => currentUser != null;
  String? get currentUserId => currentUser?.uid;

  // Collections
  CollectionReference get usersCollection => firestore.collection('users');

  CollectionReference get merchantsCollection =>
      firestore.collection('merchants');

  CollectionReference get productsCollection =>
      firestore.collection('products');

  CollectionReference get transactionsCollection =>
      firestore.collection('transactions');

  CollectionReference get receiptsCollection =>
      firestore.collection('receipts');

  CollectionReference get mallsCollection => firestore.collection('malls');

  CollectionReference get salesRepsCollection =>
      firestore.collection('salesReps');

  CollectionReference get adminsCollection => firestore.collection('admins');

  // Get products for a specific merchant and mall
  Query getProductsQuery(String merchantId, String mallId) {
    return productsCollection
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('isActive', isEqualTo: true);
  }

  // Get transactions for a specific mall
  Query getTransactionsQuery(String mallId,
      {DateTime? startDate, DateTime? endDate}) {
    Query query = transactionsCollection.where('mallId', isEqualTo: mallId);

    if (startDate != null) {
      query = query.where('createdAt', isGreaterThanOrEqualTo: startDate);
    }

    if (endDate != null) {
      query = query.where('createdAt', isLessThanOrEqualTo: endDate);
    }

    return query.orderBy('createdAt', descending: true);
  }

  // Get sales reps for a specific merchant
  Query getSalesRepsQuery(String merchantId) {
    return usersCollection
        .where('role', isEqualTo: 'sales_rep')
        .where('merchantId', isEqualTo: merchantId)
        .where('isActive', isEqualTo: true);
  }

  // Get merchants for a specific mall
  Query getMerchantsQuery(String mallId) {
    return usersCollection
        .where('role', isEqualTo: 'merchant')
        .where('mallId', isEqualTo: mallId)
        .where('isActive', isEqualTo: true);
  }

  // Batch operations
  WriteBatch batch() => firestore.batch();

  // Transaction operations
  Future<T> runTransaction<T>(
    Future<T> Function(Transaction transaction) updateFunction,
  ) {
    return firestore.runTransaction(updateFunction);
  }

  // Storage references
  Reference getProductImageRef(String productId) {
    return storage.ref().child('product_images/$productId');
  }

  Reference getReceiptImageRef(String receiptId) {
    return storage.ref().child('receipt_images/$receiptId');
  }

  Reference getProfileImageRef(String userId) {
    return storage.ref().child('profile_images/$userId');
  }

  // Error handling
  String getFirebaseErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'No user found with this email address.';
        case 'wrong-password':
          return 'Incorrect password.';
        case 'email-already-in-use':
          return 'An account already exists with this email address.';
        case 'weak-password':
          return 'Password is too weak.';
        case 'invalid-email':
          return 'Invalid email address.';
        case 'user-disabled':
          return 'This account has been disabled.';
        case 'too-many-requests':
          return 'Too many requests. Please try again later.';
        default:
          return error.message ?? 'An authentication error occurred.';
      }
    } else if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return 'You do not have permission to perform this action.';
        case 'unavailable':
          return 'Service is currently unavailable. Please try again later.';
        case 'deadline-exceeded':
          return 'Request timed out. Please try again.';
        default:
          return error.message ?? 'A Firebase error occurred.';
      }
    }
    return error.toString();
  }
}
