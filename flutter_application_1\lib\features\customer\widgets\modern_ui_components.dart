import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/themes/customer_theme.dart';

/// Modern UI components for customer mobile app
/// Inspired by successful fintech apps like OPay, PayPal, Revolut

class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final Gradient? gradient;
  final Color? backgroundColor;
  final double borderRadius;
  final List<BoxShadow>? boxShadow;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.gradient,
    this.backgroundColor,
    this.borderRadius = CustomerTheme.mediumRadius,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(CustomerTheme.spacingS),
      decoration: gradient != null
          ? CustomerTheme.getGradientDecoration(
              gradient: gradient!,
              borderRadius: borderRadius,
              boxShadow: boxShadow ?? CustomerTheme.cardShadow,
            )
          : CustomerTheme.getCardDecoration(
              color: backgroundColor,
              borderRadius: borderRadius,
              boxShadow: boxShadow ?? CustomerTheme.cardShadow,
            ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(CustomerTheme.spacingM),
            child: child,
          ),
        ),
      ),
    );
  }
}

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isOutlined;
  final Gradient? gradient;
  final Color? backgroundColor;
  final Color? textColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
    this.gradient,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = CustomerTheme.mediumRadius,
    this.padding,
    this.width,
    this.height,
  });

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CustomerTheme.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.onPressed != null ? _onTapDown : null,
            onTapUp: widget.onPressed != null ? _onTapUp : null,
            onTapCancel: _onTapCancel,
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Container(
              width: widget.width,
              height: widget.height ?? 48,
              padding: widget.padding ??
                  const EdgeInsets.symmetric(
                    horizontal: CustomerTheme.spacingL,
                    vertical: CustomerTheme.spacingM,
                  ),
              decoration: widget.isOutlined
                  ? BoxDecoration(
                      border: Border.all(
                        color:
                            widget.backgroundColor ?? CustomerTheme.primaryTeal,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                    )
                  : (widget.gradient != null
                      ? CustomerTheme.getGradientDecoration(
                          gradient: widget.gradient!,
                          borderRadius: widget.borderRadius,
                        )
                      : BoxDecoration(
                          color: widget.backgroundColor ??
                              CustomerTheme.primaryTeal,
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                        )),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.textColor ?? Colors.white,
                        ),
                      ),
                    )
                  else if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.isOutlined
                          ? (widget.backgroundColor ??
                              CustomerTheme.primaryTeal)
                          : (widget.textColor ?? Colors.white),
                      size: 20,
                    ),
                    const SizedBox(width: CustomerTheme.spacingS),
                  ],
                  if (!widget.isLoading)
                    Text(
                      widget.text,
                      style: CustomerTheme.labelLarge.copyWith(
                        color: widget.isOutlined
                            ? (widget.backgroundColor ??
                                CustomerTheme.primaryTeal)
                            : (widget.textColor ?? Colors.white),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class ModernMetricCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color iconColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  const ModernMetricCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.iconColor,
    this.backgroundColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      onTap: onTap,
      backgroundColor: backgroundColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(CustomerTheme.spacingS),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(CustomerTheme.smallRadius),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 20,
                ),
              ),
              const Spacer(),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: CustomerTheme.textTertiary,
                ),
            ],
          ),
          const SizedBox(height: CustomerTheme.spacingM),
          Text(
            title,
            style: CustomerTheme.bodySmall.copyWith(
              color: CustomerTheme.textSecondary,
            ),
          ),
          const SizedBox(height: CustomerTheme.spacingXS),
          Text(
            value,
            style: CustomerTheme.headingSmall.copyWith(
              color: CustomerTheme.textPrimary,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: CustomerTheme.spacingXS),
            Text(
              subtitle!,
              style: CustomerTheme.bodySmall.copyWith(
                color: CustomerTheme.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class ModernSearchBar extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const ModernSearchBar({
    super.key,
    required this.hintText,
    this.controller,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: CustomerTheme.getCardDecoration(),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: CustomerTheme.bodyMedium.copyWith(
            color: CustomerTheme.textTertiary,
          ),
          prefixIcon: prefixIcon ??
              Icon(
                Icons.search,
                color: CustomerTheme.textTertiary,
              ),
          suffixIcon: suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacingM,
            vertical: CustomerTheme.spacingM,
          ),
        ),
      ),
    );
  }
}

/// Enhanced glassmorphism card with blur effects
class GlassmorphismCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final double borderRadius;
  final double blurIntensity;
  final Color? backgroundColor;
  final Border? border;

  const GlassmorphismCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.borderRadius = CustomerTheme.mediumRadius,
    this.blurIntensity = 10.0,
    this.backgroundColor,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(CustomerTheme.spacingS),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        border: border ??
            Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
        boxShadow: CustomerTheme.elevatedShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(borderRadius),
              child: Padding(
                padding:
                    padding ?? const EdgeInsets.all(CustomerTheme.spacingM),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Animated floating action button with modern design
class ModernFloatingActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final String? tooltip;
  final Gradient? gradient;
  final Color? backgroundColor;
  final double size;

  const ModernFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.gradient,
    this.backgroundColor,
    this.size = 56.0,
  });

  @override
  State<ModernFloatingActionButton> createState() =>
      _ModernFloatingActionButtonState();
}

class _ModernFloatingActionButtonState extends State<ModernFloatingActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown() {
    _controller.forward();
  }

  void _onTapUp() {
    _controller.reverse();
    widget.onPressed();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: GestureDetector(
              onTapDown: (_) => _onTapDown(),
              onTapUp: (_) => _onTapUp(),
              onTapCancel: () => _controller.reverse(),
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: widget.gradient != null
                    ? CustomerTheme.getGradientDecoration(
                        gradient: widget.gradient!,
                        borderRadius: widget.size / 2,
                        boxShadow: CustomerTheme.elevatedShadow,
                      )
                    : BoxDecoration(
                        color:
                            widget.backgroundColor ?? CustomerTheme.primaryTeal,
                        borderRadius: BorderRadius.circular(widget.size / 2),
                        boxShadow: CustomerTheme.elevatedShadow,
                      ),
                child: Icon(
                  widget.icon,
                  color: Colors.white,
                  size: widget.size * 0.4,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Modern progress indicator with gradient
class ModernProgressIndicator extends StatefulWidget {
  final double value;
  final Color? backgroundColor;
  final Gradient? gradient;
  final double height;
  final double borderRadius;
  final String? label;
  final bool showPercentage;

  const ModernProgressIndicator({
    super.key,
    required this.value,
    this.backgroundColor,
    this.gradient,
    this.height = 8.0,
    this.borderRadius = 4.0,
    this.label,
    this.showPercentage = false,
  });

  @override
  State<ModernProgressIndicator> createState() =>
      _ModernProgressIndicatorState();
}

class _ModernProgressIndicatorState extends State<ModernProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CustomerTheme.slowAnimation,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _controller.forward();
  }

  @override
  void didUpdateWidget(ModernProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.value,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null || widget.showPercentage) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (widget.label != null)
                Text(
                  widget.label!,
                  style: CustomerTheme.bodySmall.copyWith(
                    color: CustomerTheme.textSecondary,
                  ),
                ),
              if (widget.showPercentage)
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return Text(
                      '${(_animation.value * 100).toInt()}%',
                      style: CustomerTheme.bodySmall.copyWith(
                        color: CustomerTheme.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  },
                ),
            ],
          ),
          const SizedBox(height: CustomerTheme.spacingXS),
        ],
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? CustomerTheme.borderLight,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _animation.value,
                child: Container(
                  decoration: widget.gradient != null
                      ? CustomerTheme.getGradientDecoration(
                          gradient: widget.gradient!,
                          borderRadius: widget.borderRadius,
                        )
                      : BoxDecoration(
                          color: CustomerTheme.primaryTeal,
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                        ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Professional loading states and feedback components
class ModernLoadingIndicator extends StatefulWidget {
  final String? message;
  final Color? color;
  final double size;
  final bool showMessage;

  const ModernLoadingIndicator({
    super.key,
    this.message,
    this.color,
    this.size = 40.0,
    this.showMessage = true,
  });

  @override
  State<ModernLoadingIndicator> createState() => _ModernLoadingIndicatorState();
}

class _ModernLoadingIndicatorState extends State<ModernLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CustomerTheme.slowAnimation,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _animation.value * 2 * 3.14159,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      widget.color ?? CustomerTheme.primaryTeal,
                      (widget.color ?? CustomerTheme.primaryTeal)
                          .withOpacity(0.3),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(widget.size / 2),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(3),
                  child: Container(
                    decoration: BoxDecoration(
                      color: CustomerTheme.surfaceWhite,
                      borderRadius:
                          BorderRadius.circular((widget.size - 6) / 2),
                    ),
                    child: Icon(
                      Icons.refresh,
                      color: widget.color ?? CustomerTheme.primaryTeal,
                      size: widget.size * 0.4,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        if (widget.showMessage && widget.message != null) ...[
          SizedBox(height: CustomerTheme.spacing12),
          Text(
            widget.message!,
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Success feedback component
class ModernSuccessIndicator extends StatefulWidget {
  final String message;
  final VoidCallback? onDismiss;
  final Duration displayDuration;

  const ModernSuccessIndicator({
    super.key,
    required this.message,
    this.onDismiss,
    this.displayDuration = const Duration(seconds: 3),
  });

  @override
  State<ModernSuccessIndicator> createState() => _ModernSuccessIndicatorState();
}

class _ModernSuccessIndicatorState extends State<ModernSuccessIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward();

    // Auto dismiss
    Future.delayed(widget.displayDuration, () {
      if (mounted) {
        _controller.reverse().then((_) {
          widget.onDismiss?.call();
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              padding: EdgeInsets.all(CustomerTheme.spacing16),
              decoration: CustomerTheme.getGradientDecoration(
                gradient: CustomerTheme.successGradient,
                boxShadow: CustomerTheme.elevatedShadow,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                  SizedBox(width: CustomerTheme.spacing12),
                  Flexible(
                    child: Text(
                      widget.message,
                      style: CustomerTheme.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
