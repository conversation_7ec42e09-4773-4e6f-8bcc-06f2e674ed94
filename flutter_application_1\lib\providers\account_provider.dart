import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/user_model.dart';
import '../models/user_preferences_model.dart';
import '../models/account_settings_model.dart';
import '../services/profile_service.dart';
import 'auth_provider.dart';

// User preferences provider
final userPreferencesProvider = FutureProvider.family<UserPreferencesModel?, String?>((ref, userId) async {
  if (userId == null) return null;
  
  try {
    return await ProfileService.getUserPreferences(userId);
  } catch (e) {
    throw Exception('Failed to load user preferences: $e');
  }
});

// Account settings provider
final accountSettingsProvider = FutureProvider.family<AccountSettingsModel?, String?>((ref, userId) async {
  if (userId == null) return null;
  
  try {
    return await ProfileService.getAccountSettings(userId);
  } catch (e) {
    throw Exception('Failed to load account settings: $e');
  }
});

// Current user preferences provider
final currentUserPreferencesProvider = FutureProvider<UserPreferencesModel?>((ref) async {
  final userId = ref.watch(currentUserIdProvider);
  if (userId == null) return null;
  
  return ref.watch(userPreferencesProvider(userId)).value;
});

// Current user account settings provider
final currentUserAccountSettingsProvider = FutureProvider<AccountSettingsModel?>((ref) async {
  final userId = ref.watch(currentUserIdProvider);
  if (userId == null) return null;
  
  return ref.watch(accountSettingsProvider(userId)).value;
});

// Account controller for managing account operations
class AccountController extends StateNotifier<AsyncValue<void>> {
  AccountController(this.ref) : super(const AsyncValue.data(null));
  
  final Ref ref;

  // Update user profile
  Future<void> updateProfile({
    required String userId,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.updateUserProfile(
        userId: userId,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
      );
      
      // Invalidate current user provider to refresh data
      ref.invalidate(currentUserProvider);
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Upload profile image
  Future<void> uploadProfileImage({
    required String userId,
    required File imageFile,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final imageUrl = await ProfileService.uploadProfileImage(
        userId: userId,
        imageFile: imageFile,
      );
      
      await ProfileService.updateUserProfile(
        userId: userId,
        profileImageUrl: imageUrl,
      );
      
      // Invalidate current user provider to refresh data
      ref.invalidate(currentUserProvider);
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Remove profile image
  Future<void> removeProfileImage(String userId) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.deleteProfileImage(userId);
      
      await ProfileService.updateUserProfile(
        userId: userId,
        profileImageUrl: null,
      );
      
      // Invalidate current user provider to refresh data
      ref.invalidate(currentUserProvider);
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update user preferences
  Future<void> updatePreferences({
    required String userId,
    required UserPreferencesModel preferences,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.updateUserPreferences(
        userId: userId,
        preferences: preferences,
      );
      
      // Invalidate preferences provider to refresh data
      ref.invalidate(userPreferencesProvider(userId));
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update account settings
  Future<void> updateAccountSettings({
    required String userId,
    required AccountSettingsModel settings,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.updateAccountSettings(
        userId: userId,
        settings: settings,
      );
      
      // Invalidate settings provider to refresh data
      ref.invalidate(accountSettingsProvider(userId));
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      
      // Invalidate account settings to refresh last password change
      final userId = ref.read(currentUserIdProvider);
      if (userId != null) {
        ref.invalidate(accountSettingsProvider(userId));
      }
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.sendPasswordResetEmail(email);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Request account deactivation
  Future<void> requestAccountDeactivation(String userId) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.requestAccountDeactivation(userId);
      
      // Invalidate settings provider to refresh data
      ref.invalidate(accountSettingsProvider(userId));
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Cancel account deactivation
  Future<void> cancelAccountDeactivation(String userId) async {
    state = const AsyncValue.loading();
    
    try {
      await ProfileService.cancelAccountDeactivation(userId);
      
      // Invalidate settings provider to refresh data
      ref.invalidate(accountSettingsProvider(userId));
      
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Export user data
  Future<Map<String, dynamic>> exportUserData(String userId) async {
    try {
      return await ProfileService.exportUserData(userId);
    } catch (e) {
      rethrow;
    }
  }

  // Update security review
  Future<void> updateSecurityReview(String userId) async {
    try {
      await ProfileService.updateSecurityReview(userId);
      
      // Invalidate settings provider to refresh data
      ref.invalidate(accountSettingsProvider(userId));
    } catch (e) {
      rethrow;
    }
  }
}

// Account controller provider
final accountControllerProvider = StateNotifierProvider<AccountController, AsyncValue<void>>((ref) {
  return AccountController(ref);
});

// Helper providers for specific preferences
final themePreferenceProvider = Provider<ThemeMode>((ref) {
  final preferences = ref.watch(currentUserPreferencesProvider);
  return preferences.when(
    data: (prefs) => prefs?.themeMode ?? ThemeMode.system,
    loading: () => ThemeMode.system,
    error: (_, __) => ThemeMode.system,
  );
});

final notificationsEnabledProvider = Provider<bool>((ref) {
  final preferences = ref.watch(currentUserPreferencesProvider);
  return preferences.when(
    data: (prefs) => prefs?.notificationsEnabled ?? true,
    loading: () => true,
    error: (_, __) => true,
  );
});

final languagePreferenceProvider = Provider<Language>((ref) {
  final preferences = ref.watch(currentUserPreferencesProvider);
  return preferences.when(
    data: (prefs) => prefs?.language ?? Language.english,
    loading: () => Language.english,
    error: (_, __) => Language.english,
  );
});

// Security status providers
final needsSecurityReviewProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentUserAccountSettingsProvider);
  return settings.when(
    data: (accountSettings) => accountSettings?.needsSecurityReview ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final needsPasswordChangeProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentUserAccountSettingsProvider);
  return settings.when(
    data: (accountSettings) => accountSettings?.needsPasswordChange ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final twoFactorEnabledProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentUserAccountSettingsProvider);
  return settings.when(
    data: (accountSettings) => accountSettings?.twoFactorEnabled ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});
