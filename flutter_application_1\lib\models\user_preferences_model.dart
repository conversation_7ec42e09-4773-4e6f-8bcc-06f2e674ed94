import 'package:cloud_firestore/cloud_firestore.dart';

enum ThemeMode { light, dark, system }
enum Language { english, spanish, french }

class UserPreferencesModel {
  final String userId;
  final ThemeMode themeMode;
  final Language language;
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool pushNotifications;
  final bool smsNotifications;
  final bool marketingEmails;
  final bool orderUpdates;
  final bool promotionalOffers;
  final bool securityAlerts;
  final bool locationServices;
  final bool dataAnalytics;
  final bool personalizedAds;
  final double fontSize;
  final bool highContrast;
  final bool reduceAnimations;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserPreferencesModel({
    required this.userId,
    this.themeMode = ThemeMode.system,
    this.language = Language.english,
    this.notificationsEnabled = true,
    this.emailNotifications = true,
    this.pushNotifications = true,
    this.smsNotifications = false,
    this.marketingEmails = false,
    this.orderUpdates = true,
    this.promotionalOffers = false,
    this.securityAlerts = true,
    this.locationServices = false,
    this.dataAnalytics = false,
    this.personalizedAds = false,
    this.fontSize = 1.0,
    this.highContrast = false,
    this.reduceAnimations = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserPreferencesModel.defaultPreferences(String userId) {
    final now = DateTime.now();
    return UserPreferencesModel(
      userId: userId,
      createdAt: now,
      updatedAt: now,
    );
  }

  factory UserPreferencesModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserPreferencesModel(
      userId: doc.id,
      themeMode: ThemeMode.values.firstWhere(
        (e) => e.name == data['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      language: Language.values.firstWhere(
        (e) => e.name == data['language'],
        orElse: () => Language.english,
      ),
      notificationsEnabled: data['notificationsEnabled'] ?? true,
      emailNotifications: data['emailNotifications'] ?? true,
      pushNotifications: data['pushNotifications'] ?? true,
      smsNotifications: data['smsNotifications'] ?? false,
      marketingEmails: data['marketingEmails'] ?? false,
      orderUpdates: data['orderUpdates'] ?? true,
      promotionalOffers: data['promotionalOffers'] ?? false,
      securityAlerts: data['securityAlerts'] ?? true,
      locationServices: data['locationServices'] ?? false,
      dataAnalytics: data['dataAnalytics'] ?? false,
      personalizedAds: data['personalizedAds'] ?? false,
      fontSize: (data['fontSize'] ?? 1.0).toDouble(),
      highContrast: data['highContrast'] ?? false,
      reduceAnimations: data['reduceAnimations'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'themeMode': themeMode.name,
      'language': language.name,
      'notificationsEnabled': notificationsEnabled,
      'emailNotifications': emailNotifications,
      'pushNotifications': pushNotifications,
      'smsNotifications': smsNotifications,
      'marketingEmails': marketingEmails,
      'orderUpdates': orderUpdates,
      'promotionalOffers': promotionalOffers,
      'securityAlerts': securityAlerts,
      'locationServices': locationServices,
      'dataAnalytics': dataAnalytics,
      'personalizedAds': personalizedAds,
      'fontSize': fontSize,
      'highContrast': highContrast,
      'reduceAnimations': reduceAnimations,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  UserPreferencesModel copyWith({
    ThemeMode? themeMode,
    Language? language,
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
    bool? marketingEmails,
    bool? orderUpdates,
    bool? promotionalOffers,
    bool? securityAlerts,
    bool? locationServices,
    bool? dataAnalytics,
    bool? personalizedAds,
    double? fontSize,
    bool? highContrast,
    bool? reduceAnimations,
    DateTime? updatedAt,
  }) {
    return UserPreferencesModel(
      userId: userId,
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      marketingEmails: marketingEmails ?? this.marketingEmails,
      orderUpdates: orderUpdates ?? this.orderUpdates,
      promotionalOffers: promotionalOffers ?? this.promotionalOffers,
      securityAlerts: securityAlerts ?? this.securityAlerts,
      locationServices: locationServices ?? this.locationServices,
      dataAnalytics: dataAnalytics ?? this.dataAnalytics,
      personalizedAds: personalizedAds ?? this.personalizedAds,
      fontSize: fontSize ?? this.fontSize,
      highContrast: highContrast ?? this.highContrast,
      reduceAnimations: reduceAnimations ?? this.reduceAnimations,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

extension ThemeModeExtension on ThemeMode {
  String get displayName {
    switch (this) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }
}

extension LanguageExtension on Language {
  String get displayName {
    switch (this) {
      case Language.english:
        return 'English';
      case Language.spanish:
        return 'Español';
      case Language.french:
        return 'Français';
    }
  }

  String get code {
    switch (this) {
      case Language.english:
        return 'en';
      case Language.spanish:
        return 'es';
      case Language.french:
        return 'fr';
    }
  }
}
