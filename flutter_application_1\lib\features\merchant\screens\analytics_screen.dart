import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/analytics_model.dart';
import '../../../providers/analytics_provider.dart';
import '../../../shared/themes/merchant_theme.dart';

/// Professional Analytics & Reports Screen
/// Features: Sales metrics, revenue charts, performance indicators,
/// responsive design, and modern UI components
class AnalyticsScreen extends ConsumerStatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  ConsumerState<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends ConsumerState<AnalyticsScreen>
    with TickerProviderStateMixin {
  String _selectedPeriod = 'Last 30 Days';
  String _selectedMetric = 'Revenue';

  late AnimationController _pageController;
  late Animation<double> _pageAnimation;

  final List<String> _periodOptions = [
    'Last 7 Days',
    'Last 30 Days',
    'Last 90 Days',
    'This Year',
    'Custom Range',
  ];

  final List<String> _metricOptions = [
    'Revenue',
    'Orders',
    'Products',
    'Customers',
  ];

  @override
  void initState() {
    super.initState();
    _pageController = AnimationController(
      duration: MerchantTheme.slowAnimation,
      vsync: this,
    );
    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeOutCubic,
    );
    _pageController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final analyticsAsync = ref.watch(dashboardAnalyticsProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final isWide = screenWidth > 1200;
    final isTablet = screenWidth > 900 && screenWidth <= 1200;
    final isMobile = screenWidth <= 900;

    return Theme(
      data: MerchantTheme.lightTheme,
      child: Scaffold(
        backgroundColor: MerchantTheme.neutral50,
        body: FadeTransition(
          opacity: _pageAnimation,
          child: CustomScrollView(
            slivers: [
              // Enhanced Header
              SliverToBoxAdapter(
                child: _buildEnhancedHeader(isWide, isTablet, isMobile),
              ),

              // Enhanced Analytics Content
              SliverToBoxAdapter(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1600),
                  margin: EdgeInsets.symmetric(
                    horizontal: _getContentPadding(isWide, isTablet, isMobile),
                  ),
                  child: analyticsAsync.when(
                    data: (analytics) => _buildEnhancedAnalyticsContent(
                        analytics, isWide, isTablet, isMobile),
                    loading: () => _buildLoadingSection(),
                    error: (error, stack) =>
                        _buildErrorSection(error.toString()),
                  ),
                ),
              ),

              const SliverToBoxAdapter(
                child: SizedBox(height: MerchantTheme.spacing64),
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getContentPadding(bool isWide, bool isTablet, bool isMobile) {
    if (isMobile) return MerchantTheme.spacing16;
    if (isTablet) return MerchantTheme.spacing32;
    return MerchantTheme.spacing48;
  }

  Widget _buildEnhancedHeader(bool isWide, bool isTablet, bool isMobile) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        _getContentPadding(isWide, isTablet, isMobile),
        isWide ? MerchantTheme.spacing48 : MerchantTheme.spacing32,
        _getContentPadding(isWide, isTablet, isMobile),
        MerchantTheme.spacing24,
      ),
      decoration: const BoxDecoration(
        gradient: MerchantTheme.primaryGradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(MerchantTheme.radiusLarge),
          bottomRight: Radius.circular(MerchantTheme.radiusLarge),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          if (isWide || isTablet)
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(MerchantTheme.spacing12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius:
                        BorderRadius.circular(MerchantTheme.radiusMedium),
                  ),
                  child: const Icon(Icons.analytics,
                      color: Colors.white, size: 32),
                ),
                const SizedBox(width: MerchantTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Analytics & Reports',
                        style: (isWide
                                ? MerchantTheme.headline1
                                : MerchantTheme.headline2)
                            .copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: MerchantTheme.spacing4),
                      Text(
                        'Comprehensive business insights and performance metrics',
                        style: MerchantTheme.bodyLarge
                            .copyWith(color: Colors.white.withOpacity(0.9)),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: MerchantTheme.spacing24),
                _buildEnhancedExportButton(isWide),
              ],
            )
          else
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(MerchantTheme.spacing8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius:
                            BorderRadius.circular(MerchantTheme.radiusMedium),
                      ),
                      child: const Icon(Icons.analytics,
                          color: Colors.white, size: 24),
                    ),
                    const SizedBox(width: MerchantTheme.spacing12),
                    Expanded(
                      child: Text(
                        'Analytics & Reports',
                        style: MerchantTheme.headline3.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: MerchantTheme.spacing8),
                Text(
                  'Business insights and metrics',
                  style: MerchantTheme.bodyMedium
                      .copyWith(color: Colors.white.withOpacity(0.9)),
                ),
                const SizedBox(height: MerchantTheme.spacing16),
                _buildEnhancedExportButton(false),
              ],
            ),

          const SizedBox(height: MerchantTheme.spacing24),

          // Enhanced Period Selector
          _buildEnhancedPeriodSelector(isWide, isTablet),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Row(
      children: [
        Text(
          'Period:',
          style: MerchantTheme.bodyLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: MerchantTheme.spacing16),
        Expanded(
          child: Container(
            padding:
                const EdgeInsets.symmetric(horizontal: MerchantTheme.spacing16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedPeriod,
                onChanged: (value) => setState(() => _selectedPeriod = value!),
                items: _periodOptions
                    .map((period) => DropdownMenuItem(
                          value: period,
                          child: Text(
                            period,
                            style: MerchantTheme.bodyMedium
                                .copyWith(color: Colors.white),
                          ),
                        ))
                    .toList(),
                icon:
                    const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                isExpanded: true,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyticsContent(DashboardAnalytics analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Key Metrics Cards
        _buildKeyMetrics(analytics),
        const SizedBox(height: MerchantTheme.spacing32),

        // Charts Section
        _buildChartsSection(analytics),
        const SizedBox(height: MerchantTheme.spacing32),

        // Performance Indicators
        _buildPerformanceIndicators(analytics),
        const SizedBox(height: MerchantTheme.spacing32),

        // Recent Activity
        _buildRecentActivity(analytics),
      ],
    );
  }

  Widget _buildKeyMetrics(DashboardAnalytics analytics) {
    final sales = analytics.salesMetrics;
    final inventory = analytics.inventoryMetrics;
    final customers = analytics.customerMetrics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Metrics',
          style: MerchantTheme.headline3.copyWith(
            color: MerchantTheme.neutral800,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: MerchantTheme.spacing16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount:
              MerchantTheme.getResponsiveGridCrossAxisCount(context),
          crossAxisSpacing: MerchantTheme.spacing16,
          mainAxisSpacing: MerchantTheme.spacing16,
          childAspectRatio: 1.5,
          children: [
            _buildMetricCard(
              'Total Revenue',
              '₦${sales.totalRevenue.toStringAsFixed(0)}',
              Icons.attach_money,
              MerchantTheme.successGreen,
              '+12.5%', // Demo growth
            ),
            _buildMetricCard(
              'Total Orders',
              sales.totalOrders.toString(),
              Icons.shopping_cart,
              MerchantTheme.primaryBlue,
              '+8.3%', // Demo growth
            ),
            _buildMetricCard(
              'Active Products',
              inventory.totalProducts.toString(),
              Icons.inventory_2,
              MerchantTheme.secondaryBlue,
              '+15.2%', // Demo growth
            ),
            _buildMetricCard(
              'Total Customers',
              customers.totalCustomers.toString(),
              Icons.people,
              MerchantTheme.warningOrange,
              '+22.1%', // Demo growth
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(
      String title, String value, IconData icon, Color color, String growth) {
    final isPositive = growth.startsWith('+');

    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(MerchantTheme.spacing8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(MerchantTheme.radiusSmall),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: MerchantTheme.spacing8,
                  vertical: MerchantTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: (isPositive
                          ? MerchantTheme.successGreen
                          : MerchantTheme.errorRed)
                      .withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(MerchantTheme.radiusSmall),
                ),
                child: Text(
                  growth,
                  style: MerchantTheme.labelSmall.copyWith(
                    color: isPositive
                        ? MerchantTheme.successGreen
                        : MerchantTheme.errorRed,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: MerchantTheme.spacing16),
          Text(
            value,
            style: MerchantTheme.headline2.copyWith(
              color: MerchantTheme.neutral800,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: MerchantTheme.spacing4),
          Text(
            title,
            style: MerchantTheme.bodyMedium.copyWith(
              color: MerchantTheme.neutral600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection(DashboardAnalytics analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Performance Charts',
              style: MerchantTheme.headline3.copyWith(
                color: MerchantTheme.neutral800,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: MerchantTheme.spacing16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
                border: Border.all(color: MerchantTheme.neutral200),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedMetric,
                  onChanged: (value) =>
                      setState(() => _selectedMetric = value!),
                  items: _metricOptions
                      .map((metric) => DropdownMenuItem(
                            value: metric,
                            child:
                                Text(metric, style: MerchantTheme.bodyMedium),
                          ))
                      .toList(),
                  icon: const Icon(Icons.keyboard_arrow_down,
                      color: MerchantTheme.neutral500),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: MerchantTheme.spacing16),
        Container(
          height: 300,
          padding: const EdgeInsets.all(MerchantTheme.spacing24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            boxShadow: MerchantTheme.cardShadow,
          ),
          child: _buildChart(analytics),
        ),
      ],
    );
  }

  Widget _buildChart(DashboardAnalytics analytics) {
    // Simple bar chart implementation
    final data = _getChartData(analytics);
    final maxValue =
        data.fold<double>(0, (max, value) => value > max ? value : max);

    return Column(
      children: [
        Text(
          '$_selectedMetric Trend',
          style: MerchantTheme.headline4.copyWith(
            color: MerchantTheme.neutral800,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: MerchantTheme.spacing24),
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: data.asMap().entries.map((entry) {
              final index = entry.key;
              final value = entry.value;
              final height = maxValue > 0 ? (value / maxValue) : 0.0;

              return Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          width: 20,
                          decoration: BoxDecoration(
                            gradient: MerchantTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.bottomCenter,
                            heightFactor: height,
                            child: Container(
                              decoration: BoxDecoration(
                                color: MerchantTheme.primaryBlue,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: MerchantTheme.spacing8),
                      Text(
                        _getChartLabel(index),
                        style: MerchantTheme.labelSmall.copyWith(
                          color: MerchantTheme.neutral500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  List<double> _getChartData(DashboardAnalytics analytics) {
    switch (_selectedMetric) {
      case 'Revenue':
        return [
          analytics.salesMetrics.totalRevenue * 0.8,
          analytics.salesMetrics.totalRevenue * 0.9,
          analytics.salesMetrics.totalRevenue
        ];
      case 'Orders':
        return [
          analytics.salesMetrics.totalOrders * 0.7,
          analytics.salesMetrics.totalOrders * 0.85,
          analytics.salesMetrics.totalOrders.toDouble()
        ];
      case 'Products':
        return [
          analytics.inventoryMetrics.totalProducts * 0.6,
          analytics.inventoryMetrics.totalProducts * 0.8,
          analytics.inventoryMetrics.totalProducts.toDouble()
        ];
      case 'Customers':
        return [
          analytics.customerMetrics.totalCustomers * 0.75,
          analytics.customerMetrics.totalCustomers * 0.9,
          analytics.customerMetrics.totalCustomers.toDouble()
        ];
      default:
        return [1000, 2000, 3000];
    }
  }

  String _getChartLabel(int index) {
    final labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return labels[index % labels.length];
  }

  Widget _buildPerformanceIndicators(DashboardAnalytics analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Indicators',
          style: MerchantTheme.headline3.copyWith(
            color: MerchantTheme.neutral800,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: MerchantTheme.spacing16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount:
              MerchantTheme.getResponsiveGridCrossAxisCount(context),
          crossAxisSpacing: MerchantTheme.spacing16,
          mainAxisSpacing: MerchantTheme.spacing16,
          childAspectRatio: 2.5,
          children: [
            _buildIndicatorCard(
              'Average Order Value',
              '₦${analytics.salesMetrics.averageOrderValue.toStringAsFixed(0)}',
              Icons.trending_up,
              MerchantTheme.successGreen,
            ),
            _buildIndicatorCard(
              'Conversion Rate',
              '3.8%', // Demo value
              Icons.percent,
              MerchantTheme.primaryBlue,
            ),
            _buildIndicatorCard(
              'Customer Retention',
              '${analytics.customerMetrics.customerRetentionRate.toStringAsFixed(1)}%',
              Icons.people_outline,
              MerchantTheme.secondaryBlue,
            ),
            _buildIndicatorCard(
              'Inventory Turnover',
              '4.2x', // Demo value
              Icons.autorenew,
              MerchantTheme.warningOrange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIndicatorCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(MerchantTheme.spacing12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: MerchantTheme.spacing16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: MerchantTheme.headline4.copyWith(
                    color: MerchantTheme.neutral800,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: MerchantTheme.bodyMedium.copyWith(
                    color: MerchantTheme.neutral600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(DashboardAnalytics analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: MerchantTheme.headline3.copyWith(
            color: MerchantTheme.neutral800,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: MerchantTheme.spacing16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            boxShadow: MerchantTheme.cardShadow,
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getActivityColor(index).withOpacity(0.1),
                  child: Icon(
                    _getActivityIcon(index),
                    color: _getActivityColor(index),
                    size: 20,
                  ),
                ),
                title: Text(
                  _getActivityTitle(index),
                  style: MerchantTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  _getActivitySubtitle(index),
                  style: MerchantTheme.bodySmall.copyWith(
                    color: MerchantTheme.neutral500,
                  ),
                ),
                trailing: Text(
                  _getActivityTime(index),
                  style: MerchantTheme.labelSmall.copyWith(
                    color: MerchantTheme.neutral400,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getActivityColor(int index) {
    final colors = [
      MerchantTheme.successGreen,
      MerchantTheme.primaryBlue,
      MerchantTheme.warningOrange,
      MerchantTheme.secondaryBlue,
      MerchantTheme.errorRed,
    ];
    return colors[index % colors.length];
  }

  IconData _getActivityIcon(int index) {
    final icons = [
      Icons.shopping_cart,
      Icons.person_add,
      Icons.inventory_2,
      Icons.attach_money,
      Icons.notification_important,
    ];
    return icons[index % icons.length];
  }

  String _getActivityTitle(int index) {
    final titles = [
      'New order received',
      'New customer registered',
      'Product stock updated',
      'Payment processed',
      'Low stock alert',
    ];
    return titles[index % titles.length];
  }

  String _getActivitySubtitle(int index) {
    final subtitles = [
      'Order #1234 from John Doe',
      'Customer: <EMAIL>',
      'iPhone 13 Pro - 50 units added',
      '₦25,000 received for Order #1234',
      'Samsung Galaxy S21 - Only 5 units left',
    ];
    return subtitles[index % subtitles.length];
  }

  String _getActivityTime(int index) {
    final times = [
      '2 min ago',
      '15 min ago',
      '1 hour ago',
      '2 hours ago',
      '3 hours ago',
    ];
    return times[index % times.length];
  }

  Widget _buildLoadingSection() {
    return Column(
      children: [
        _buildLoadingMetricCards(),
        const SizedBox(height: MerchantTheme.spacing32),
        _buildLoadingChart(),
        const SizedBox(height: MerchantTheme.spacing32),
        _buildLoadingIndicators(),
      ],
    );
  }

  Widget _buildLoadingMetricCards() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: MerchantTheme.getResponsiveGridCrossAxisCount(context),
        crossAxisSpacing: MerchantTheme.spacing16,
        mainAxisSpacing: MerchantTheme.spacing16,
        childAspectRatio: 1.5,
      ),
      itemCount: 4,
      itemBuilder: (context, index) => Container(
        decoration: BoxDecoration(
          color: MerchantTheme.neutral100,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        ),
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildLoadingChart() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: MerchantTheme.neutral100,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildLoadingIndicators() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: MerchantTheme.getResponsiveGridCrossAxisCount(context),
        crossAxisSpacing: MerchantTheme.spacing16,
        mainAxisSpacing: MerchantTheme.spacing16,
        childAspectRatio: 2.5,
      ),
      itemCount: 4,
      itemBuilder: (context, index) => Container(
        decoration: BoxDecoration(
          color: MerchantTheme.neutral100,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        ),
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildErrorSection(String error) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing32),
      child: Column(
        children: [
          const Icon(Icons.error_outline,
              size: 64, color: MerchantTheme.errorRed),
          const SizedBox(height: MerchantTheme.spacing16),
          Text(
            'Error loading analytics',
            style:
                MerchantTheme.headline3.copyWith(color: MerchantTheme.errorRed),
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          Text(error,
              style: MerchantTheme.bodyMedium, textAlign: TextAlign.center),
          const SizedBox(height: MerchantTheme.spacing24),
          ElevatedButton(
            onPressed: () => ref.invalidate(dashboardAnalyticsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Action Methods
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exporting analytics report...'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  // Enhanced UI Methods
  Widget _buildEnhancedExportButton(bool isWide) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        child: InkWell(
          onTap: _exportReport,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal:
                  isWide ? MerchantTheme.spacing20 : MerchantTheme.spacing16,
              vertical: MerchantTheme.spacing12,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.download,
                  color: Colors.white,
                  size: 20,
                ),
                if (isWide) ...[
                  const SizedBox(width: MerchantTheme.spacing8),
                  Text(
                    'Export Report',
                    style: MerchantTheme.bodyMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedPeriodSelector(bool isWide, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: isWide || isTablet
          ? Row(
              children: [
                Text(
                  'Period:',
                  style: MerchantTheme.bodyLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: MerchantTheme.spacing16),
                ..._periodOptions.map((period) {
                  final isSelected = _selectedPeriod == period;
                  return Padding(
                    padding:
                        const EdgeInsets.only(right: MerchantTheme.spacing8),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius:
                          BorderRadius.circular(MerchantTheme.radiusSmall),
                      child: InkWell(
                        onTap: () => setState(() => _selectedPeriod = period),
                        borderRadius:
                            BorderRadius.circular(MerchantTheme.radiusSmall),
                        child: AnimatedContainer(
                          duration: MerchantTheme.fastAnimation,
                          padding: const EdgeInsets.symmetric(
                            horizontal: MerchantTheme.spacing12,
                            vertical: MerchantTheme.spacing8,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.white.withOpacity(0.2)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(
                                MerchantTheme.radiusSmall),
                          ),
                          child: Text(
                            period,
                            style: MerchantTheme.bodyMedium.copyWith(
                              color: Colors.white,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            )
          : Row(
              children: [
                Text(
                  'Period:',
                  style: MerchantTheme.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: MerchantTheme.spacing12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: MerchantTheme.spacing12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius:
                          BorderRadius.circular(MerchantTheme.radiusSmall),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedPeriod,
                        onChanged: (value) =>
                            setState(() => _selectedPeriod = value!),
                        items: _periodOptions
                            .map((period) => DropdownMenuItem(
                                  value: period,
                                  child: Text(
                                    period,
                                    style: MerchantTheme.bodyMedium
                                        .copyWith(color: Colors.white),
                                  ),
                                ))
                            .toList(),
                        icon: const Icon(Icons.keyboard_arrow_down,
                            color: Colors.white),
                        isExpanded: true,
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildEnhancedAnalyticsContent(
      DashboardAnalytics analytics, bool isWide, bool isTablet, bool isMobile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: MerchantTheme.spacing24),

        // Enhanced Key Metrics Cards
        _buildEnhancedKeyMetrics(analytics, isWide, isTablet, isMobile),
        const SizedBox(height: MerchantTheme.spacing32),

        // Enhanced Charts Section
        _buildEnhancedChartsSection(analytics, isWide, isTablet, isMobile),
        const SizedBox(height: MerchantTheme.spacing32),

        // Enhanced Performance Indicators
        _buildEnhancedPerformanceIndicators(
            analytics, isWide, isTablet, isMobile),
        const SizedBox(height: MerchantTheme.spacing32),

        // Enhanced Recent Activity
        _buildEnhancedRecentActivity(analytics, isWide, isTablet, isMobile),
      ],
    );
  }

  Widget _buildEnhancedKeyMetrics(
      DashboardAnalytics analytics, bool isWide, bool isTablet, bool isMobile) {
    return _buildKeyMetrics(analytics); // Use existing implementation for now
  }

  Widget _buildEnhancedChartsSection(
      DashboardAnalytics analytics, bool isWide, bool isTablet, bool isMobile) {
    return _buildChartsSection(
        analytics); // Use existing implementation for now
  }

  Widget _buildEnhancedPerformanceIndicators(
      DashboardAnalytics analytics, bool isWide, bool isTablet, bool isMobile) {
    return _buildPerformanceIndicators(
        analytics); // Use existing implementation for now
  }

  Widget _buildEnhancedRecentActivity(
      DashboardAnalytics analytics, bool isWide, bool isTablet, bool isMobile) {
    return _buildRecentActivity(
        analytics); // Use existing implementation for now
  }
}
