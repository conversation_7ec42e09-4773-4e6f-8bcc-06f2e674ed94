import 'package:flutter/material.dart';

enum SettingsTileType {
  simple,
  switchTile,
  navigation,
  selection,
}

class SettingsTile extends StatelessWidget {
  final SettingsTileType type;
  final Widget leading;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool? value;
  final ValueChanged<bool>? onToggle;
  final bool enabled;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? contentPadding;
  final bool showDivider;

  const SettingsTile({
    super.key,
    required this.type,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.value,
    this.onToggle,
    this.enabled = true,
    this.backgroundColor,
    this.contentPadding,
    this.showDivider = true,
  });

  // Simple tile constructor
  const SettingsTile.simple({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.enabled = true,
    this.backgroundColor,
    this.contentPadding,
    this.showDivider = true,
  }) : type = SettingsTileType.simple,
       value = null,
       onToggle = null;

  // Switch tile constructor
  const SettingsTile.switchTile({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    required this.value,
    required this.onToggle,
    this.enabled = true,
    this.backgroundColor,
    this.contentPadding,
    this.showDivider = true,
  }) : type = SettingsTileType.switchTile,
       trailing = null,
       onTap = null;

  // Navigation tile constructor
  const SettingsTile.navigation({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    required this.onTap,
    this.enabled = true,
    this.backgroundColor,
    this.contentPadding,
    this.showDivider = true,
  }) : type = SettingsTileType.navigation,
       value = null,
       onToggle = null;

  // Selection tile constructor
  const SettingsTile.selection({
    super.key,
    required this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    required this.onTap,
    this.enabled = true,
    this.backgroundColor,
    this.contentPadding,
    this.showDivider = true,
  }) : type = SettingsTileType.selection,
       value = null,
       onToggle = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Widget tile;

    switch (type) {
      case SettingsTileType.switchTile:
        tile = SwitchListTile(
          secondary: leading,
          title: Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: enabled ? colorScheme.onSurface : colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: enabled ? colorScheme.onSurfaceVariant : colorScheme.onSurfaceVariant.withOpacity(0.6),
                  ),
                )
              : null,
          value: value ?? false,
          onChanged: enabled ? onToggle : null,
          contentPadding: contentPadding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          tileColor: backgroundColor,
          activeColor: colorScheme.primary,
          activeTrackColor: colorScheme.primary.withOpacity(0.3),
          inactiveThumbColor: colorScheme.outline,
          inactiveTrackColor: colorScheme.surfaceVariant,
        );
        break;

      default:
        tile = ListTile(
          leading: leading,
          title: Text(
            title,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: enabled ? colorScheme.onSurface : colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: enabled ? colorScheme.onSurfaceVariant : colorScheme.onSurfaceVariant.withOpacity(0.6),
                  ),
                )
              : null,
          trailing: _buildTrailing(context),
          onTap: enabled ? onTap : null,
          contentPadding: contentPadding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          tileColor: backgroundColor,
          enabled: enabled,
        );
    }

    if (showDivider) {
      return Column(
        children: [
          tile,
          Divider(
            height: 1,
            thickness: 0.5,
            color: colorScheme.outline.withOpacity(0.2),
            indent: 72, // Align with title text
          ),
        ],
      );
    }

    return tile;
  }

  Widget? _buildTrailing(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    switch (type) {
      case SettingsTileType.navigation:
        return trailing ?? Icon(
          Icons.chevron_right,
          color: enabled ? colorScheme.onSurfaceVariant : colorScheme.onSurfaceVariant.withOpacity(0.6),
        );
      case SettingsTileType.selection:
        return trailing ?? Icon(
          Icons.arrow_drop_down,
          color: enabled ? colorScheme.onSurfaceVariant : colorScheme.onSurfaceVariant.withOpacity(0.6),
        );
      default:
        return trailing;
    }
  }
}

// Settings section widget
class SettingsSection extends StatelessWidget {
  final String? title;
  final List<Widget> tiles;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const SettingsSection({
    super.key,
    this.title,
    required this.tiles,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                title!,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          ...tiles,
        ],
      ),
    );
  }
}

// Settings group widget for organizing multiple sections
class SettingsGroup extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;

  const SettingsGroup({
    super.key,
    this.title,
    this.subtitle,
    required this.children,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.fromLTRB(32, 16, 32, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title!,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
          ...children,
        ],
      ),
    );
  }
}

// Specialized settings tiles for common use cases
class SecuritySettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final bool isSecure;
  final bool enabled;

  const SecuritySettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    required this.onTap,
    this.isSecure = false,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return SettingsTile.navigation(
      leading: Icon(
        Icons.security,
        color: isSecure ? colorScheme.primary : colorScheme.error,
      ),
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      enabled: enabled,
      trailing: Icon(
        isSecure ? Icons.check_circle : Icons.warning,
        color: isSecure ? colorScheme.primary : colorScheme.error,
        size: 20,
      ),
    );
  }
}

class PrivacySettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;

  const PrivacySettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    required this.value,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsTile.switchTile(
      leading: const Icon(Icons.privacy_tip_outlined),
      title: title,
      subtitle: subtitle,
      value: value,
      onToggle: onChanged,
      enabled: enabled,
    );
  }
}
