import 'package:flutter/material.dart';

/// Advanced animation theme system for professional inventory management
/// Implements modern micro-interactions and sophisticated visual effects
class AnimationTheme {
  // Animation Durations - Following Material Design 3 principles
  static const Duration ultraFast = Duration(milliseconds: 100);
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration ultraSlow = Duration(milliseconds: 800);

  // Stagger Delays for Sequential Animations
  static const Duration staggerDelay = Duration(milliseconds: 50);
  static const Duration cardStaggerDelay = Duration(milliseconds: 100);

  // Animation Curves - Modern easing functions
  static const Curve defaultCurve = Curves.easeOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeInOutCubic;
  static const Curve sharpCurve = Curves.easeOutExpo;
  static const Curve gentleCurve = Curves.easeOutQuart;

  // Hover and Focus Effects
  static const double hoverScale = 1.02;
  static const double pressScale = 0.98;
  static const double cardHoverElevation = 8.0;
  static const double cardRestElevation = 2.0;

  // Glassmorphism Effects
  static const double glassBlur = 10.0;
  static const double glassOpacity = 0.1;
  static const double glassStrokeOpacity = 0.2;

  // Shadow System - Multiple elevation levels
  static List<BoxShadow> elevationShadows(double elevation, {Color? color}) {
    final shadowColor = color ?? Colors.black;
    
    switch (elevation.round()) {
      case 1:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ];
      case 2:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: shadowColor.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 1),
          ),
        ];
      case 4:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.12),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: shadowColor.withOpacity(0.06),
            blurRadius: 16,
            offset: const Offset(0, 2),
          ),
        ];
      case 8:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.16),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: shadowColor.withOpacity(0.08),
            blurRadius: 32,
            offset: const Offset(0, 4),
          ),
        ];
      case 16:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.20),
            blurRadius: 32,
            offset: const Offset(0, 16),
          ),
          BoxShadow(
            color: shadowColor.withOpacity(0.12),
            blurRadius: 64,
            offset: const Offset(0, 8),
          ),
        ];
      default:
        return [
          BoxShadow(
            color: shadowColor.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];
    }
  }

  // Gradient Definitions
  static LinearGradient primaryGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        colorScheme.primary,
        colorScheme.primary.withOpacity(0.8),
      ],
    );
  }

  static LinearGradient surfaceGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        colorScheme.surface,
        colorScheme.surface.withOpacity(0.95),
      ],
    );
  }

  static LinearGradient glassGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        colorScheme.surface.withOpacity(0.2),
        colorScheme.surface.withOpacity(0.1),
      ],
    );
  }

  // Color Animations
  static Color lerpColor(Color begin, Color end, double t) {
    return Color.lerp(begin, end, t) ?? begin;
  }

  // Status Colors with Semantic Meaning
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);

  // Stock Status Colors
  static Color stockStatusColor(int stock, int threshold) {
    if (stock <= 0) return errorColor;
    if (stock <= threshold) return warningColor;
    return successColor;
  }

  // Animation Builders
  static Widget fadeInUp({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
    double offset = 50.0,
  }) {
    final animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: defaultCurve,
      ),
    ));

    final slideAnimation = Tween<Offset>(
      begin: Offset(0, offset),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: defaultCurve,
      ),
    ));

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Transform.translate(
          offset: slideAnimation.value,
          child: Opacity(
            opacity: animation.value,
            child: child,
          ),
        );
      },
    );
  }

  static Widget scaleIn({
    required Widget child,
    required AnimationController controller,
    Duration delay = Duration.zero,
    double initialScale = 0.8,
  }) {
    final animation = Tween<double>(
      begin: initialScale,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Interval(
        delay.inMilliseconds / controller.duration!.inMilliseconds,
        1.0,
        curve: bounceCurve,
      ),
    ));

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Transform.scale(
          scale: animation.value,
          child: child,
        );
      },
    );
  }

  // Shimmer Effect for Loading States
  static Widget shimmerEffect({
    required Widget child,
    required AnimationController controller,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        final theme = Theme.of(context);
        final base = baseColor ?? theme.colorScheme.surfaceContainerHighest;
        final highlight = highlightColor ?? theme.colorScheme.surface;
        
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment(-1.0 - controller.value, 0.0),
              end: Alignment(1.0 - controller.value, 0.0),
              colors: [base, highlight, base],
              stops: const [0.0, 0.5, 1.0],
            ).createShader(bounds);
          },
          child: child,
        );
      },
    );
  }
}
