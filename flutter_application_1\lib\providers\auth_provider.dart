import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../core/enums/user_role.dart';
import 'demo_auth_provider.dart';

// Auth state provider
final authStateProvider = StreamProvider<User?>((ref) {
  return AuthService.instance.authStateChanges;
});

// Current user data provider
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  // Check for demo user first
  final demoUser = ref.watch(demoUserProvider);
  if (demoUser != null) {
    return demoUser;
  }

  // Otherwise check Firebase auth
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) async {
      if (user != null) {
        return await AuthService.instance.getUserData(user.uid);
      }
      return null;
    },
    loading: () => null,
    error: (_, __) => null,
  );
});

// Auth controller
class AuthController extends StateNotifier<AsyncValue<UserModel?>> {
  AuthController() : super(const AsyncValue.loading());

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();

    try {
      final user = await AuthService.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      state = AsyncValue.data(user);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required UserRole role,
    String? phoneNumber,
    String? mallId,
    String? merchantId,
    String? adminId,
    Map<String, dynamic>? securityQuestions,
  }) async {
    state = const AsyncValue.loading();

    try {
      final user = await AuthService.instance.registerWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        role: role,
        phoneNumber: phoneNumber,
        mallId: mallId,
        merchantId: merchantId,
        adminId: adminId,
        securityQuestions: securityQuestions,
      );
      state = AsyncValue.data(user);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> signOut() async {
    try {
      await AuthService.instance.signOut();
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await AuthService.instance.resetPassword(email);
    } catch (e) {
      rethrow;
    }
  }
}

// Auth controller provider
final authControllerProvider =
    StateNotifierProvider<AuthController, AsyncValue<UserModel?>>((ref) {
  return AuthController();
});

// Helper providers for specific roles
final isCustomerProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.role.isCustomer ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final isMerchantProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.role.isMerchant ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final isSalesRepProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.role.isSalesRep ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final isAdminProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.role.isAdmin ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

final isBossProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.role.isBoss ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
});

// Current user ID provider
final currentUserIdProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user?.uid,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Current mall ID provider
final currentMallIdProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.mallId,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Current merchant ID provider
final currentMerchantIdProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user.when(
    data: (userData) => userData?.merchantId,
    loading: () => null,
    error: (_, __) => null,
  );
});
