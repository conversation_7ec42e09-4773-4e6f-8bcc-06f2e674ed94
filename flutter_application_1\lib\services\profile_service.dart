import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../models/user_model.dart';
import '../models/user_preferences_model.dart';
import '../models/account_settings_model.dart';

class ProfileService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collections
  static const String _usersCollection = 'users';
  static const String _preferencesCollection = 'user_preferences';
  static const String _settingsCollection = 'account_settings';

  // Update user profile
  static Future<UserModel> updateUserProfile({
    required String userId,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (firstName != null) updateData['firstName'] = firstName;
      if (lastName != null) updateData['lastName'] = lastName;
      if (phoneNumber != null) updateData['phoneNumber'] = phoneNumber;
      if (profileImageUrl != null) updateData['profileImageUrl'] = profileImageUrl;

      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .update(updateData);

      // Get updated user data
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .get();

      return UserModel.fromFirestore(doc);
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  // Upload profile image
  static Future<String> uploadProfileImage({
    required String userId,
    required File imageFile,
  }) async {
    try {
      final ref = _storage
          .ref()
          .child('profile_images')
          .child('$userId.jpg');

      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  // Delete profile image
  static Future<void> deleteProfileImage(String userId) async {
    try {
      final ref = _storage
          .ref()
          .child('profile_images')
          .child('$userId.jpg');

      await ref.delete();
    } catch (e) {
      // Image might not exist, which is fine
      print('Profile image deletion failed (might not exist): $e');
    }
  }

  // Get user preferences
  static Future<UserPreferencesModel> getUserPreferences(String userId) async {
    try {
      final doc = await _firestore
          .collection(_preferencesCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserPreferencesModel.fromFirestore(doc);
      } else {
        // Create default preferences
        final defaultPrefs = UserPreferencesModel.defaultPreferences(userId);
        await _firestore
            .collection(_preferencesCollection)
            .doc(userId)
            .set(defaultPrefs.toFirestore());
        return defaultPrefs;
      }
    } catch (e) {
      throw Exception('Failed to get user preferences: $e');
    }
  }

  // Update user preferences
  static Future<UserPreferencesModel> updateUserPreferences({
    required String userId,
    required UserPreferencesModel preferences,
  }) async {
    try {
      final updatedPrefs = preferences.copyWith(updatedAt: DateTime.now());
      
      await _firestore
          .collection(_preferencesCollection)
          .doc(userId)
          .set(updatedPrefs.toFirestore());

      return updatedPrefs;
    } catch (e) {
      throw Exception('Failed to update preferences: $e');
    }
  }

  // Get account settings
  static Future<AccountSettingsModel> getAccountSettings(String userId) async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return AccountSettingsModel.fromFirestore(doc);
      } else {
        // Create default settings
        final defaultSettings = AccountSettingsModel.defaultSettings(userId);
        await _firestore
            .collection(_settingsCollection)
            .doc(userId)
            .set(defaultSettings.toFirestore());
        return defaultSettings;
      }
    } catch (e) {
      throw Exception('Failed to get account settings: $e');
    }
  }

  // Update account settings
  static Future<AccountSettingsModel> updateAccountSettings({
    required String userId,
    required AccountSettingsModel settings,
  }) async {
    try {
      final updatedSettings = settings.copyWith(updatedAt: DateTime.now());
      
      await _firestore
          .collection(_settingsCollection)
          .doc(userId)
          .set(updatedSettings.toFirestore());

      return updatedSettings;
    } catch (e) {
      throw Exception('Failed to update account settings: $e');
    }
  }

  // Change password
  static Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user logged in');
      }

      // Re-authenticate user with current password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      // Update last password change in account settings
      final userId = user.uid;
      final currentSettings = await getAccountSettings(userId);
      await updateAccountSettings(
        userId: userId,
        settings: currentSettings.copyWith(
          lastPasswordChange: DateTime.now(),
        ),
      );
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'wrong-password':
            throw Exception('Current password is incorrect');
          case 'weak-password':
            throw Exception('New password is too weak');
          case 'requires-recent-login':
            throw Exception('Please log out and log back in before changing password');
          default:
            throw Exception('Failed to change password: ${e.message}');
        }
      }
      throw Exception('Failed to change password: $e');
    }
  }

  // Send password reset email
  static Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'user-not-found':
            throw Exception('No account found with this email address');
          case 'invalid-email':
            throw Exception('Invalid email address');
          default:
            throw Exception('Failed to send reset email: ${e.message}');
        }
      }
      throw Exception('Failed to send reset email: $e');
    }
  }

  // Request account deactivation
  static Future<void> requestAccountDeactivation(String userId) async {
    try {
      final currentSettings = await getAccountSettings(userId);
      await updateAccountSettings(
        userId: userId,
        settings: currentSettings.copyWith(
          accountDeactivationRequested: true,
          deactivationRequestDate: DateTime.now(),
        ),
      );
    } catch (e) {
      throw Exception('Failed to request account deactivation: $e');
    }
  }

  // Cancel account deactivation request
  static Future<void> cancelAccountDeactivation(String userId) async {
    try {
      final currentSettings = await getAccountSettings(userId);
      await updateAccountSettings(
        userId: userId,
        settings: currentSettings.copyWith(
          accountDeactivationRequested: false,
          deactivationRequestDate: null,
        ),
      );
    } catch (e) {
      throw Exception('Failed to cancel account deactivation: $e');
    }
  }

  // Export user data
  static Future<Map<String, dynamic>> exportUserData(String userId) async {
    try {
      final userDoc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .get();

      final preferencesDoc = await _firestore
          .collection(_preferencesCollection)
          .doc(userId)
          .get();

      final settingsDoc = await _firestore
          .collection(_settingsCollection)
          .doc(userId)
          .get();

      return {
        'user_profile': userDoc.data(),
        'preferences': preferencesDoc.data(),
        'account_settings': settingsDoc.data(),
        'export_date': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to export user data: $e');
    }
  }

  // Update security review timestamp
  static Future<void> updateSecurityReview(String userId) async {
    try {
      final currentSettings = await getAccountSettings(userId);
      await updateAccountSettings(
        userId: userId,
        settings: currentSettings.copyWith(
          lastSecurityReview: DateTime.now(),
        ),
      );
    } catch (e) {
      throw Exception('Failed to update security review: $e');
    }
  }
}
