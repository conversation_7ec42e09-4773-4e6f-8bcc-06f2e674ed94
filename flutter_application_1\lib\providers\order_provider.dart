import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

import '../models/order_model.dart';
import '../models/cart_item_model.dart';
import '../services/firebase_service.dart';
import 'auth_provider.dart';

// Order repository
class OrderRepository {
  final FirebaseService _firebase = FirebaseService.instance;

  // Get orders for a specific merchant and mall
  Stream<List<OrderModel>> getOrders(String merchantId, String mallId) {
    return _firebase.firestore
        .collection('orders')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => OrderModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Get orders by status
  Stream<List<OrderModel>> getOrdersByStatus(
      String merchantId, String mallId, OrderStatus status) {
    return _firebase.firestore
        .collection('orders')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('status', isEqualTo: status.value)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => OrderModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Get pending orders
  Stream<List<OrderModel>> getPendingOrders(String merchantId, String mallId) {
    return getOrdersByStatus(merchantId, mallId, OrderStatus.pending);
  }

  // Get recent orders (last 30 days)
  Stream<List<OrderModel>> getRecentOrders(String merchantId, String mallId) {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return _firebase.firestore
        .collection('orders')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('createdAt', isGreaterThan: Timestamp.fromDate(thirtyDaysAgo))
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => OrderModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Create new order
  Future<OrderModel> createOrder({
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String merchantId,
    required String mallId,
    required List<CartItemModel> items,
    required double subtotal,
    required double tax,
    required double shippingFee,
    required double total,
    String? notes,
    String? shippingAddress,
  }) async {
    final orderId = const Uuid().v4();
    final now = DateTime.now();

    final order = OrderModel(
      id: orderId,
      customerId: customerId,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
      merchantId: merchantId,
      mallId: mallId,
      items: items,
      subtotal: subtotal,
      tax: tax,
      shippingFee: shippingFee,
      total: total,
      status: OrderStatus.pending,
      notes: notes,
      shippingAddress: shippingAddress,
      createdAt: now,
      updatedAt: now,
    );

    await _firebase.firestore
        .collection('orders')
        .doc(orderId)
        .set(order.toFirestore());

    return order;
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, OrderStatus status,
      {String? processedBy}) async {
    final now = DateTime.now();
    final updateData = <String, dynamic>{
      'status': status.value,
      'updatedAt': Timestamp.fromDate(now),
      if (processedBy != null) 'processedBy': processedBy,
    };

    // Add timestamp for specific status changes
    switch (status) {
      case OrderStatus.confirmed:
        updateData['confirmedAt'] = Timestamp.fromDate(now);
        break;
      case OrderStatus.shipped:
        updateData['shippedAt'] = Timestamp.fromDate(now);
        break;
      case OrderStatus.delivered:
        updateData['deliveredAt'] = Timestamp.fromDate(now);
        break;
      default:
        break;
    }

    await _firebase.firestore
        .collection('orders')
        .doc(orderId)
        .update(updateData);
  }

  // Update order tracking
  Future<void> updateOrderTracking(
      String orderId, String trackingNumber) async {
    await _firebase.firestore.collection('orders').doc(orderId).update({
      'trackingNumber': trackingNumber,
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    });
  }

  // Add order notes
  Future<void> addOrderNotes(String orderId, String notes) async {
    await _firebase.firestore.collection('orders').doc(orderId).update({
      'notes': notes,
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    });
  }

  // Delete order (only for pending orders)
  Future<void> deleteOrder(String orderId) async {
    await _firebase.firestore.collection('orders').doc(orderId).delete();
  }

  // Get order statistics
  Future<Map<String, dynamic>> getOrderStatistics(
      String merchantId, String mallId) async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfDay = DateTime(now.year, now.month, now.day);

    // Get all orders for the merchant
    final ordersSnapshot = await _firebase.firestore
        .collection('orders')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .get();

    final orders = ordersSnapshot.docs
        .map((doc) => OrderModel.fromFirestore(doc))
        .toList();

    // Calculate statistics
    final totalOrders = orders.length;
    final pendingOrders =
        orders.where((o) => o.status == OrderStatus.pending).length;
    final completedOrders = orders.where((o) => o.status.isCompleted).length;
    final cancelledOrders = orders.where((o) => o.status.isCancelled).length;

    final monthlyOrders =
        orders.where((o) => o.createdAt.isAfter(startOfMonth)).length;
    final weeklyOrders =
        orders.where((o) => o.createdAt.isAfter(startOfWeek)).length;
    final dailyOrders =
        orders.where((o) => o.createdAt.isAfter(startOfDay)).length;

    final totalRevenue =
        orders.fold<double>(0, (sum, order) => sum + order.total);
    final monthlyRevenue = orders
        .where((o) => o.createdAt.isAfter(startOfMonth))
        .fold<double>(0, (sum, order) => sum + order.total);

    final averageOrderValue =
        totalOrders > 0 ? totalRevenue / totalOrders : 0.0;

    return {
      'totalOrders': totalOrders,
      'pendingOrders': pendingOrders,
      'completedOrders': completedOrders,
      'cancelledOrders': cancelledOrders,
      'monthlyOrders': monthlyOrders,
      'weeklyOrders': weeklyOrders,
      'dailyOrders': dailyOrders,
      'totalRevenue': totalRevenue,
      'monthlyRevenue': monthlyRevenue,
      'averageOrderValue': averageOrderValue,
    };
  }
}

// Demo Order Repository for testing
class DemoOrderRepository {
  static final List<OrderModel> _orders = [
    OrderModel(
      id: '1',
      customerId: 'customer-1',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+234 ************',
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      items: [
        CartItemModel(
          productId: '1',
          barcode: '123456789012',
          name: 'Samsung Galaxy S24',
          description: 'Latest Samsung flagship smartphone',
          unitType: 'unit',
          pricePerUnit: 299999.99,
          quantity: 1,
          category: 'Electronics',
        ),
      ],
      subtotal: 299999.99,
      tax: 0.0,
      shippingFee: 5000.0,
      total: 304999.99,
      status: OrderStatus.pending,
      shippingAddress: '123 Main Street, Lagos, Nigeria',
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    OrderModel(
      id: '2',
      customerId: 'customer-2',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      customerPhone: '+234 ************',
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      items: [
        CartItemModel(
          productId: '2',
          barcode: '234567890123',
          name: 'iPhone 15 Pro',
          description: 'Latest Apple flagship smartphone',
          unitType: 'unit',
          pricePerUnit: 450000.0,
          quantity: 1,
          category: 'Electronics',
        ),
      ],
      subtotal: 450000.0,
      tax: 0.0,
      shippingFee: 5000.0,
      total: 455000.0,
      status: OrderStatus.confirmed,
      shippingAddress: '456 Oak Avenue, Abuja, Nigeria',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 12)),
      confirmedAt: DateTime.now().subtract(const Duration(hours: 12)),
    ),
  ];

  Stream<List<OrderModel>> getOrders(String merchantId, String mallId) {
    return Stream.value(_orders
        .where((o) => o.merchantId == merchantId && o.mallId == mallId)
        .toList());
  }

  Stream<List<OrderModel>> getOrdersByStatus(
      String merchantId, String mallId, OrderStatus status) {
    return Stream.value(_orders
        .where((o) =>
            o.merchantId == merchantId &&
            o.mallId == mallId &&
            o.status == status)
        .toList());
  }

  Stream<List<OrderModel>> getPendingOrders(String merchantId, String mallId) {
    return getOrdersByStatus(merchantId, mallId, OrderStatus.pending);
  }

  Stream<List<OrderModel>> getRecentOrders(String merchantId, String mallId) {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return Stream.value(_orders
        .where((o) =>
            o.merchantId == merchantId &&
            o.mallId == mallId &&
            o.createdAt.isAfter(thirtyDaysAgo))
        .toList());
  }

  Future<void> updateOrderStatus(String orderId, OrderStatus status,
      {String? processedBy}) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final index = _orders.indexWhere((o) => o.id == orderId);
    if (index != -1) {
      final now = DateTime.now();
      _orders[index] = _orders[index].copyWith(
        status: status,
        updatedAt: now,
        processedBy: processedBy,
        confirmedAt:
            status == OrderStatus.confirmed ? now : _orders[index].confirmedAt,
        shippedAt:
            status == OrderStatus.shipped ? now : _orders[index].shippedAt,
        deliveredAt:
            status == OrderStatus.delivered ? now : _orders[index].deliveredAt,
      );
    }
  }
}

// Providers
final orderRepositoryProvider =
    Provider<OrderRepository>((ref) => OrderRepository());
final demoOrderRepositoryProvider =
    Provider<DemoOrderRepository>((ref) => DemoOrderRepository());

// Current user's orders provider (simplified for demo)
final currentUserOrdersProvider = StreamProvider<List<OrderModel>>((ref) {
  final repository = ref.watch(demoOrderRepositoryProvider);
  // For demo, always return orders for demo merchant
  return repository.getOrders('demo-merchant', 'demo-mall');
});

// Pending orders provider
final pendingOrdersProvider = StreamProvider<List<OrderModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(demoOrderRepositoryProvider);

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return repository.getPendingOrders(userData!.id, userData.mallId!);
      }
      return Stream.value(<OrderModel>[]);
    },
    loading: () => Stream.value(<OrderModel>[]),
    error: (_, __) => Stream.value(<OrderModel>[]),
  );
});

// Recent orders provider
final recentOrdersProvider = StreamProvider<List<OrderModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(demoOrderRepositoryProvider);

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return repository.getRecentOrders(userData!.id, userData.mallId!);
      }
      return Stream.value(<OrderModel>[]);
    },
    loading: () => Stream.value(<OrderModel>[]),
    error: (_, __) => Stream.value(<OrderModel>[]),
  );
});

// Order management notifier
class OrderNotifier extends StateNotifier<AsyncValue<OrderModel?>> {
  OrderNotifier(this._repository) : super(const AsyncValue.data(null));

  final OrderRepository _repository;

  Future<void> updateOrderStatus(String orderId, OrderStatus status,
      {String? processedBy}) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateOrderStatus(orderId, status,
          processedBy: processedBy);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateOrderTracking(
      String orderId, String trackingNumber) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateOrderTracking(orderId, trackingNumber);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> addOrderNotes(String orderId, String notes) async {
    state = const AsyncValue.loading();
    try {
      await _repository.addOrderNotes(orderId, notes);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

final orderNotifierProvider =
    StateNotifierProvider<OrderNotifier, AsyncValue<OrderModel?>>((ref) {
  final repository = ref.watch(orderRepositoryProvider);
  return OrderNotifier(repository);
});
