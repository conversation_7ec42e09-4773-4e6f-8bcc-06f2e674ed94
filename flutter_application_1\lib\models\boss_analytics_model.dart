class MallPerformance {
  final String mallId;
  final String mallName;
  final double revenue;
  final double growthRate;
  final double efficiency;
  final int totalStores;
  final int activeStores;
  final double occupancyRate;
  final DateTime lastUpdated;

  MallPerformance({
    required this.mallId,
    required this.mallName,
    required this.revenue,
    required this.growthRate,
    required this.efficiency,
    required this.totalStores,
    required this.activeStores,
    required this.occupancyRate,
    required this.lastUpdated,
  });

  factory MallPerformance.fromJson(Map<String, dynamic> json) {
    return MallPerformance(
      mallId: json['mallId'] ?? '',
      mallName: json['mallName'] ?? '',
      revenue: (json['revenue'] ?? 0).toDouble(),
      growthRate: (json['growthRate'] ?? 0).toDouble(),
      efficiency: (json['efficiency'] ?? 0).toDouble(),
      totalStores: json['totalStores'] ?? 0,
      activeStores: json['activeStores'] ?? 0,
      occupancyRate: (json['occupancyRate'] ?? 0).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mallId': mallId,
      'mallName': mallName,
      'revenue': revenue,
      'growthRate': growthRate,
      'efficiency': efficiency,
      'totalStores': totalStores,
      'activeStores': activeStores,
      'occupancyRate': occupancyRate,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

class NetworkAnalytics {
  final double totalRevenue;
  final double averageGrowthRate;
  final int totalMalls;
  final int totalStores;
  final double networkEfficiency;
  final List<MallPerformance> mallPerformances;
  final Map<String, double> categoryBreakdown;
  final List<ChartData> revenueChart;
  final DateTime generatedAt;

  NetworkAnalytics({
    required this.totalRevenue,
    required this.averageGrowthRate,
    required this.totalMalls,
    required this.totalStores,
    required this.networkEfficiency,
    required this.mallPerformances,
    required this.categoryBreakdown,
    required this.revenueChart,
    required this.generatedAt,
  });

  factory NetworkAnalytics.fromJson(Map<String, dynamic> json) {
    return NetworkAnalytics(
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      averageGrowthRate: (json['averageGrowthRate'] ?? 0).toDouble(),
      totalMalls: json['totalMalls'] ?? 0,
      totalStores: json['totalStores'] ?? 0,
      networkEfficiency: (json['networkEfficiency'] ?? 0).toDouble(),
      mallPerformances: (json['mallPerformances'] as List<dynamic>?)
          ?.map((e) => MallPerformance.fromJson(e))
          .toList() ?? [],
      categoryBreakdown: Map<String, double>.from(json['categoryBreakdown'] ?? {}),
      revenueChart: (json['revenueChart'] as List<dynamic>?)
          ?.map((e) => ChartData.fromJson(e))
          .toList() ?? [],
      generatedAt: DateTime.parse(json['generatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class ChartData {
  final String label;
  final double value;
  final DateTime date;

  ChartData({
    required this.label,
    required this.value,
    required this.date,
  });

  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      label: json['label'] ?? '',
      value: (json['value'] ?? 0).toDouble(),
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      'date': date.toIso8601String(),
    };
  }
}