import 'package:flutter/material.dart';
import '../themes/customer_theme.dart';

/// Modern loading widgets for enhanced user experience
/// Inspired by contemporary mobile app loading patterns

class ModernLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final double strokeWidth;
  final String? message;

  const ModernLoadingIndicator({
    super.key,
    this.size = 40.0,
    this.color,
    this.strokeWidth = 3.0,
    this.message,
  });

  @override
  State<ModernLoadingIndicator> createState() => _ModernLoadingIndicatorState();
}

class _ModernLoadingIndicatorState extends State<ModernLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CircularProgressIndicator(
                value: _animation.value,
                strokeWidth: widget.strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.color ?? CustomerTheme.primaryTeal,
                ),
                backgroundColor: (widget.color ?? CustomerTheme.primaryTeal).withOpacity(0.2),
              );
            },
          ),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: CustomerTheme.spacingM),
          Text(
            widget.message!,
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class PulsingLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final String? message;

  const PulsingLoadingIndicator({
    super.key,
    this.size = 60.0,
    this.color,
    this.message,
  });

  @override
  State<PulsingLoadingIndicator> createState() => _PulsingLoadingIndicatorState();
}

class _PulsingLoadingIndicatorState extends State<PulsingLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Opacity(
                opacity: _opacityAnimation.value,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        widget.color ?? CustomerTheme.primaryTeal,
                        (widget.color ?? CustomerTheme.primaryTeal).withOpacity(0.6),
                      ],
                    ),
                  ),
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    color: Colors.white,
                    size: widget.size * 0.4,
                  ),
                ),
              ),
            );
          },
        ),
        if (widget.message != null) ...[
          const SizedBox(height: CustomerTheme.spacingM),
          Text(
            widget.message!,
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class DotsLoadingIndicator extends StatefulWidget {
  final Color? color;
  final double size;
  final String? message;

  const DotsLoadingIndicator({
    super.key,
    this.color,
    this.size = 8.0,
    this.message,
  });

  @override
  State<DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(3, (index) {
            return AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: widget.size * 0.5),
                  child: Transform.scale(
                    scale: 0.5 + (_animations[index].value * 0.5),
                    child: Container(
                      width: widget.size,
                      height: widget.size,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: (widget.color ?? CustomerTheme.primaryTeal)
                            .withOpacity(0.5 + (_animations[index].value * 0.5)),
                      ),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: CustomerTheme.spacingM),
          Text(
            widget.message!,
            style: CustomerTheme.bodyMedium.copyWith(
              color: CustomerTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class ModernLoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  final Color? backgroundColor;
  final LoadingType type;

  const ModernLoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.message,
    this.backgroundColor,
    this.type = LoadingType.circular,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? Colors.black.withOpacity(0.5),
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(CustomerTheme.spacingXL),
                decoration: BoxDecoration(
                  color: CustomerTheme.surfaceWhite,
                  borderRadius: BorderRadius.circular(CustomerTheme.largeRadius),
                  boxShadow: CustomerTheme.elevatedShadow,
                ),
                child: _buildLoadingIndicator(),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingIndicator() {
    switch (type) {
      case LoadingType.circular:
        return ModernLoadingIndicator(message: message);
      case LoadingType.pulsing:
        return PulsingLoadingIndicator(message: message);
      case LoadingType.dots:
        return DotsLoadingIndicator(message: message);
    }
  }
}

enum LoadingType {
  circular,
  pulsing,
  dots,
}

/// Loading state widget for pages
class LoadingPage extends StatelessWidget {
  final String? message;
  final LoadingType type;

  const LoadingPage({
    super.key,
    this.message,
    this.type = LoadingType.pulsing,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CustomerTheme.backgroundLight,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CustomerTheme.primaryTeal.withOpacity(0.05),
              CustomerTheme.backgroundLight,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLoadingIndicator(),
              if (message != null) ...[
                const SizedBox(height: CustomerTheme.spacingXL),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: CustomerTheme.spacingXL),
                  child: Text(
                    message!,
                    style: CustomerTheme.bodyLarge.copyWith(
                      color: CustomerTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    switch (type) {
      case LoadingType.circular:
        return const ModernLoadingIndicator(size: 60);
      case LoadingType.pulsing:
        return const PulsingLoadingIndicator();
      case LoadingType.dots:
        return const DotsLoadingIndicator(size: 12);
    }
  }
}
