import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../../../providers/auth_provider.dart';
import '../../../providers/account_provider.dart';
import '../../../services/logout_service.dart';
import '../../../shared/widgets/settings_tile.dart';
import '../../../models/user_preferences_model.dart';
import '../widgets/profile/profile_header_widget.dart';
import 'profile/edit_profile_screen.dart';
import 'profile/account_settings_screen.dart';
import 'profile/privacy_settings_screen.dart';
import 'profile/security_settings_screen.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(currentUserProvider);
    final preferencesAsync = ref.watch(currentUserPreferencesProvider);

    final needsSecurityReview = ref.watch(needsSecurityReviewProvider);
    final needsPasswordChange = ref.watch(needsPasswordChangeProvider);

    return Scaffold(
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: Text('Please log in to view your profile'),
            );
          }

          return CustomScrollView(
            slivers: [
              // App Bar with Profile Header
              SliverAppBar(
                expandedHeight: 280,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: ProfileHeaderWidget(
                    user: user,
                    onEditPressed: () => _navigateToEditProfile(context, user),
                  ),
                ),
                actions: [
                  IconButton(
                    onPressed: () => _showLogoutDialog(context, ref),
                    icon: const Icon(Icons.logout),
                  ),
                ],
              ),

              // Profile Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Security Alerts
                      if (needsSecurityReview || needsPasswordChange)
                        _buildSecurityAlerts(context, needsSecurityReview, needsPasswordChange),

                      // Account Management Section
                      SettingsSection(
                        title: 'Account Management',
                        tiles: [
                          SettingsTile.navigation(
                            leading: const Icon(Icons.edit),
                            title: 'Edit Profile',
                            subtitle: 'Update your personal information',
                            onTap: () => _navigateToEditProfile(context, user),
                          ),
                          SettingsTile.navigation(
                            leading: const Icon(Icons.settings),
                            title: 'Account Settings',
                            subtitle: 'Manage your account preferences',
                            onTap: () => _navigateToAccountSettings(context),
                          ),
                          SettingsTile.navigation(
                            leading: const Icon(Icons.security),
                            title: 'Security Settings',
                            subtitle: 'Password, 2FA, and security options',
                            onTap: () => _navigateToSecuritySettings(context),
                          ),
                          SettingsTile.navigation(
                            leading: const Icon(Icons.privacy_tip),
                            title: 'Privacy Settings',
                            subtitle: 'Control your privacy and data sharing',
                            onTap: () => _navigateToPrivacySettings(context),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Quick Settings Section
                      SettingsSection(
                        title: 'Quick Settings',
                        tiles: [
                          Consumer(
                            builder: (context, ref, child) {
                              return preferencesAsync.when(
                                data: (preferences) => SettingsTile.switchTile(
                                  leading: const Icon(Icons.notifications),
                                  title: 'Notifications',
                                  subtitle: 'Receive push notifications',
                                  value: preferences?.notificationsEnabled ?? true,
                                  onToggle: (value) => _toggleNotifications(ref, value),
                                ),
                                loading: () => SettingsTile.simple(
                                  leading: const Icon(Icons.notifications),
                                  title: 'Notifications',
                                  subtitle: 'Loading...',
                                ),
                                error: (_, __) => SettingsTile.simple(
                                  leading: const Icon(Icons.notifications),
                                  title: 'Notifications',
                                  subtitle: 'Error loading setting',
                                ),
                              );
                            },
                          ),
                          SettingsTile.selection(
                            leading: const Icon(Icons.palette),
                            title: 'Theme',
                            subtitle: _getThemeSubtitle(ref),
                            onTap: () => _showThemeSelector(context, ref),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Support Section
                      SettingsSection(
                        title: 'Support & Information',
                        tiles: [
                          SettingsTile.navigation(
                            leading: const Icon(Icons.help),
                            title: 'Help & Support',
                            subtitle: 'Get help and contact support',
                            onTap: () => _showHelpDialog(context),
                          ),
                          SettingsTile.navigation(
                            leading: const Icon(Icons.info),
                            title: 'About',
                            subtitle: 'App version and information',
                            onTap: () => _showAboutDialog(context),
                          ),
                          SettingsTile.navigation(
                            leading: const Icon(Icons.download),
                            title: 'Export Data',
                            subtitle: 'Download your account data',
                            onTap: () => _exportUserData(context, ref, user.id),
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Logout Button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () => _showLogoutDialog(context, ref),
                          icon: const Icon(Icons.logout),
                          label: const Text('Sign Out'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Theme.of(context).colorScheme.error,
                            side: BorderSide(color: Theme.of(context).colorScheme.error),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading profile',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityAlerts(BuildContext context, bool needsSecurityReview, bool needsPasswordChange) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.error.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: colorScheme.error,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Security Attention Required',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (needsPasswordChange)
            Text(
              '• Your password hasn\'t been changed in over 6 months',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onErrorContainer,
              ),
            ),
          if (needsSecurityReview)
            Text(
              '• Security review recommended (last review over 3 months ago)',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onErrorContainer,
              ),
            ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () => _navigateToSecuritySettings(context),
            child: Text(
              'Review Security Settings',
              style: TextStyle(
                color: colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEditProfile(BuildContext context, dynamic user) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(user: user),
      ),
    );
  }

  void _navigateToAccountSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AccountSettingsScreen(),
      ),
    );
  }

  void _navigateToSecuritySettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SecuritySettingsScreen(),
      ),
    );
  }

  void _navigateToPrivacySettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PrivacySettingsScreen(),
      ),
    );
  }

  void _toggleNotifications(WidgetRef ref, bool value) async {
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) return;

    final currentPrefs = await ref.read(userPreferencesProvider(userId).future);
    if (currentPrefs != null) {
      final updatedPrefs = currentPrefs.copyWith(notificationsEnabled: value);
      ref.read(accountControllerProvider.notifier).updatePreferences(
        userId: userId,
        preferences: updatedPrefs,
      );
    }
  }

  String _getThemeSubtitle(WidgetRef ref) {
    final themeMode = ref.watch(themePreferenceProvider);
    return themeMode.displayName;
  }

  void _showThemeSelector(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.read(themePreferenceProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppThemeMode.values.map((theme) {
            return RadioListTile<AppThemeMode>(
              title: Text(theme.displayName),
              value: theme,
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  _updateTheme(ref, value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _updateTheme(WidgetRef ref, AppThemeMode themeMode) async {
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) return;

    final currentPrefs = await ref.read(userPreferencesProvider(userId).future);
    if (currentPrefs != null) {
      final updatedPrefs = currentPrefs.copyWith(themeMode: themeMode);
      ref.read(accountControllerProvider.notifier).updatePreferences(
        userId: userId,
        preferences: updatedPrefs,
      );
    }
  }

  void _exportUserData(BuildContext context, WidgetRef ref, String userId) async {
    try {
      await ref.read(accountControllerProvider.notifier).exportUserData(userId);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data export feature coming soon!'),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to export data: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('For assistance, please contact:'),
            SizedBox(height: 8),
            Text('Email: <EMAIL>'),
            Text('Phone: +234 ************'),
            SizedBox(height: 8),
            Text('Business Hours: 9:00 AM - 6:00 PM'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Mall Management System'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text(
                'A comprehensive shopping mall management system with customer mobile app and web dashboards.'),
            SizedBox(height: 8),
            Text('© 2024 Mall Management System'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await LogoutService.performLogout(
                context: context,
                ref: ref,
                showLoadingDialog: false,
              );
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
