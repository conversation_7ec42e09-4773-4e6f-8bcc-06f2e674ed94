class CartItemModel {
  final String productId;
  final String barcode;
  final String name;
  final String description;
  final String unitType;
  final double pricePerUnit;
  final int quantity; // in units
  final String? imageUrl;
  final String category;

  CartItemModel({
    required this.productId,
    required this.barcode,
    required this.name,
    required this.description,
    required this.unitType,
    required this.pricePerUnit,
    required this.quantity,
    this.imageUrl,
    this.category = 'General',
  });

  double get totalPrice => pricePerUnit * quantity;

  factory CartItemModel.fromProduct(
    String productId,
    String barcode,
    String name,
    String description,
    String unitType,
    double pricePerUnit,
    String? imageUrl,
    String category, {
    int quantity = 1,
  }) {
    return CartItemModel(
      productId: productId,
      barcode: barcode,
      name: name,
      description: description,
      unitType: unitType,
      pricePerUnit: pricePerUnit,
      quantity: quantity,
      imageUrl: imageUrl,
      category: category,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'barcode': barcode,
      'name': name,
      'description': description,
      'unitType': unitType,
      'pricePerUnit': pricePerUnit,
      'quantity': quantity,
      'imageUrl': imageUrl,
      'category': category,
      'totalPrice': totalPrice,
    };
  }

  factory CartItemModel.fromJson(Map<String, dynamic> json) {
    return CartItemModel(
      productId: json['productId'] ?? '',
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      unitType: json['unitType'] ?? 'unit',
      pricePerUnit: (json['pricePerUnit'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
      imageUrl: json['imageUrl'],
      category: json['category'] ?? 'General',
    );
  }

  CartItemModel copyWith({
    String? productId,
    String? barcode,
    String? name,
    String? description,
    String? unitType,
    double? pricePerUnit,
    int? quantity,
    String? imageUrl,
    String? category,
  }) {
    return CartItemModel(
      productId: productId ?? this.productId,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      description: description ?? this.description,
      unitType: unitType ?? this.unitType,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      quantity: quantity ?? this.quantity,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItemModel && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;
}
