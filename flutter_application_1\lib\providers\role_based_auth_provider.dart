import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/enums/user_role.dart';
import '../models/user_model.dart';
import '../shared/middleware/auth_middleware.dart';
import 'auth_provider.dart';
import 'demo_auth_provider.dart';

/// Enhanced authentication state that combines Firebase and demo auth
class RoleBasedAuthState {
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const RoleBasedAuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  RoleBasedAuthState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return RoleBasedAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Role-based authentication notifier
class RoleBasedAuthNotifier extends StateNotifier<RoleBasedAuthState> {
  final Ref ref;

  RoleBasedAuthNotifier(this.ref) : super(const RoleBasedAuthState()) {
    _initialize();
  }

  /// Initialize authentication state
  void _initialize() {
    // Listen to auth changes
    ref.listen(authControllerProvider, (previous, next) {
      next.when(
        data: (user) {
          state = state.copyWith(
            user: user,
            isLoading: false,
            error: null,
            isAuthenticated: user != null,
          );
        },
        loading: () {
          state = state.copyWith(isLoading: true, error: null);
        },
        error: (error, _) {
          state = state.copyWith(
            isLoading: false,
            error: error.toString(),
            isAuthenticated: false,
          );
        },
      );
    });

    // Listen to demo auth changes
    ref.listen(demoUserProvider, (previous, next) {
      if (next != null) {
        state = state.copyWith(
          user: next,
          isLoading: false,
          error: null,
          isAuthenticated: true,
        );
      } else if (state.user?.email.contains('demo.com') == true) {
        // Demo user logged out
        state = state.copyWith(
          user: null,
          isAuthenticated: false,
        );
      }
    });
  }

  /// Get current user
  UserModel? get currentUser {
    final authUser = ref.read(authControllerProvider).value;
    final demoUser = ref.read(demoUserProvider);
    return authUser ?? demoUser;
  }

  /// Check if user has specific role
  bool hasRole(UserRole role) {
    return currentUser?.role == role;
  }

  /// Check if user has any of the specified roles
  bool hasAnyRole(List<UserRole> roles) {
    final user = currentUser;
    return user != null && roles.contains(user.role);
  }

  /// Check if user can access web interface
  bool get canAccessWeb {
    return currentUser?.role.hasWebAccess ?? false;
  }

  /// Check if user can access mobile interface
  bool get canAccessMobile {
    return currentUser?.role.hasMobileAccess ?? false;
  }

  /// Check permissions
  bool hasPermission(String permission) {
    final user = currentUser;
    if (user == null) return false;
    final userPermissions = AuthMiddleware.getUserPermissions(user.role);
    return userPermissions.contains(permission);
  }

  /// Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true);

    try {
      // Clear demo user using the proper logout method
      ref.read(demoUserProvider.notifier).logout();

      // Sign out from Firebase
      await ref.read(authControllerProvider.notifier).signOut();

      // Force clear state
      state = state.copyWith(
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Force clear authentication state (for emergency logout)
  void forceClear() {
    state = state.copyWith(
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,
    );
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    state = state.copyWith(isLoading: true);

    try {
      final user = currentUser;
      state = state.copyWith(
        user: user,
        isLoading: false,
        isAuthenticated: user != null,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        isAuthenticated: false,
      );
    }
  }
}

/// Role-based authentication provider
final roleBasedAuthProvider =
    StateNotifierProvider<RoleBasedAuthNotifier, RoleBasedAuthState>((ref) {
  return RoleBasedAuthNotifier(ref);
});

/// Current user provider (unified)
final unifiedCurrentUserProvider = Provider<UserModel?>((ref) {
  final authUser = ref.watch(authControllerProvider).value;
  final demoUser = ref.watch(demoUserProvider);
  return authUser ?? demoUser;
});

/// Authentication status provider
final isAuthenticatedUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user != null;
});

/// Role-specific providers
final isCustomerUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.isCustomer ?? false;
});

final isSalesRepUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.isSalesRep ?? false;
});

final isMerchantUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.isMerchant ?? false;
});

final isAdminUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.isAdmin ?? false;
});

final isBossUnifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.isBoss ?? false;
});

/// Web access provider
final hasWebAccessProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.hasWebAccess ?? false;
});

/// Mobile access provider
final hasMobileAccessProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.hasMobileAccess ?? false;
});

/// Permission providers
final canManageProductsProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.canManageProducts ?? false;
});

final canViewAnalyticsProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.canViewAnalytics ?? false;
});

final canManageMerchantsProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.canManageMerchants ?? false;
});

final canManageAdminsProvider = Provider<bool>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.canManageAdmins ?? false;
});

/// User display name provider
final userDisplayNameProvider = Provider<String>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.displayName ?? 'Guest';
});

/// User role display name provider
final userRoleDisplayNameProvider = Provider<String>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return user?.role.displayName ?? 'Guest';
});

/// Dashboard route provider
final userDashboardRouteProvider = Provider<String>((ref) {
  final user = ref.watch(unifiedCurrentUserProvider);
  return AuthMiddleware.getDashboardRoute(user?.role);
});
