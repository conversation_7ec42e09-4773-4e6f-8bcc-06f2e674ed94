import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../models/product_model.dart';
import '../../../shared/themes/customer_theme.dart';
import 'modern_ui_components.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final Widget? trailing;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      margin: const EdgeInsets.symmetric(
          horizontal: CustomerTheme.spacingM, vertical: CustomerTheme.spacingS),
      onTap: onTap,
      child: Row(
        children: [
          // Product image with modern styling
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
              gradient: LinearGradient(
                colors: [
                  CustomerTheme.primaryTeal.withOpacity(0.1),
                  CustomerTheme.primaryTeal.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: CustomerTheme.primaryTeal.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
              child: product.imageUrl != null
                  ? CachedNetworkImage(
                      imageUrl: product.imageUrl!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.grey[200]!,
                              Colors.grey[100]!,
                            ],
                          ),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                CustomerTheme.primaryTeal),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              CustomerTheme.primaryTeal.withOpacity(0.1),
                              CustomerTheme.primaryTeal.withOpacity(0.05),
                            ],
                          ),
                        ),
                        child: const Icon(
                          Icons.shopping_bag_outlined,
                          color: CustomerTheme.primaryTeal,
                          size: 32,
                        ),
                      ),
                    )
                  : Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            CustomerTheme.primaryTeal.withOpacity(0.1),
                            CustomerTheme.primaryTeal.withOpacity(0.05),
                          ],
                        ),
                      ),
                      child: const Icon(
                        Icons.shopping_bag_outlined,
                        color: CustomerTheme.primaryTeal,
                        size: 32,
                      ),
                    ),
            ),
          ),

          const SizedBox(width: CustomerTheme.spacingM),

          // Product details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product name
                Text(
                  product.name,
                  style: CustomerTheme.headingSmall.copyWith(
                    color: CustomerTheme.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: CustomerTheme.spacingXS),

                // Product description
                if (product.description.isNotEmpty)
                  Text(
                    product.description,
                    style: CustomerTheme.bodySmall.copyWith(
                      color: CustomerTheme.textSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                const SizedBox(height: CustomerTheme.spacingS),

                // Price and unit info
                Row(
                  children: [
                    // Price
                    Text(
                      '₦${product.pricePerUnit.toStringAsFixed(2)}',
                      style: CustomerTheme.headingSmall.copyWith(
                        color: CustomerTheme.primaryTeal,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(width: CustomerTheme.spacingS),

                    // Unit type
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: CustomerTheme.spacingS,
                        vertical: CustomerTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: CustomerTheme.primaryTeal.withOpacity(0.1),
                        borderRadius:
                            BorderRadius.circular(CustomerTheme.smallRadius),
                      ),
                      child: Text(
                        'per ${product.unitType}',
                        style: CustomerTheme.labelMedium.copyWith(
                          color: CustomerTheme.primaryTeal,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: CustomerTheme.spacingS),

                // Stock status and category
                Row(
                  children: [
                    // Stock status
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: CustomerTheme.spacingS,
                        vertical: CustomerTheme.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: CustomerTheme.getStockStatusColor(
                          product.stockQuantity,
                          product.lowStockThreshold,
                        ).withOpacity(0.1),
                        borderRadius:
                            BorderRadius.circular(CustomerTheme.smallRadius),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            product.isOutOfStock
                                ? Icons.error_outline
                                : product.isLowStock
                                    ? Icons.warning_outlined
                                    : Icons.check_circle_outline,
                            size: 12,
                            color: CustomerTheme.getStockStatusColor(
                              product.stockQuantity,
                              product.lowStockThreshold,
                            ),
                          ),
                          const SizedBox(width: CustomerTheme.spacingXS),
                          Text(
                            product.isOutOfStock
                                ? 'Out of Stock'
                                : product.isLowStock
                                    ? 'Low Stock'
                                    : 'In Stock',
                            style: CustomerTheme.labelMedium.copyWith(
                              color: CustomerTheme.getStockStatusColor(
                                product.stockQuantity,
                                product.lowStockThreshold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),

                    // Category
                    if (product.category.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: CustomerTheme.spacingS,
                          vertical: CustomerTheme.spacingXS,
                        ),
                        decoration: BoxDecoration(
                          color: CustomerTheme.textTertiary.withOpacity(0.1),
                          borderRadius:
                              BorderRadius.circular(CustomerTheme.smallRadius),
                        ),
                        child: Text(
                          product.category,
                          style: CustomerTheme.labelMedium.copyWith(
                            color: CustomerTheme.textTertiary,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          // Trailing widget (e.g., add to cart button)
          if (trailing != null) ...[
            const SizedBox(width: CustomerTheme.spacingS),
            trailing!,
          ],
        ],
      ),
    );
  }
}

class ProductGridCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final Widget? trailing;

  const ProductGridCard({
    super.key,
    required this.product,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colors.grey[200],
                ),
                child: ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                  child: product.imageUrl != null
                      ? CachedNetworkImage(
                          imageUrl: product.imageUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 40,
                          ),
                        )
                      : const Icon(
                          Icons.shopping_bag,
                          color: Colors.grey,
                          size: 40,
                        ),
                ),
              ),
            ),

            // Product details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // Price
                    Text(
                      '₦${product.pricePerUnit.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),

                    const SizedBox(height: 2),

                    // Unit type
                    Text(
                      'per ${product.unitType}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),

                    const Spacer(),

                    // Stock status and trailing
                    Row(
                      children: [
                        Icon(
                          product.isOutOfStock
                              ? Icons.error
                              : product.isLowStock
                                  ? Icons.warning
                                  : Icons.check_circle,
                          size: 12,
                          color: product.isOutOfStock
                              ? Colors.red
                              : product.isLowStock
                                  ? Colors.orange
                                  : Colors.green,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            product.isOutOfStock ? 'Out of Stock' : 'In Stock',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: product.isOutOfStock
                                          ? Colors.red
                                          : Colors.green,
                                      fontSize: 10,
                                    ),
                          ),
                        ),
                        if (trailing != null) trailing!,
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
