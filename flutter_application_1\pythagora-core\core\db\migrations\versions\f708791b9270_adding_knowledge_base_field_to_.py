"""Adding knowledge_base field to ProjectState

Revision ID: f708791b9270
Revises: c8905d4ce784
Create Date: 2024-12-22 12:13:14.979169

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f708791b9270"
down_revision: Union[str, None] = "c8905d4ce784"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.add_column(sa.Column("knowledge_base", sa.JSON(), server_default="{}", nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.drop_column("knowledge_base")

    # ### end Alembic commands ###
