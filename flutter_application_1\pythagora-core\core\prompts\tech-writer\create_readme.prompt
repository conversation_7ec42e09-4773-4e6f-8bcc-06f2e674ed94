You are working on a project called "{{ state.branch.project.name }}" and you need to create a detailed documentation for current state of project. Your first task is to create README.md file.

{% include "partials/project_details.prompt" %}
{% include "partials/features_list.prompt" %}

~~FILE_DESCRIPTIONS_IN_THE_CODEBASE~~
{% include "partials/files_descriptions.prompt" %}
~~END_OF_FILE_DESCRIPTIONS_IN_THE_CODEBASE~~

Project dependencies are installed using "npm install" in the root folder. That will install all the necessary dependencies in the root, client and server folders.
Project is started using "npm run start" in the root folder. That will start both the frontend and backend.

Now, based on the project details provided, think step by step and create README.md file for this project. The file should have the following format:

# Project name

Short description (a few sentences) of the project based on the project details.

## Overview

Description of the architecture and technologies used in the project, and the project structure.

## Features

Description of what the app can do and how it can be used.

## Getting started

### Requirements

Required technologies/setup needed on the computer to run the project.

### Quickstart

How to set up the project and run it

### License

The project is proprietary (not open source), just output the standard Copyright (c) 2024. template here.
