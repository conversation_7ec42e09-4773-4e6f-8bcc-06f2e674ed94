import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../models/account_settings_model.dart';
import '../../../../providers/account_provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../shared/widgets/settings_tile.dart';

class PrivacySettingsScreen extends ConsumerWidget {
  const PrivacySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userId = ref.watch(currentUserIdProvider);
    final settingsAsync = ref.watch(accountSettingsProvider(userId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Settings'),
      ),
      body: settingsAsync.when(
        data: (settings) {
          if (settings == null) {
            return const Center(
              child: Text('Unable to load privacy settings'),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Privacy Overview
                _buildPrivacyOverview(context),

                // Profile Visibility Section
                SettingsSection(
                  title: 'Profile Visibility',
                  tiles: [
                    SettingsTile.selection(
                      leading: const Icon(Icons.visibility),
                      title: 'Profile Visibility',
                      subtitle: settings.profileVisibility.displayName,
                      onTap: () => _showProfileVisibilitySelector(context, ref, settings),
                    ),
                    PrivacySettingsTile(
                      title: 'Show Email Address',
                      subtitle: 'Allow others to see your email',
                      value: settings.showEmail,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(showEmail: value),
                      ),
                    ),
                    PrivacySettingsTile(
                      title: 'Show Phone Number',
                      subtitle: 'Allow others to see your phone number',
                      value: settings.showPhone,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(showPhone: value),
                      ),
                    ),
                    PrivacySettingsTile(
                      title: 'Show Last Seen',
                      subtitle: 'Show when you were last active',
                      value: settings.showLastSeen,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(showLastSeen: value),
                      ),
                    ),
                  ],
                ),

                // Communication Settings Section
                SettingsSection(
                  title: 'Communication',
                  tiles: [
                    PrivacySettingsTile(
                      title: 'Allow Direct Messages',
                      subtitle: 'Let others send you direct messages',
                      value: settings.allowDirectMessages,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(allowDirectMessages: value),
                      ),
                    ),
                    PrivacySettingsTile(
                      title: 'Allow Friend Requests',
                      subtitle: 'Let others send you friend requests',
                      value: settings.allowFriendRequests,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(allowFriendRequests: value),
                      ),
                    ),
                  ],
                ),

                // Data Sharing Section
                SettingsSection(
                  title: 'Data Sharing',
                  tiles: [
                    PrivacySettingsTile(
                      title: 'Analytics Data',
                      subtitle: 'Share usage data to improve the app',
                      value: settings.shareDataForAnalytics,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(shareDataForAnalytics: value),
                      ),
                    ),
                    PrivacySettingsTile(
                      title: 'Marketing Data',
                      subtitle: 'Share data for personalized marketing',
                      value: settings.shareDataForMarketing,
                      onChanged: (value) => _updateSettings(
                        ref,
                        settings.copyWith(shareDataForMarketing: value),
                      ),
                    ),
                  ],
                ),

                // Blocked Users Section
                SettingsSection(
                  title: 'Blocked Users',
                  tiles: [
                    SettingsTile.navigation(
                      leading: const Icon(Icons.block),
                      title: 'Blocked Users',
                      subtitle: '${settings.blockedUsers.length} users blocked',
                      onTap: () => _showBlockedUsers(context, settings),
                    ),
                  ],
                ),

                // Data Management Section
                SettingsSection(
                  title: 'Data Management',
                  tiles: [
                    SettingsTile.navigation(
                      leading: const Icon(Icons.download),
                      title: 'Download My Data',
                      subtitle: 'Export all your account data',
                      onTap: () => _exportData(context, ref),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.delete_forever),
                      title: 'Delete All Data',
                      subtitle: 'Permanently delete your account and data',
                      onTap: () => _showDeleteDataDialog(context, ref),
                    ),
                  ],
                ),

                // Privacy Information Section
                SettingsSection(
                  title: 'Privacy Information',
                  tiles: [
                    SettingsTile.navigation(
                      leading: const Icon(Icons.policy),
                      title: 'Privacy Policy',
                      subtitle: 'Read our privacy policy',
                      onTap: () => _showPrivacyPolicy(context),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.description),
                      title: 'Terms of Service',
                      subtitle: 'Read our terms of service',
                      onTap: () => _showTermsOfService(context),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.cookie),
                      title: 'Cookie Settings',
                      subtitle: 'Manage cookie preferences',
                      onTap: () => _showCookieSettings(context),
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading privacy settings',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacyOverview(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.primary.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.privacy_tip,
                color: colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'Your Privacy Matters',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Control how your information is shared and who can see your profile. '
            'You can change these settings at any time.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  void _showProfileVisibilitySelector(BuildContext context, WidgetRef ref, AccountSettingsModel settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Profile Visibility'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ProfileVisibility.values.map((visibility) {
            return RadioListTile<ProfileVisibility>(
              title: Text(visibility.displayName),
              subtitle: Text(visibility.description),
              value: visibility,
              groupValue: settings.profileVisibility,
              onChanged: (value) {
                if (value != null) {
                  _updateSettings(ref, settings.copyWith(profileVisibility: value));
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showBlockedUsers(BuildContext context, AccountSettingsModel settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Blocked Users'),
        content: settings.blockedUsers.isEmpty
            ? const Text('No blocked users.')
            : SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: settings.blockedUsers.length,
                  itemBuilder: (context, index) {
                    final userId = settings.blockedUsers[index];
                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: Text('User $userId'),
                      trailing: TextButton(
                        onPressed: () {
                          // Unblock user logic would go here
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('User management coming soon!'),
                            ),
                          );
                        },
                        child: const Text('Unblock'),
                      ),
                    );
                  },
                ),
              ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context, WidgetRef ref) async {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      try {
        await ref.read(accountControllerProvider.notifier).exportUserData(userId);

        // In a real app, you would save this data to a file or send it via email
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Data export feature coming soon!'),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to export data: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  void _showDeleteDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Data'),
        content: const Text(
          'This will permanently delete your account and all associated data. '
          'This action cannot be undone. Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Account deletion feature coming soon!'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete Forever'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'This is where the privacy policy would be displayed. '
            'In a real app, this would contain the full privacy policy text '
            'or open a web view to display the policy.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms of Service'),
        content: const SingleChildScrollView(
          child: Text(
            'This is where the terms of service would be displayed. '
            'In a real app, this would contain the full terms text '
            'or open a web view to display the terms.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCookieSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cookie Settings'),
        content: const Text(
          'Cookie management settings would be displayed here. '
          'This would allow users to control which cookies are accepted.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _updateSettings(WidgetRef ref, AccountSettingsModel settings) {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      ref.read(accountControllerProvider.notifier).updateAccountSettings(
        userId: userId,
        settings: settings,
      );
    }
  }
}
