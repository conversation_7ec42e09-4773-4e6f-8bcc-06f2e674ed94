import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../providers/product_provider.dart';
import '../../../providers/auth_provider.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_button.dart';
import '../../../models/product_model.dart';
import 'external_barcode_input.dart';

class AddProductDialog extends ConsumerStatefulWidget {
  final ProductModel? product; // For editing existing product

  const AddProductDialog({super.key, this.product});

  @override
  ConsumerState<AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends ConsumerState<AddProductDialog> {
  final _formKey = GlobalKey<FormState>();
  final _barcodeController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _unitTypeController = TextEditingController();
  final _unitsPerCartonController = TextEditingController();
  final _pricePerCartonController = TextEditingController();
  final _stockQuantityController = TextEditingController();
  final _lowStockThresholdController = TextEditingController();
  final _imageUrlController = TextEditingController();

  String _selectedCategory = 'General';
  bool _isLoading = false;

  final List<String> _categories = [
    'General',
    'Electronics',
    'Clothing',
    'Food & Beverages',
    'Books & Media',
    'Home & Garden',
    'Sports & Outdoors',
    'Beauty & Health',
    'Toys & Games',
    'Automotive',
    'Office Supplies',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.product != null) {
      _populateFields(widget.product!);
    } else {
      // Set default values
      _unitTypeController.text = 'unit';
      _unitsPerCartonController.text = '1';
      _lowStockThresholdController.text = '5';
    }
  }

  void _populateFields(ProductModel product) {
    _barcodeController.text = product.barcode;
    _nameController.text = product.name;
    _descriptionController.text = product.description;
    _unitTypeController.text = product.unitType;
    _unitsPerCartonController.text = product.unitsPerCarton.toString();
    _pricePerCartonController.text = product.pricePerCarton.toString();
    _stockQuantityController.text = product.stockQuantity.toString();
    _lowStockThresholdController.text = product.lowStockThreshold.toString();
    _imageUrlController.text = product.imageUrl ?? '';
    _selectedCategory = product.category;
  }

  @override
  void dispose() {
    _barcodeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _unitTypeController.dispose();
    _unitsPerCartonController.dispose();
    _pricePerCartonController.dispose();
    _stockQuantityController.dispose();
    _lowStockThresholdController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.product != null;

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  isEditing ? 'Edit Product' : 'Add New Product',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const Divider(),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // External barcode scanner input
                      ExternalBarcodeInput(
                        controller: _barcodeController,
                        label: 'Product Barcode *',
                        hint: 'Scan with barcode scanner or enter manually',
                        autoSubmit: false,
                        showScanIndicator: true,
                        onBarcodeScanned: (barcode) {
                          // Show success message when barcode is scanned
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.check_circle,
                                      color: Colors.white),
                                  const SizedBox(width: 8),
                                  Text('Barcode scanned: $barcode'),
                                ],
                              ),
                              backgroundColor: Colors.green,
                              duration: const Duration(seconds: 2),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );

                          // Auto-focus next field for faster data entry
                          Future.delayed(const Duration(milliseconds: 300), () {
                            FocusScope.of(context).nextFocus();
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Barcode is required';
                          }
                          if (value.trim().length < 6) {
                            return 'Barcode must be at least 6 characters';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Product name
                      CustomTextField(
                        controller: _nameController,
                        label: 'Product Name *',
                        prefixIcon: Icons.shopping_bag,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a product name';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description
                      CustomTextField(
                        controller: _descriptionController,
                        label: 'Description',
                        prefixIcon: Icons.description,
                        maxLines: 3,
                      ),

                      const SizedBox(height: 16),

                      // Category
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category *',
                          prefixIcon: Icon(Icons.category),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => _selectedCategory = value);
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a category';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Unit type and units per carton
                      Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: _unitTypeController,
                              label: 'Unit Type *',
                              prefixIcon: Icons.straighten,
                              hint: 'e.g., kg, unit, liter',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter unit type';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomTextField(
                              controller: _unitsPerCartonController,
                              label: 'Units per Carton *',
                              prefixIcon: Icons.inventory_2,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Required';
                                }
                                final number = int.tryParse(value);
                                if (number == null || number <= 0) {
                                  return 'Invalid number';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Price per carton
                      CustomTextField(
                        controller: _pricePerCartonController,
                        label: 'Price per Carton (₦) *',
                        prefixIcon: Icons.attach_money,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter price per carton';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Stock quantity and low stock threshold
                      Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: _stockQuantityController,
                              label: 'Stock Quantity *',
                              prefixIcon: Icons.inventory,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Required';
                                }
                                final number = int.tryParse(value);
                                if (number == null || number < 0) {
                                  return 'Invalid';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomTextField(
                              controller: _lowStockThresholdController,
                              label: 'Low Stock Alert',
                              prefixIcon: Icons.warning,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Required';
                                }
                                final number = int.tryParse(value);
                                if (number == null || number < 0) {
                                  return 'Invalid';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Image URL (optional)
                      CustomTextField(
                        controller: _imageUrlController,
                        label: 'Image URL (Optional)',
                        prefixIcon: Icons.image,
                        hint: 'https://example.com/image.jpg',
                      ),

                      const SizedBox(height: 16),

                      // Price calculation display
                      if (_pricePerCartonController.text.isNotEmpty &&
                          _unitsPerCartonController.text.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calculate, color: Colors.blue[700]),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Price per unit: ₦${_calculatePricePerUnit().toStringAsFixed(2)}',
                                  style: TextStyle(
                                    color: Colors.blue[700],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: LoadingButton(
                    onPressed: _saveProduct,
                    isLoading: _isLoading,
                    child: Text(isEditing ? 'Update Product' : 'Add Product'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  double _calculatePricePerUnit() {
    final pricePerCarton = double.tryParse(_pricePerCartonController.text) ?? 0;
    final unitsPerCarton = int.tryParse(_unitsPerCartonController.text) ?? 1;
    return unitsPerCarton > 0 ? pricePerCarton / unitsPerCarton : 0;
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final user = ref.read(currentUserProvider).value;
      if (user == null) {
        throw Exception('User not found');
      }

      final productController = ref.read(productControllerProvider.notifier);

      if (widget.product != null) {
        // Update existing product
        final updatedProduct = widget.product!.copyWith(
          barcode: _barcodeController.text.trim(),
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          unitType: _unitTypeController.text.trim(),
          unitsPerCarton: int.parse(_unitsPerCartonController.text),
          pricePerCarton: double.parse(_pricePerCartonController.text),
          stockQuantity: int.parse(_stockQuantityController.text),
          lowStockThreshold: int.parse(_lowStockThresholdController.text),
          imageUrl: _imageUrlController.text.trim().isEmpty
              ? null
              : _imageUrlController.text.trim(),
          category: _selectedCategory,
        );

        await productController.updateProduct(updatedProduct);
      } else {
        // Add new product
        await productController.addProduct(
          barcode: _barcodeController.text.trim(),
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          merchantId: user.id,
          mallId: user.mallId ?? 'default-mall',
          unitType: _unitTypeController.text.trim(),
          unitsPerCarton: int.parse(_unitsPerCartonController.text),
          pricePerCarton: double.parse(_pricePerCartonController.text),
          stockQuantity: int.parse(_stockQuantityController.text),
          lowStockThreshold: int.parse(_lowStockThresholdController.text),
          imageUrl: _imageUrlController.text.trim().isEmpty
              ? null
              : _imageUrlController.text.trim(),
          category: _selectedCategory,
          createdBy: user.id,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.product != null
                  ? 'Product updated successfully'
                  : 'Product added successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving product: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
