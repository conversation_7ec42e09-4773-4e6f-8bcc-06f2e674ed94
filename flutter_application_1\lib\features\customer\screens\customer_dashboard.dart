import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../providers/cart_provider.dart';
import '../../../providers/customer_navigation_provider.dart';
import '../../../shared/themes/customer_theme.dart';
import '../../../shared/widgets/page_transitions.dart';
import '../widgets/professional_navigation.dart';
import 'customer_home_screen.dart';
import 'scanner_screen.dart';
import 'cart_screen.dart';
import 'history_screen.dart';
import 'profile_screen.dart';

class CustomerDashboard extends ConsumerStatefulWidget {
  const CustomerDashboard({super.key});

  @override
  ConsumerState<CustomerDashboard> createState() => _CustomerDashboardState();
}

class _CustomerDashboardState extends ConsumerState<CustomerDashboard>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late AnimationController _navAnimationController;
  late Animation<double> _fadeAnimation;

  final List<Widget> _screens = const [
    CustomerHomeScreen(),
    ScannerScreen(),
    CartScreen(),
    HistoryScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);

    _animationController = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );

    _navAnimationController = AnimationController(
      duration: CustomerTheme.fastAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    _navAnimationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _navAnimationController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    final currentIndex = ref.read(customerDashboardNavigationProvider);
    if (index != currentIndex) {
      HapticFeedback.lightImpact();
      ref.read(customerDashboardNavigationProvider.notifier).state = index;

      // Smooth page transition
      _pageController.animateToPage(
        index,
        duration: CustomerTheme.mediumAnimation,
        curve: Curves.easeInOutCubic,
      );

      // Animate navigation indicator
      _navAnimationController.reset();
      _navAnimationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(customerDashboardNavigationProvider);

    // Listen to navigation changes and update page controller
    ref.listen(customerDashboardNavigationProvider, (previous, next) {
      if (previous != next && _pageController.hasClients) {
        _pageController.animateToPage(
          next,
          duration: CustomerTheme.mediumAnimation,
          curve: Curves.easeInOutCubic,
        );
      }
    });

    return Theme(
      data: CustomerTheme.lightTheme,
      child: Scaffold(
        backgroundColor: CustomerTheme.backgroundLight,
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              ref.read(customerDashboardNavigationProvider.notifier).state =
                  index;
              HapticFeedback.selectionClick();
            },
            children: _screens.map((screen) {
              return AnimatedPageWrapper(
                duration: CustomerTheme.mediumAnimation,
                curve: Curves.easeInOutCubic,
                child: screen,
              );
            }).toList(),
          ),
        ),
        bottomNavigationBar: _buildProfessionalBottomNav(),
      ),
    );
  }

  Widget _buildProfessionalBottomNav() {
    final cartItemCount = ref.watch(cartItemCountProvider);
    final currentIndex = ref.watch(customerDashboardNavigationProvider);

    final navigationItems = [
      NavigationItem(
        icon: Icons.home_outlined,
        selectedIcon: Icons.home,
        label: 'Home',
        color: CustomerTheme.primaryTeal,
      ),
      NavigationItem(
        icon: Icons.qr_code_scanner_outlined,
        selectedIcon: Icons.qr_code_scanner,
        label: 'Scan',
        color: CustomerTheme.accentOrange,
      ),
      NavigationItem(
        icon: Icons.shopping_cart_outlined,
        selectedIcon: Icons.shopping_cart,
        label: 'Cart',
        color: CustomerTheme.accentBlue,
        badgeCount: cartItemCount > 0 ? cartItemCount : null,
      ),
      NavigationItem(
        icon: Icons.history_outlined,
        selectedIcon: Icons.history,
        label: 'History',
        color: CustomerTheme.accentPurple,
      ),
      NavigationItem(
        icon: Icons.person_outline,
        selectedIcon: Icons.person,
        label: 'Profile',
        color: CustomerTheme.textSecondary,
      ),
    ];

    return ProfessionalBottomNavigation(
      items: navigationItems,
      currentIndex: currentIndex,
      onTap: _onTabTapped,
    );
  }
}
