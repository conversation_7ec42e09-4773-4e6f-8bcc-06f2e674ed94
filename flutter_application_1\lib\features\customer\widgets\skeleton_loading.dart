import 'package:flutter/material.dart';
import '../../../shared/themes/customer_theme.dart';

/// Skeleton Loading Components - Inspired by Facebook, LinkedIn, Instagram
/// Provides smooth shimmer effects for better perceived performance

class SkeletonLoader extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;

  const SkeletonLoader({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor,
    this.highlightColor,
  });

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutSine,
    ));
    
    if (widget.isLoading) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(SkeletonLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                widget.baseColor ?? CustomerTheme.borderTertiary,
                widget.highlightColor ?? CustomerTheme.surfaceContainer,
                widget.baseColor ?? CustomerTheme.borderTertiary,
              ],
              stops: const [0.0, 0.5, 1.0],
              begin: Alignment(_animation.value - 1, 0),
              end: Alignment(_animation.value, 0),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Skeleton Box - Basic building block
class SkeletonBox extends StatelessWidget {
  final double? width;
  final double? height;
  final double borderRadius;

  const SkeletonBox({
    super.key,
    this.width,
    this.height,
    this.borderRadius = CustomerTheme.smallRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: CustomerTheme.borderTertiary,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}

/// Skeleton Text Lines
class SkeletonText extends StatelessWidget {
  final int lines;
  final double? width;
  final double height;
  final double spacing;

  const SkeletonText({
    super.key,
    this.lines = 1,
    this.width,
    this.height = 16,
    this.spacing = 8,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(lines, (index) {
        final isLast = index == lines - 1;
        final lineWidth = isLast && lines > 1 
            ? (width ?? double.infinity) * 0.7 
            : width;
        
        return Padding(
          padding: EdgeInsets.only(bottom: isLast ? 0 : spacing),
          child: SkeletonBox(
            width: lineWidth,
            height: height,
            borderRadius: height / 4,
          ),
        );
      }),
    );
  }
}

/// Skeleton Avatar
class SkeletonAvatar extends StatelessWidget {
  final double size;

  const SkeletonAvatar({
    super.key,
    this.size = 48,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonBox(
      width: size,
      height: size,
      borderRadius: size / 2,
    );
  }
}

/// Skeleton Card for Home Screen
class SkeletonHomeCard extends StatelessWidget {
  const SkeletonHomeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(CustomerTheme.spacing20),
      decoration: BoxDecoration(
        color: CustomerTheme.surfaceElevated,
        borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
        boxShadow: CustomerTheme.shadowMD,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const SkeletonAvatar(size: 56),
              const SizedBox(width: CustomerTheme.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonText(
                      width: 120,
                      height: 14,
                    ),
                    const SizedBox(height: CustomerTheme.spacing8),
                    SkeletonText(
                      width: 180,
                      height: 18,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: CustomerTheme.spacing20),
          SkeletonBox(
            width: double.infinity,
            height: 60,
            borderRadius: CustomerTheme.mediumRadius,
          ),
        ],
      ),
    );
  }
}

/// Skeleton Quick Action Grid
class SkeletonQuickActions extends StatelessWidget {
  const SkeletonQuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SkeletonText(
          width: 140,
          height: 20,
        ),
        const SizedBox(height: CustomerTheme.spacing16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: CustomerTheme.spacing16,
            mainAxisSpacing: CustomerTheme.spacing16,
            childAspectRatio: 1.2,
          ),
          itemCount: 4,
          itemBuilder: (context, index) => SkeletonBox(
            width: double.infinity,
            height: double.infinity,
            borderRadius: CustomerTheme.mediumRadius,
          ),
        ),
      ],
    );
  }
}

/// Complete Skeleton Home Screen
class SkeletonHomeScreen extends StatelessWidget {
  const SkeletonHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SkeletonLoader(
      isLoading: true,
      child: Scaffold(
        backgroundColor: CustomerTheme.backgroundSecondary,
        body: CustomScrollView(
          physics: const NeverScrollableScrollPhysics(),
          slivers: [
            // Skeleton App Bar
            SliverAppBar(
              expandedHeight: 120,
              floating: true,
              pinned: true,
              backgroundColor: CustomerTheme.primaryTeal,
              elevation: 0,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    color: CustomerTheme.primaryTeal,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(CustomerTheme.extraLargeRadius),
                      bottomRight: Radius.circular(CustomerTheme.extraLargeRadius),
                    ),
                  ),
                ),
              ),
              title: Row(
                children: [
                  SkeletonBox(
                    width: 40,
                    height: 40,
                    borderRadius: CustomerTheme.mediumRadius,
                  ),
                  const SizedBox(width: CustomerTheme.spacing12),
                  const SkeletonText(
                    width: 120,
                    height: 18,
                  ),
                ],
              ),
              actions: [
                SkeletonBox(
                  width: 40,
                  height: 40,
                  borderRadius: CustomerTheme.mediumRadius,
                ),
                const SizedBox(width: CustomerTheme.spacing16),
              ],
            ),
            
            // Skeleton Content
            SliverPadding(
              padding: const EdgeInsets.all(CustomerTheme.spacing16),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  const SkeletonHomeCard(),
                  const SizedBox(height: CustomerTheme.spacing24),
                  const SkeletonQuickActions(),
                  const SizedBox(height: CustomerTheme.spacing24),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SkeletonText(
                        width: 160,
                        height: 20,
                      ),
                      const SizedBox(height: CustomerTheme.spacing16),
                      SizedBox(
                        height: 200,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: 3,
                          itemBuilder: (context, index) => Container(
                            width: 160,
                            margin: const EdgeInsets.only(right: CustomerTheme.spacing16),
                            child: SkeletonBox(
                              width: double.infinity,
                              height: double.infinity,
                              borderRadius: CustomerTheme.mediumRadius,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: CustomerTheme.spacing64),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
