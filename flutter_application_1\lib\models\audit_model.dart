enum AuditEventType {
  login,
  logout,
  create,
  update,
  delete,
  view,
  export,
  import,
  systemChange,
  securityEvent,
  paymentEvent,
  userManagement,
}

enum AuditSeverity {
  low,
  medium,
  high,
  critical,
}

class AuditEvent {
  final String id;
  final String userId;
  final String userName;
  final String userRole;
  final AuditEventType eventType;
  final AuditSeverity severity;
  final String module;
  final String action;
  final String description;
  final Map<String, dynamic> metadata;
  final Map<String, dynamic> beforeData;
  final Map<String, dynamic> afterData;
  final String ipAddress;
  final String userAgent;
  final String sessionId;
  final DateTime timestamp;
  final String mallId;
  final bool isSuccessful;
  final String errorMessage;

  AuditEvent({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userRole,
    required this.eventType,
    required this.severity,
    required this.module,
    required this.action,
    required this.description,
    required this.metadata,
    required this.beforeData,
    required this.afterData,
    required this.ipAddress,
    required this.userAgent,
    required this.sessionId,
    required this.timestamp,
    required this.mallId,
    required this.isSuccessful,
    this.errorMessage = '',
  });

  factory AuditEvent.fromJson(Map<String, dynamic> json) {
    return AuditEvent(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userRole: json['userRole'] ?? '',
      eventType: AuditEventType.values.firstWhere(
        (e) => e.toString() == 'AuditEventType.${json['eventType']}',
        orElse: () => AuditEventType.view,
      ),
      severity: AuditSeverity.values.firstWhere(
        (e) => e.toString() == 'AuditSeverity.${json['severity']}',
        orElse: () => AuditSeverity.low,
      ),
      module: json['module'] ?? '',
      action: json['action'] ?? '',
      description: json['description'] ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      beforeData: Map<String, dynamic>.from(json['beforeData'] ?? {}),
      afterData: Map<String, dynamic>.from(json['afterData'] ?? {}),
      ipAddress: json['ipAddress'] ?? '',
      userAgent: json['userAgent'] ?? '',
      sessionId: json['sessionId'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      mallId: json['mallId'] ?? '',
      isSuccessful: json['isSuccessful'] ?? true,
      errorMessage: json['errorMessage'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userRole': userRole,
      'eventType': eventType.toString().split('.').last,
      'severity': severity.toString().split('.').last,
      'module': module,
      'action': action,
      'description': description,
      'metadata': metadata,
      'beforeData': beforeData,
      'afterData': afterData,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'sessionId': sessionId,
      'timestamp': timestamp.toIso8601String(),
      'mallId': mallId,
      'isSuccessful': isSuccessful,
      'errorMessage': errorMessage,
    };
  }

  String get eventTypeDisplayName {
    switch (eventType) {
      case AuditEventType.login:
        return 'Login';
      case AuditEventType.logout:
        return 'Logout';
      case AuditEventType.create:
        return 'Create';
      case AuditEventType.update:
        return 'Update';
      case AuditEventType.delete:
        return 'Delete';
      case AuditEventType.view:
        return 'View';
      case AuditEventType.export:
        return 'Export';
      case AuditEventType.import:
        return 'Import';
      case AuditEventType.systemChange:
        return 'System Change';
      case AuditEventType.securityEvent:
        return 'Security Event';
      case AuditEventType.paymentEvent:
        return 'Payment Event';
      case AuditEventType.userManagement:
        return 'User Management';
    }
  }

  String get severityDisplayName {
    switch (severity) {
      case AuditSeverity.low:
        return 'Low';
      case AuditSeverity.medium:
        return 'Medium';
      case AuditSeverity.high:
        return 'High';
      case AuditSeverity.critical:
        return 'Critical';
    }
  }
}

class AuditFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<AuditEventType> eventTypes;
  final List<AuditSeverity> severities;
  final List<String> userIds;
  final List<String> modules;
  final String? searchQuery;
  final bool? isSuccessful;
  final String? mallId;

  AuditFilter({
    this.startDate,
    this.endDate,
    this.eventTypes = const [],
    this.severities = const [],
    this.userIds = const [],
    this.modules = const [],
    this.searchQuery,
    this.isSuccessful,
    this.mallId,
  });

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'eventTypes': eventTypes.map((e) => e.toString().split('.').last).toList(),
      'severities': severities.map((e) => e.toString().split('.').last).toList(),
      'userIds': userIds,
      'modules': modules,
      'searchQuery': searchQuery,
      'isSuccessful': isSuccessful,
      'mallId': mallId,
    };
  }
}

class AuditSummary {
  final int totalEvents;
  final int successfulEvents;
  final int failedEvents;
  final int criticalEvents;
  final int highSeverityEvents;
  final Map<String, int> eventsByType;
  final Map<String, int> eventsByModule;
  final Map<String, int> eventsByUser;
  final DateTime generatedAt;

  AuditSummary({
    required this.totalEvents,
    required this.successfulEvents,
    required this.failedEvents,
    required this.criticalEvents,
    required this.highSeverityEvents,
    required this.eventsByType,
    required this.eventsByModule,
    required this.eventsByUser,
    required this.generatedAt,
  });

  factory AuditSummary.fromJson(Map<String, dynamic> json) {
    return AuditSummary(
      totalEvents: json['totalEvents'] ?? 0,
      successfulEvents: json['successfulEvents'] ?? 0,
      failedEvents: json['failedEvents'] ?? 0,
      criticalEvents: json['criticalEvents'] ?? 0,
      highSeverityEvents: json['highSeverityEvents'] ?? 0,
      eventsByType: Map<String, int>.from(json['eventsByType'] ?? {}),
      eventsByModule: Map<String, int>.from(json['eventsByModule'] ?? {}),
      eventsByUser: Map<String, int>.from(json['eventsByUser'] ?? {}),
      generatedAt: DateTime.parse(json['generatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  double get successRate {
    if (totalEvents == 0) return 0;
    return (successfulEvents / totalEvents) * 100;
  }

  double get criticalEventRate {
    if (totalEvents == 0) return 0;
    return (criticalEvents / totalEvents) * 100;
  }
}