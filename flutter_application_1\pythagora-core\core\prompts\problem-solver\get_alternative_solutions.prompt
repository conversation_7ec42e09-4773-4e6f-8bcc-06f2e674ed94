You are working on an app called "{{ state.branch.project.name }}" and you need to write code for the entire {% if state.epics|length > 1 %}feature{% else %}app{% endif %} based on the tasks that the tech lead gives you. So that you understand better what you're working on, you're given other specs for "{{ state.branch.project.name }}" as well.

Here is a high level description of "{{ state.branch.project.name }}":
```
{{ state.specification.description }}
```

{% include "partials/features_list.prompt" %}

We've broken the development of this {% if state.epics|length > 1 %}feature{% else %}app{% endif %} down to these tasks:
```
{% for task in state.tasks %}
{{ loop.index }}. {{ task.description }}{% if task.get("status") == "done" %} (completed){% endif %}
{% endfor %}
```

{% if state.current_task %}
You are currently working on, and have to focus only on, this task:
```
{{ state.current_task.description }}
```

{% endif %}
A part of the app is already finished.
{% include "partials/files_list.prompt" %}

You are trying to solve an issue that your colleague is reporting.
{% if previous_solutions|length > 0 %}
You tried {{ previous_solutions|length }} times to solve it but it was unsuccessful. In last few attempts, your colleague gave you this report:
{% for solution in previous_solutions[-3:] %}
----------------------------start_of_report_{{ loop.index }}----------------------------
{{ solution.user_feedback }}
----------------------------end_of_report_{{ loop.index }}----------------------------

Then, you gave the following proposal (proposal_{{ loop.index }}) of what needs to be done to fix the issue:
----------------------------start_of_proposal_{{ loop.index }}----------------------------
{{ solution.description }}
----------------------------end_of_of_proposal_{{ loop.index }}----------------------------

{% if not loop.last %}
Then, upon implementing these changes, your colleague came back with the following report:
{% endif %}
{% endfor %}
{% endif %}

{% if user_input != '' %}
Your colleague who is testing the app "{{ state.branch.project.name }}" sent you this report now:
```
{{ user_input }}
```

You tried to solve this problem before but your colleague is telling you that you got into a loop where all your tries end up the same way - with an error.
{%- endif -%}

It seems that the solutions you're proposing aren't working.

Now, think step by step about 5 alternative solutions to get this code to work that are most probable to solve this issue.

Every proposed solution needs to be concrete and not vague (eg, it cannot be "Review and change apps functionality") and based on the code changes. A solution can be complex if it's related to the same part of the code (eg. "Try changing the input variables X, Y and Z to a method N").

Order them in the order of the biggest probability of fixing the problem. A developer will then go through this list item by item, try to implement it, and check if it solved the issue until the end of the list.
