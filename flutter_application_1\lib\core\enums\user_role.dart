enum UserRole {
  customer('customer', 'Customer'),
  salesRep('sales_rep', 'Sales Rep'),
  merchant('merchant', 'Merchant'),
  admin('admin', 'Admin'),
  boss('boss', 'Boss');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.customer,
    );
  }

  bool get isCustomer => this == UserRole.customer;
  bool get isSalesRep => this == UserRole.salesRep;
  bool get isMerchant => this == UserRole.merchant;
  bool get isAdmin => this == UserRole.admin;
  bool get isBoss => this == UserRole.boss;

  bool get hasWebAccess => !isCustomer;
  bool get hasMobileAccess => isCustomer;
  
  bool get canManageProducts => isMerchant || isAdmin || isBoss;
  bool get canViewAnalytics => isMerchant || isAdmin || isBoss;
  bool get canManageMerchants => isAdmin || isBoss;
  bool get canManageAdmins => isBoss;
}
