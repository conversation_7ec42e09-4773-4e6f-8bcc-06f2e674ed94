import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../providers/cart_provider.dart';
import '../../../providers/auth_provider.dart';
import '../../../providers/customer_navigation_provider.dart';
import '../../../shared/themes/customer_theme.dart';
import '../widgets/modern_ui_components.dart';
import '../widgets/premium_ui_components.dart';
import '../widgets/skeleton_loading.dart';

class CustomerHomeScreen extends ConsumerStatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  ConsumerState<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends ConsumerState<CustomerHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: CustomerTheme.mediumAnimation,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: CustomerTheme.slowAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cart = ref.watch(cartProvider);
    final userAsync = ref.watch(currentUserProvider);

    return Scaffold(
      backgroundColor: CustomerTheme.backgroundLight,
      body: userAsync.when(
        loading: () => const SkeletonHomeScreen(),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: CustomerTheme.errorRed,
              ),
              const SizedBox(height: CustomerTheme.spacing16),
              Text(
                'Something went wrong',
                style: CustomerTheme.headingSmall.copyWith(
                  color: CustomerTheme.textPrimary,
                ),
              ),
              const SizedBox(height: CustomerTheme.spacing8),
              Text(
                'Please try again later',
                style: CustomerTheme.bodyMedium.copyWith(
                  color: CustomerTheme.textSecondary,
                ),
              ),
              const SizedBox(height: CustomerTheme.spacing24),
              ModernButton(
                text: 'Retry',
                icon: Icons.refresh,
                onPressed: () {
                  ref.invalidate(currentUserProvider);
                },
              ),
            ],
          ),
        ),
        data: (user) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildAppBar(context, user),
                SliverPadding(
                  padding: const EdgeInsets.all(CustomerTheme.spacing16),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildWelcomeCard(context, user),
                      const SizedBox(height: CustomerTheme.spacing24),
                      _buildQuickActions(context, cart),
                      const SizedBox(height: CustomerTheme.spacing24),
                      _buildFeaturedSection(context),
                      const SizedBox(height: CustomerTheme.spacing24),
                      _buildRecentActivity(context),
                      const SizedBox(height: CustomerTheme.spacing64),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, dynamic user) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: CustomerTheme.primaryGradient,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(CustomerTheme.extraLargeRadius),
              bottomRight: Radius.circular(CustomerTheme.extraLargeRadius),
            ),
          ),
        ),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
            ),
            child: const Icon(
              Icons.shopping_bag_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: CustomerTheme.spacingM),
          const Expanded(
            child: Text(
              'Mall Shopping',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            // TODO: Implement notifications
          },
          icon: Stack(
            children: [
              const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 24,
              ),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: CustomerTheme.errorRed,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: CustomerTheme.spacingS),
      ],
    );
  }

  Widget _buildWelcomeCard(BuildContext context, dynamic user) {
    final timeOfDay = DateTime.now().hour;
    String greeting = 'Good morning';
    if (timeOfDay >= 12 && timeOfDay < 17) {
      greeting = 'Good afternoon';
    } else if (timeOfDay >= 17) {
      greeting = 'Good evening';
    }

    return PremiumCard(
      padding: const EdgeInsets.all(CustomerTheme.spacing20),
      boxShadow: CustomerTheme.shadowLG,
      enableHoverEffect: true,
      enablePressEffect: false,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: CustomerTheme.primarySubtleGradient,
                  borderRadius:
                      BorderRadius.circular(CustomerTheme.largeRadius),
                  boxShadow: CustomerTheme.shadowSM,
                ),
                child: Center(
                  child: Text(
                    user?.firstName?.substring(0, 1).toUpperCase() ?? 'U',
                    style: CustomerTheme.headingSmall.copyWith(
                      color: CustomerTheme.primaryTealDark,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: CustomerTheme.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$greeting!',
                      style: CustomerTheme.bodyMedium.copyWith(
                        color: CustomerTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: CustomerTheme.spacing4),
                    Text(
                      user?.displayName ?? 'Welcome',
                      style: CustomerTheme.headingSmall.copyWith(
                        color: CustomerTheme.textPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: CustomerTheme.spacing20),
          Container(
            padding: const EdgeInsets.all(CustomerTheme.spacing16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomerTheme.primaryTealSurface,
                  CustomerTheme.accentBlueSurface,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
              border: Border.all(
                color: CustomerTheme.primaryTeal.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(CustomerTheme.spacing8),
                  decoration: BoxDecoration(
                    color: CustomerTheme.primaryTeal.withOpacity(0.1),
                    borderRadius:
                        BorderRadius.circular(CustomerTheme.smallRadius),
                  ),
                  child: const Icon(
                    Icons.location_on_outlined,
                    color: CustomerTheme.primaryTeal,
                    size: 20,
                  ),
                ),
                const SizedBox(width: CustomerTheme.spacing12),
                Expanded(
                  child: Text(
                    user?.mallId != null
                        ? 'Shopping at ${user!.mallId}'
                        : 'Select your mall location',
                    style: CustomerTheme.bodyMedium.copyWith(
                      color: CustomerTheme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (user?.mallId == null)
                  Container(
                    padding: const EdgeInsets.all(CustomerTheme.spacing4),
                    decoration: BoxDecoration(
                      color: CustomerTheme.primaryTeal.withOpacity(0.1),
                      borderRadius:
                          BorderRadius.circular(CustomerTheme.smallRadius),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios,
                      color: CustomerTheme.primaryTeal,
                      size: 14,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, dynamic cart) {
    final actions = [
      _QuickAction(
        icon: Icons.qr_code_scanner,
        label: 'Scan Product',
        color: CustomerTheme.primaryTeal,
        gradient: CustomerTheme.primaryGradient,
        onTap: () {
          HapticFeedback.lightImpact();
          // Navigate to scanner tab
          ref.read(customerDashboardNavigationProvider.notifier).state = 1;
        },
      ),
      _QuickAction(
        icon: Icons.shopping_cart,
        label: 'View Cart',
        color: CustomerTheme.accentBlue,
        gradient: LinearGradient(
          colors: [
            CustomerTheme.accentBlue,
            CustomerTheme.accentBlue.withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        badge: cart.itemCount > 0 ? cart.itemCount.toString() : null,
        onTap: () {
          HapticFeedback.lightImpact();
          // Navigate to cart tab
          ref.read(customerDashboardNavigationProvider.notifier).state = 2;
        },
      ),
      _QuickAction(
        icon: Icons.history,
        label: 'Purchase History',
        color: CustomerTheme.accentPurple,
        gradient: LinearGradient(
          colors: [
            CustomerTheme.accentPurple,
            CustomerTheme.accentPurple.withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        onTap: () {
          HapticFeedback.lightImpact();
          // Navigate to history tab
          ref.read(customerDashboardNavigationProvider.notifier).state = 3;
        },
      ),
      _QuickAction(
        icon: Icons.local_offer,
        label: 'Offers & Deals',
        color: CustomerTheme.accentOrange,
        gradient: CustomerTheme.accentOrangeGradient,
        onTap: () {
          HapticFeedback.lightImpact();
          // TODO: Implement offers screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Offers & Deals coming soon!'),
              backgroundColor: CustomerTheme.accentOrange,
            ),
          );
        },
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: CustomerTheme.headingSmall.copyWith(
            color: CustomerTheme.textPrimary,
          ),
        ),
        const SizedBox(height: CustomerTheme.spacingM),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: CustomerTheme.spacingM,
            mainAxisSpacing: CustomerTheme.spacingM,
            childAspectRatio: 1.2,
          ),
          itemCount: actions.length,
          itemBuilder: (context, index) =>
              _buildQuickActionCard(actions[index]),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(_QuickAction action) {
    return PremiumCard(
      onTap: action.onTap,
      gradient: action.gradient,
      boxShadow: CustomerTheme.shadowMD,
      enableHoverEffect: true,
      enablePressEffect: true,
      padding: const EdgeInsets.all(CustomerTheme.spacing16),
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.25),
                  borderRadius:
                      BorderRadius.circular(CustomerTheme.largeRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  action.icon,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(height: CustomerTheme.spacing12),
              Text(
                action.label,
                style: CustomerTheme.labelLarge.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
          if (action.badge != null)
            Positioned(
              top: -4,
              right: -4,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: CustomerTheme.spacing8,
                  vertical: CustomerTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: CustomerTheme.errorRed,
                  borderRadius: BorderRadius.circular(CustomerTheme.spacing12),
                  boxShadow: CustomerTheme.shadowSM,
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                child: Text(
                  action.badge!,
                  style: CustomerTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 11,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeaturedSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Products',
              style: CustomerTheme.headingSmall.copyWith(
                color: CustomerTheme.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                // TODO: Navigate to featured products
              },
              child: Text(
                'View All',
                style: CustomerTheme.labelLarge.copyWith(
                  color: CustomerTheme.primaryTeal,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: CustomerTheme.spacingM),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (context, index) => _buildFeaturedProductCard(index),
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProductCard(int index) {
    final products = [
      {'name': 'Premium Coffee', 'price': '₦2,500', 'image': '☕'},
      {'name': 'Fresh Bread', 'price': '₦800', 'image': '🍞'},
      {'name': 'Organic Milk', 'price': '₦1,200', 'image': '🥛'},
      {'name': 'Energy Drink', 'price': '₦600', 'image': '🥤'},
      {'name': 'Chocolate Bar', 'price': '₦450', 'image': '🍫'},
    ];

    final product = products[index];

    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: CustomerTheme.spacingM),
      decoration: CustomerTheme.getCardDecoration(
        boxShadow: CustomerTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomerTheme.primaryTeal.withOpacity(0.1),
                  CustomerTheme.accentBlue.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(CustomerTheme.mediumRadius),
                topRight: Radius.circular(CustomerTheme.mediumRadius),
              ),
            ),
            child: Center(
              child: Text(
                product['image']!,
                style: const TextStyle(fontSize: 40),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(CustomerTheme.spacingS),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['name']!,
                  style: CustomerTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: CustomerTheme.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  product['price']!,
                  style: CustomerTheme.labelLarge.copyWith(
                    color: CustomerTheme.primaryTeal,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: CustomerTheme.headingSmall.copyWith(
            color: CustomerTheme.textPrimary,
          ),
        ),
        const SizedBox(height: CustomerTheme.spacingM),
        Container(
          padding: const EdgeInsets.all(CustomerTheme.spacingM),
          decoration: CustomerTheme.getCardDecoration(
            boxShadow: CustomerTheme.cardShadow,
          ),
          child: Column(
            children: [
              _buildActivityItem(
                icon: Icons.shopping_bag,
                title: 'Last Purchase',
                subtitle: 'Coffee & Snacks - ₦3,200',
                time: '2 hours ago',
                color: CustomerTheme.successGreen,
              ),
              const Divider(height: CustomerTheme.spacingL),
              _buildActivityItem(
                icon: Icons.qr_code_scanner,
                title: 'Product Scanned',
                subtitle: 'Premium Coffee',
                time: '1 day ago',
                color: CustomerTheme.primaryTeal,
              ),
              const Divider(height: CustomerTheme.spacingL),
              _buildActivityItem(
                icon: Icons.local_offer,
                title: 'Offer Used',
                subtitle: '10% off on beverages',
                time: '3 days ago',
                color: CustomerTheme.accentOrange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(CustomerTheme.spacingS),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(CustomerTheme.smallRadius),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: CustomerTheme.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: CustomerTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: CustomerTheme.textPrimary,
                ),
              ),
              Text(
                subtitle,
                style: CustomerTheme.bodySmall.copyWith(
                  color: CustomerTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: CustomerTheme.bodySmall.copyWith(
            color: CustomerTheme.textTertiary,
          ),
        ),
      ],
    );
  }
}

class _QuickAction {
  final IconData icon;
  final String label;
  final Color color;
  final Gradient gradient;
  final String? badge;
  final VoidCallback onTap;

  const _QuickAction({
    required this.icon,
    required this.label,
    required this.color,
    required this.gradient,
    required this.onTap,
    this.badge,
  });
}
