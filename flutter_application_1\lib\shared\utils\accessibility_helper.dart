import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import '../themes/customer_theme.dart';

/// Accessibility Helper - WCAG 2.1 AA Compliance
/// Ensures the app meets accessibility standards for all users

class AccessibilityHelper {
  /// Minimum touch target size (44dp for iOS, 48dp for Material Design)
  static const double minTouchTarget = 44.0;
  static const double recommendedTouchTarget = 48.0;
  
  /// WCAG AA contrast ratios
  static const double minContrastRatio = 4.5;
  static const double minContrastRatioLarge = 3.0;
  
  /// Check if a color combination meets WCAG contrast requirements
  static bool meetsContrastRequirement(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    final ratio = calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText ? minContrastRatioLarge : minContrastRatio;
    return ratio >= requiredRatio;
  }
  
  /// Calculate contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  /// Calculate relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }
  
  static double _linearizeColorComponent(double component) {
    return component <= 0.03928
        ? component / 12.92
        : math.pow((component + 0.055) / 1.055, 2.4).toDouble();
  }
  
  /// Create accessible button with proper touch targets
  static Widget accessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    String? semanticLabel,
    String? tooltip,
    double minWidth = recommendedTouchTarget,
    double minHeight = recommendedTouchTarget,
    EdgeInsetsGeometry? padding,
  }) {
    return Semantics(
      label: semanticLabel,
      button: true,
      enabled: onPressed != null,
      child: Tooltip(
        message: tooltip ?? '',
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minWidth: minWidth,
            minHeight: minHeight,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
              child: Padding(
                padding: padding ?? const EdgeInsets.all(CustomerTheme.spacing8),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// Create accessible text with proper contrast
  static Widget accessibleText(
    String text, {
    TextStyle? style,
    Color? color,
    Color? backgroundColor,
    String? semanticLabel,
    bool isHeading = false,
    int? maxLines,
    TextAlign? textAlign,
  }) {
    final effectiveStyle = style ?? CustomerTheme.bodyMedium;
    final effectiveColor = color ?? CustomerTheme.textPrimary;
    final effectiveBackground = backgroundColor ?? CustomerTheme.surfaceElevated;
    
    // Check contrast ratio
    final hasGoodContrast = meetsContrastRequirement(
      effectiveColor,
      effectiveBackground,
      isLargeText: (effectiveStyle.fontSize ?? 14) >= 18,
    );
    
    if (!hasGoodContrast) {
      debugPrint('⚠️ Accessibility Warning: Poor contrast ratio for text "$text"');
    }
    
    return Semantics(
      label: semanticLabel ?? text,
      header: isHeading,
      child: Text(
        text,
        style: effectiveStyle.copyWith(color: effectiveColor),
        maxLines: maxLines,
        textAlign: textAlign,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }
  
  /// Create accessible form field with proper labels
  static Widget accessibleTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    String? errorText,
    String? helperText,
    bool isRequired = false,
    bool obscureText = false,
    TextInputType? keyboardType,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    Widget? suffixIcon,
    Widget? prefixIcon,
  }) {
    return Semantics(
      label: '$label${isRequired ? ' (required)' : ''}',
      textField: true,
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          errorText: errorText,
          helperText: helperText,
          suffixIcon: suffixIcon,
          prefixIcon: prefixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: CustomerTheme.spacing16,
            vertical: CustomerTheme.spacing12,
          ),
        ),
        validator: isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '$label is required';
                }
                return null;
              }
            : null,
      ),
    );
  }
  
  /// Create accessible card with proper semantics
  static Widget accessibleCard({
    required Widget child,
    String? semanticLabel,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? backgroundColor,
  }) {
    return Semantics(
      label: semanticLabel,
      button: onTap != null,
      child: Card(
        margin: margin,
        color: backgroundColor,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(CustomerTheme.mediumRadius),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(CustomerTheme.spacing16),
            child: child,
          ),
        ),
      ),
    );
  }
  
  /// Create accessible loading indicator
  static Widget accessibleLoadingIndicator({
    String? semanticLabel,
    double size = 24.0,
    Color? color,
  }) {
    return Semantics(
      label: semanticLabel ?? 'Loading',
      liveRegion: true,
      child: SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? CustomerTheme.primaryTeal,
          ),
        ),
      ),
    );
  }
  
  /// Create accessible icon with semantic meaning
  static Widget accessibleIcon(
    IconData icon, {
    String? semanticLabel,
    double size = 24.0,
    Color? color,
  }) {
    return Semantics(
      label: semanticLabel,
      image: true,
      child: Icon(
        icon,
        size: size,
        color: color,
      ),
    );
  }
  
  /// Create accessible navigation item
  static Widget accessibleNavigationItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    int? badgeCount,
  }) {
    final semanticLabel = badgeCount != null && badgeCount > 0
        ? '$label, $badgeCount items'
        : label;
    
    return Semantics(
      label: semanticLabel,
      button: true,
      selected: isSelected,
      child: accessibleButton(
        onPressed: onTap,
        semanticLabel: semanticLabel,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  icon,
                  color: isSelected 
                      ? CustomerTheme.primaryTeal 
                      : CustomerTheme.textTertiary,
                ),
                if (badgeCount != null && badgeCount > 0)
                  Positioned(
                    right: -6,
                    top: -6,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: CustomerTheme.errorRed,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        badgeCount > 99 ? '99+' : badgeCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected 
                    ? CustomerTheme.primaryTeal 
                    : CustomerTheme.textTertiary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Announce message to screen readers
  static void announceToScreenReader(String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }
  
  /// Focus management helper
  static void requestFocus(BuildContext context, FocusNode focusNode) {
    FocusScope.of(context).requestFocus(focusNode);
  }
  
  /// Check if device has accessibility features enabled
  static bool hasAccessibilityFeatures(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.accessibleNavigation ||
           mediaQuery.boldText ||
           mediaQuery.highContrast ||
           mediaQuery.textScaleFactor > 1.0;
  }
  
  /// Get appropriate text scale factor
  static double getAccessibleTextScale(BuildContext context) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    // Clamp between 0.8 and 2.0 for better readability
    return textScaleFactor.clamp(0.8, 2.0);
  }
}

/// Import math for luminance calculation
import 'dart:math' as math;
