import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/cart_item_model.dart';
import '../models/product_model.dart';

// Cart state
class CartState {
  final List<CartItemModel> items;
  final double subtotal;
  final double tax;
  final double total;

  CartState({
    this.items = const [],
    this.subtotal = 0.0,
    this.tax = 0.0,
    this.total = 0.0,
  });

  CartState copyWith({
    List<CartItemModel>? items,
    double? subtotal,
    double? tax,
    double? total,
  }) {
    return CartState(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      total: total ?? this.total,
    );
  }

  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
}

// Cart controller
class CartController extends StateNotifier<CartState> {
  CartController() : super(CartState());

  static const double taxRate = 0.075; // 7.5% VAT

  void addItem(ProductModel product, {int quantity = 1}) {
    final existingItemIndex = state.items.indexWhere(
      (item) => item.productId == product.id,
    );

    List<CartItemModel> updatedItems;

    if (existingItemIndex >= 0) {
      // Update existing item quantity
      updatedItems = List.from(state.items);
      final existingItem = updatedItems[existingItemIndex];
      updatedItems[existingItemIndex] = existingItem.copyWith(
        quantity: existingItem.quantity + quantity,
      );
    } else {
      // Add new item
      final cartItem = CartItemModel.fromProduct(
        product.id,
        product.barcode,
        product.name,
        product.description,
        product.unitType,
        product.pricePerUnit,
        product.imageUrl,
        product.category,
        quantity: quantity,
      );
      updatedItems = [...state.items, cartItem];
    }

    _updateCart(updatedItems);
  }

  void removeItem(String productId) {
    final updatedItems = state.items.where((item) => item.productId != productId).toList();
    _updateCart(updatedItems);
  }

  void updateItemQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeItem(productId);
      return;
    }

    final updatedItems = state.items.map((item) {
      if (item.productId == productId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    _updateCart(updatedItems);
  }

  void incrementItem(String productId) {
    final item = state.items.firstWhere((item) => item.productId == productId);
    updateItemQuantity(productId, item.quantity + 1);
  }

  void decrementItem(String productId) {
    final item = state.items.firstWhere((item) => item.productId == productId);
    updateItemQuantity(productId, item.quantity - 1);
  }

  void clearCart() {
    state = CartState();
  }

  void _updateCart(List<CartItemModel> items) {
    final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
    final tax = subtotal * taxRate;
    final total = subtotal + tax;

    state = CartState(
      items: items,
      subtotal: subtotal,
      tax: tax,
      total: total,
    );
  }

  // Get cart item by product ID
  CartItemModel? getItem(String productId) {
    try {
      return state.items.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Check if product is in cart
  bool hasItem(String productId) {
    return state.items.any((item) => item.productId == productId);
  }

  // Get quantity of specific product in cart
  int getItemQuantity(String productId) {
    final item = getItem(productId);
    return item?.quantity ?? 0;
  }
}

// Cart provider
final cartProvider = StateNotifierProvider<CartController, CartState>((ref) {
  return CartController();
});

// Cart item count provider
final cartItemCountProvider = Provider<int>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.itemCount;
});

// Cart total provider
final cartTotalProvider = Provider<double>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.total;
});

// Cart subtotal provider
final cartSubtotalProvider = Provider<double>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.subtotal;
});

// Cart tax provider
final cartTaxProvider = Provider<double>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.tax;
});

// Cart is empty provider
final cartIsEmptyProvider = Provider<bool>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.isEmpty;
});

// Specific item quantity provider
final itemQuantityProvider = Provider.family<int, String>((ref, productId) {
  final cart = ref.watch(cartProvider);
  return cart.items
      .where((item) => item.productId == productId)
      .fold(0, (sum, item) => sum + item.quantity);
});

// Item in cart provider
final itemInCartProvider = Provider.family<bool, String>((ref, productId) {
  final cart = ref.watch(cartProvider);
  return cart.items.any((item) => item.productId == productId);
});

// Cart items provider
final cartItemsProvider = Provider<List<CartItemModel>>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.items;
});
