import 'package:cloud_firestore/cloud_firestore.dart';

enum PurchaseOrderStatus {
  draft('draft', 'Draft'),
  sent('sent', 'Sent to Supplier'),
  confirmed('confirmed', 'Confirmed'),
  partiallyReceived('partially_received', 'Partially Received'),
  received('received', 'Received'),
  cancelled('cancelled', 'Cancelled'),
  disputed('disputed', 'Disputed');

  const PurchaseOrderStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static PurchaseOrderStatus fromString(String value) {
    return PurchaseOrderStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => PurchaseOrderStatus.draft,
    );
  }

  bool get canBeEdited => this == PurchaseOrderStatus.draft;
  bool get canBeSent => this == PurchaseOrderStatus.draft;
  bool get canBeCancelled => [PurchaseOrderStatus.draft, PurchaseOrderStatus.sent, PurchaseOrderStatus.confirmed].contains(this);
  bool get canReceiveItems => [PurchaseOrderStatus.confirmed, PurchaseOrderStatus.partiallyReceived].contains(this);
  bool get isCompleted => this == PurchaseOrderStatus.received;
}

class PurchaseOrderItem {
  final String productId;
  final String productName;
  final String productBarcode;
  final int quantityOrdered;
  final int quantityReceived;
  final double unitPrice;
  final double totalPrice;
  final String? notes;

  PurchaseOrderItem({
    required this.productId,
    required this.productName,
    required this.productBarcode,
    required this.quantityOrdered,
    this.quantityReceived = 0,
    required this.unitPrice,
    required this.totalPrice,
    this.notes,
  });

  bool get isFullyReceived => quantityReceived >= quantityOrdered;
  bool get isPartiallyReceived => quantityReceived > 0 && quantityReceived < quantityOrdered;
  int get remainingQuantity => quantityOrdered - quantityReceived;
  double get receivedValue => quantityReceived * unitPrice;

  factory PurchaseOrderItem.fromJson(Map<String, dynamic> json) {
    return PurchaseOrderItem(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      productBarcode: json['productBarcode'] ?? '',
      quantityOrdered: json['quantityOrdered'] ?? 0,
      quantityReceived: json['quantityReceived'] ?? 0,
      unitPrice: (json['unitPrice'] ?? 0.0).toDouble(),
      totalPrice: (json['totalPrice'] ?? 0.0).toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'productBarcode': productBarcode,
      'quantityOrdered': quantityOrdered,
      'quantityReceived': quantityReceived,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'notes': notes,
    };
  }

  PurchaseOrderItem copyWith({
    String? productId,
    String? productName,
    String? productBarcode,
    int? quantityOrdered,
    int? quantityReceived,
    double? unitPrice,
    double? totalPrice,
    String? notes,
  }) {
    return PurchaseOrderItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productBarcode: productBarcode ?? this.productBarcode,
      quantityOrdered: quantityOrdered ?? this.quantityOrdered,
      quantityReceived: quantityReceived ?? this.quantityReceived,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      notes: notes ?? this.notes,
    );
  }
}

class PurchaseOrderModel {
  final String id;
  final String poNumber;
  final String supplierId;
  final String supplierName;
  final String merchantId;
  final String mallId;
  final List<PurchaseOrderItem> items;
  final double subtotal;
  final double tax;
  final double shippingCost;
  final double total;
  final PurchaseOrderStatus status;
  final DateTime expectedDeliveryDate;
  final DateTime? actualDeliveryDate;
  final String? notes;
  final String? terms;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? sentAt;
  final DateTime? confirmedAt;
  final String createdBy;
  final String? receivedBy;
  final Map<String, dynamic>? metadata;

  PurchaseOrderModel({
    required this.id,
    required this.poNumber,
    required this.supplierId,
    required this.supplierName,
    required this.merchantId,
    required this.mallId,
    required this.items,
    required this.subtotal,
    this.tax = 0.0,
    this.shippingCost = 0.0,
    required this.total,
    this.status = PurchaseOrderStatus.draft,
    required this.expectedDeliveryDate,
    this.actualDeliveryDate,
    this.notes,
    this.terms,
    required this.createdAt,
    required this.updatedAt,
    this.sentAt,
    this.confirmedAt,
    required this.createdBy,
    this.receivedBy,
    this.metadata,
  });

  int get totalItemsOrdered => items.fold(0, (total, item) => total + item.quantityOrdered);
  int get totalItemsReceived => items.fold(0, (total, item) => total + item.quantityReceived);
  double get receivedValue => items.fold(0.0, (total, item) => total + item.receivedValue);
  bool get isOverdue => DateTime.now().isAfter(expectedDeliveryDate) && !status.isCompleted;
  bool get isFullyReceived => items.every((item) => item.isFullyReceived);
  bool get isPartiallyReceived => items.any((item) => item.isPartiallyReceived);

  factory PurchaseOrderModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PurchaseOrderModel(
      id: doc.id,
      poNumber: data['poNumber'] ?? '',
      supplierId: data['supplierId'] ?? '',
      supplierName: data['supplierName'] ?? '',
      merchantId: data['merchantId'] ?? '',
      mallId: data['mallId'] ?? '',
      items: (data['items'] as List<dynamic>?)
              ?.map((item) => PurchaseOrderItem.fromJson(item))
              .toList() ??
          [],
      subtotal: (data['subtotal'] ?? 0.0).toDouble(),
      tax: (data['tax'] ?? 0.0).toDouble(),
      shippingCost: (data['shippingCost'] ?? 0.0).toDouble(),
      total: (data['total'] ?? 0.0).toDouble(),
      status: PurchaseOrderStatus.fromString(data['status'] ?? 'draft'),
      expectedDeliveryDate: (data['expectedDeliveryDate'] as Timestamp).toDate(),
      actualDeliveryDate: data['actualDeliveryDate'] != null ? (data['actualDeliveryDate'] as Timestamp).toDate() : null,
      notes: data['notes'],
      terms: data['terms'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      sentAt: data['sentAt'] != null ? (data['sentAt'] as Timestamp).toDate() : null,
      confirmedAt: data['confirmedAt'] != null ? (data['confirmedAt'] as Timestamp).toDate() : null,
      createdBy: data['createdBy'] ?? '',
      receivedBy: data['receivedBy'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'poNumber': poNumber,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'merchantId': merchantId,
      'mallId': mallId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shippingCost': shippingCost,
      'total': total,
      'status': status.value,
      'expectedDeliveryDate': Timestamp.fromDate(expectedDeliveryDate),
      'actualDeliveryDate': actualDeliveryDate != null ? Timestamp.fromDate(actualDeliveryDate!) : null,
      'notes': notes,
      'terms': terms,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'sentAt': sentAt != null ? Timestamp.fromDate(sentAt!) : null,
      'confirmedAt': confirmedAt != null ? Timestamp.fromDate(confirmedAt!) : null,
      'createdBy': createdBy,
      'receivedBy': receivedBy,
      'metadata': metadata,
    };
  }

  PurchaseOrderModel copyWith({
    String? poNumber,
    String? supplierId,
    String? supplierName,
    List<PurchaseOrderItem>? items,
    double? subtotal,
    double? tax,
    double? shippingCost,
    double? total,
    PurchaseOrderStatus? status,
    DateTime? expectedDeliveryDate,
    DateTime? actualDeliveryDate,
    String? notes,
    String? terms,
    DateTime? updatedAt,
    DateTime? sentAt,
    DateTime? confirmedAt,
    String? receivedBy,
    Map<String, dynamic>? metadata,
  }) {
    return PurchaseOrderModel(
      id: id,
      poNumber: poNumber ?? this.poNumber,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      merchantId: merchantId,
      mallId: mallId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shippingCost: shippingCost ?? this.shippingCost,
      total: total ?? this.total,
      status: status ?? this.status,
      expectedDeliveryDate: expectedDeliveryDate ?? this.expectedDeliveryDate,
      actualDeliveryDate: actualDeliveryDate ?? this.actualDeliveryDate,
      notes: notes ?? this.notes,
      terms: terms ?? this.terms,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sentAt: sentAt ?? this.sentAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      createdBy: createdBy,
      receivedBy: receivedBy ?? this.receivedBy,
      metadata: metadata ?? this.metadata,
    );
  }
}
