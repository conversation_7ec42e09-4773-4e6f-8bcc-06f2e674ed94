import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui';

import '../../../models/product_model.dart';
import '../../../providers/demo_product_provider.dart';
import '../../../providers/settings_provider.dart';
import '../../../services/logout_service.dart';
// Removed unused imports for cleaner code
import '../widgets/enhanced_add_product_dialog.dart';
import 'dashboard_screen.dart';
import 'analytics_screen.dart';
import 'settings_screen.dart';

/// Enhanced Color Palette for Professional Inventory Management
class EnhancedInventoryColors {
  // Primary Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFFF8F00)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFE53E3E), Color(0xFFD32F2F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient infoGradient = LinearGradient(
    colors: [Color(0xFF00BCD4), Color(0xFF0097A7)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Category Colors with Gradients
  static const Map<String, LinearGradient> categoryGradients = {
    'Electronics': LinearGradient(
      colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    'Clothing': LinearGradient(
      colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    'Food': LinearGradient(
      colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    'Books': LinearGradient(
      colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    'Home': LinearGradient(
      colors: [Color(0xFF00BCD4), Color(0xFF0097A7)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    'Sports': LinearGradient(
      colors: [Color(0xFFE91E63), Color(0xFFC2185B)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  };

  // Background Gradients
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [Colors.white, Color(0xFFFAFAFA)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Professional Navigation Button Gradients (matching admin/boss standards)
  static const LinearGradient dashboardGradient = LinearGradient(
    colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient inventoryGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient analyticsGradient = LinearGradient(
    colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient settingsGradient = LinearGradient(
    colors: [Color(0xFF607D8B), Color(0xFF455A64)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Glassmorphism Colors
  static Color glassMorphismBackground = Colors.white.withOpacity(0.25);
  static Color glassMorphismBorder = Colors.white.withOpacity(0.18);
}

/// Modern Inventory Management Dashboard
/// Professional web-optimized design following contemporary inventory management patterns
class ModernInventoryDashboard extends ConsumerStatefulWidget {
  const ModernInventoryDashboard({super.key});

  @override
  ConsumerState<ModernInventoryDashboard> createState() =>
      _ModernInventoryDashboardState();
}

class _ModernInventoryDashboardState
    extends ConsumerState<ModernInventoryDashboard>
    with TickerProviderStateMixin {
  // Navigation State
  int _selectedNavIndex = 1; // Start with Inventory selected
  bool _isSidebarCollapsed = false;

  // Hover state for product table rows
  int _hoveredIndex = -1;

  // Search and Filter State
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedStockStatus = 'All';

  // Selection State
  final Set<String> _selectedProducts = {};
  bool _isAllSelected = false;

  // Animation Controllers
  late AnimationController _pageController;
  late AnimationController _sidebarController;
  late Animation<double> _pageAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize Animation Controllers
    _pageController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _sidebarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeOutCubic,
    );

    // Sidebar animation removed for simplicity

    _pageController.forward();
    _sidebarController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _sidebarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(demoCurrentUserProductsProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: EnhancedInventoryColors.backgroundGradient,
        ),
        child: Row(
          children: [
            // Enhanced Modern Sidebar Navigation
            _buildModernSidebar(),

            // Enhanced Main Content Area
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFFF8FAFC).withOpacity(0.9),
                      const Color(0xFFE2E8F0).withOpacity(0.8),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // Enhanced Top Navigation Bar
                    _buildTopNavigationBar(),

                    // Enhanced Main Dashboard Content
                    Expanded(
                      child: _buildMainContent(productsAsync),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced Modern Sidebar with Glassmorphism
  Widget _buildModernSidebar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOutCubic,
      width: _isSidebarCollapsed ? 80 : 280,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.95),
            Colors.white.withOpacity(0.85),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(4, 0),
          ),
          BoxShadow(
            color: const Color(0xFF667EEA).withOpacity(0.05),
            blurRadius: 30,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.9),
                  Colors.white.withOpacity(0.7),
                ],
              ),
              border: Border(
                right: BorderSide(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                // Enhanced Logo/Brand Section
                _buildEnhancedSidebarHeader(),

                // Enhanced Navigation Menu and Bottom Sections
                Expanded(
                  child: Column(
                    children: [
                      // Navigation Menu
                      Expanded(
                        child: _buildEnhancedNavigationMenu(),
                      ),

                      // Enhanced User Profile Section
                      _buildEnhancedUserProfileSection(),

                      // Logout Section
                      _buildLogoutSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Enhanced Sidebar Header with Glassmorphism
  Widget _buildEnhancedSidebarHeader() {
    return Container(
      height: 90,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.3),
            Colors.white.withOpacity(0.1),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Enhanced Logo with Animation
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * value),
                child: Container(
                  width: 45,
                  height: 45,
                  decoration: BoxDecoration(
                    gradient: EnhancedInventoryColors.primaryGradient,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF667EEA).withOpacity(0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.inventory_2_rounded,
                    color: Colors.white,
                    size: 26,
                  ),
                ),
              );
            },
          ),
          if (!_isSidebarCollapsed) ...[
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ShaderMask(
                    shaderCallback: (bounds) => EnhancedInventoryColors
                        .primaryGradient
                        .createShader(bounds),
                    child: const Text(
                      'InventoryPro',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const Text(
                    'Management System',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF64748B),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
          // Enhanced Toggle Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _isSidebarCollapsed = !_isSidebarCollapsed;
                });
              },
              icon: AnimatedRotation(
                turns: _isSidebarCollapsed ? 0.5 : 0,
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _isSidebarCollapsed ? Icons.menu : Icons.menu_open,
                  color: const Color(0xFF667EEA),
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced Navigation Menu with Animations
  Widget _buildEnhancedNavigationMenu() {
    final menuItems = [
      const _EnhancedNavItem(
        icon: Icons.dashboard_outlined,
        activeIcon: Icons.dashboard,
        label: 'Dashboard',
        index: 0,
        gradient: EnhancedInventoryColors.dashboardGradient,
      ),
      const _EnhancedNavItem(
        icon: Icons.inventory_2_outlined,
        activeIcon: Icons.inventory_2,
        label: 'Inventory',
        index: 1,
        gradient: EnhancedInventoryColors.inventoryGradient,
      ),
      const _EnhancedNavItem(
        icon: Icons.analytics_outlined,
        activeIcon: Icons.analytics,
        label: 'Analytics',
        index: 2,
        gradient: EnhancedInventoryColors.analyticsGradient,
      ),
      const _EnhancedNavItem(
        icon: Icons.settings_outlined,
        activeIcon: Icons.settings,
        label: 'Settings',
        index: 3,
        gradient: EnhancedInventoryColors.settingsGradient,
      ),
    ];

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 15),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];
        final isSelected = _selectedNavIndex == item.index;

        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 50)),
          curve: Curves.easeOutCubic,
          child: _buildEnhancedNavItem(item, isSelected, index),
        );
      },
    );
  }

  /// Enhanced Navigation Item with Glassmorphism and Animations
  Widget _buildEnhancedNavItem(
      _EnhancedNavItem item, bool isSelected, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.translate(
          offset: Offset(-50 * (1 - animationValue), 0),
          child: Opacity(
            opacity: animationValue,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 4),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedNavIndex = item.index;
                    });
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOutCubic,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 18, vertical: 14),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? LinearGradient(
                              colors: [
                                item.gradient.colors.first.withOpacity(0.15),
                                item.gradient.colors.last.withOpacity(0.05),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                      borderRadius: BorderRadius.circular(16),
                      border: isSelected
                          ? Border.all(
                              color:
                                  item.gradient.colors.first.withOpacity(0.3),
                              width: 1.5,
                            )
                          : null,
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: item.gradient.colors.first
                                    .withOpacity(0.25),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ]
                          : null,
                    ),
                    child: Row(
                      children: [
                        // Professional Icon with Enhanced Gradient Design
                        Container(
                          width: 42,
                          height: 42,
                          decoration: BoxDecoration(
                            gradient: isSelected ? item.gradient : null,
                            borderRadius: BorderRadius.circular(14),
                            boxShadow: isSelected
                                ? [
                                    BoxShadow(
                                      color: item.gradient.colors.first
                                          .withOpacity(0.4),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                : null,
                          ),
                          child: Icon(
                            isSelected ? item.activeIcon : item.icon,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF64748B),
                            size: 22,
                          ),
                        ),
                        if (!_isSidebarCollapsed) ...[
                          const SizedBox(width: 16),
                          Expanded(
                            child: isSelected
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        item.gradient.createShader(bounds),
                                    child: Text(
                                      item.label,
                                      style: const TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : Text(
                                    item.label,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF475569),
                                    ),
                                  ),
                          ),
                          // Selection Indicator
                          if (isSelected)
                            Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                gradient: item.gradient,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Enhanced User Profile Section with Glassmorphism
  Widget _buildEnhancedUserProfileSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 10, 15, 5),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.3),
            Colors.white.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // Enhanced Avatar with Gradient
          Container(
            decoration: BoxDecoration(
              gradient: EnhancedInventoryColors.primaryGradient,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667EEA).withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const CircleAvatar(
              radius: 22,
              backgroundColor: Colors.transparent,
              child: Text(
                'M',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          if (!_isSidebarCollapsed) ...[
            const SizedBox(width: 14),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShaderMask(
                    shaderCallback: (bounds) => EnhancedInventoryColors
                        .primaryGradient
                        .createShader(bounds),
                    child: const Text(
                      'Merchant User',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const Text(
                    '<EMAIL>',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF64748B),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            // Enhanced Menu Button
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.more_vert,
                color: Color(0xFF667EEA),
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Enhanced Top Navigation Bar with Dynamic Content
  Widget _buildTopNavigationBar() {
    final screenTitles = [
      'Dashboard',
      'Inventory',
      'Analytics',
      'Settings',
    ];

    final screenSubtitles = [
      'Business Overview',
      'Product Management',
      'Reports & Analytics',
      'System Configuration',
    ];

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Dynamic Breadcrumb Navigation
          Row(
            children: [
              ShaderMask(
                shaderCallback: (bounds) => EnhancedInventoryColors
                    .primaryGradient
                    .createShader(bounds),
                child: const Text(
                  'Merchant Portal',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: Colors.grey.shade400,
                size: 20,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    screenTitles[_selectedNavIndex],
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1A202C),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    screenSubtitles[_selectedNavIndex],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const Spacer(),

          // Dynamic Action Buttons
          _buildDynamicActionButtons(),
        ],
      ),
    );
  }

  /// Build action buttons based on selected screen
  Widget _buildDynamicActionButtons() {
    switch (_selectedNavIndex) {
      case 0: // Dashboard
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.refresh,
              tooltip: 'Refresh Data',
              onPressed: () {
                // Refresh dashboard data
                ref.invalidate(demoCurrentUserProductsProvider);
              },
            ),
            const SizedBox(width: 12),
            _buildPrimaryActionButton(
              icon: Icons.analytics,
              label: 'View Reports',
              onPressed: () {
                setState(() {
                  _selectedNavIndex = 2; // Switch to Analytics
                });
              },
            ),
          ],
        );
      case 1: // Inventory
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.download,
              tooltip: 'Export Data',
              onPressed: () {},
            ),
            const SizedBox(width: 12),
            _buildPrimaryActionButton(
              icon: Icons.add,
              label: 'Add Product',
              onPressed: _showAddProductDialog,
            ),
          ],
        );
      case 2: // Analytics
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.date_range,
              tooltip: 'Date Range',
              onPressed: () {},
            ),
            const SizedBox(width: 12),
            _buildPrimaryActionButton(
              icon: Icons.download,
              label: 'Export Report',
              onPressed: () {
                // Export analytics report
              },
            ),
          ],
        );
      case 3: // Settings
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.backup,
              tooltip: 'Backup Data',
              onPressed: () {},
            ),
            const SizedBox(width: 12),
            _buildPrimaryActionButton(
              icon: Icons.save,
              label: 'Save Settings',
              onPressed: () {
                // Save settings
              },
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /// Build a secondary action button with enhanced animations and styling
  Widget _buildActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    bool isLoading = false,
  }) {
    return AnimatedBuilder(
      animation: _pageAnimation,
      builder: (context, child) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 200),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Tooltip(
                message: tooltip,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: isLoading ? null : onPressed,
                    borderRadius: BorderRadius.circular(16),
                    splashColor: const Color(0xFF667EEA).withOpacity(0.2),
                    highlightColor: const Color(0xFF667EEA).withOpacity(0.1),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeOutCubic,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0.9),
                            Colors.white.withOpacity(0.7),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.4),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF667EEA).withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(0.8),
                            blurRadius: 4,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      child: isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  const Color(0xFF667EEA),
                                ),
                              ),
                            )
                          : Icon(
                              icon,
                              color: const Color(0xFF667EEA),
                              size: 20,
                            ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Build a primary action button with enhanced animations and styling
  Widget _buildPrimaryActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isLoading = false,
  }) {
    return AnimatedBuilder(
      animation: _pageAnimation,
      builder: (context, child) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 250),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Tooltip(
                message: label,
                child: Semantics(
                  label: label,
                  button: true,
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                    child: InkWell(
                      onTap: isLoading ? null : onPressed,
                      borderRadius: BorderRadius.circular(16),
                      splashColor: Colors.white.withOpacity(0.2),
                      highlightColor: Colors.white.withOpacity(0.1),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 250),
                        curve: Curves.easeOutCubic,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 14),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.25),
                              Colors.white.withOpacity(0.15),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                            BoxShadow(
                              color: Colors.white.withOpacity(0.2),
                              blurRadius: 6,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isLoading)
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            else
                              Icon(
                                icon,
                                color: Colors.white,
                                size: 20,
                              ),
                            const SizedBox(width: 8),
                            Text(
                              label,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Enhanced Main Content Area with Screen Navigation
  Widget _buildMainContent(AsyncValue<List<ProductModel>> productsAsync) {
    return FadeTransition(
      opacity: _pageAnimation,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 400),
        switchInCurve: Curves.easeOutCubic,
        switchOutCurve: Curves.easeInCubic,
        transitionBuilder: (Widget child, Animation<double> animation) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.05, 0),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: FadeTransition(
              opacity: animation,
              child: child,
            ),
          );
        },
        child: Container(
          key: ValueKey(_selectedNavIndex), // Important for AnimatedSwitcher
          child: _buildSelectedScreen(),
        ),
      ),
    );
  }

  /// Build the selected screen based on navigation index
  Widget _buildSelectedScreen() {
    switch (_selectedNavIndex) {
      case 0: // Dashboard
        return const DashboardScreen();
      case 1: // Inventory (current screen content)
        return _buildInventoryContent();
      case 2: // Analytics
        return const AnalyticsScreen();
      case 3: // Settings
        return const SettingsScreen();
      default:
        return _buildInventoryContent();
    }
  }

  /// Build the original inventory content
  Widget _buildInventoryContent() {
    final productsAsync = ref.watch(demoCurrentUserProductsProvider);

    // Responsive web layout: Center and constrain main content
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 1400),
        child: CustomScrollView(
          key: const ValueKey('inventory'),
          slivers: [
            // Stats Cards Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(32, 32, 32, 0),
                child: _buildEnhancedStatsCards(productsAsync),
              ),
            ),

            // Search and Filters Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(32, 32, 32, 0),
                child: _buildEnhancedSearchAndFilters(),
              ),
            ),

            // Product Table Section
            SliverFillRemaining(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(32, 24, 32, 32),
                child: _buildEnhancedProductTable(productsAsync),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced Stats Cards with Glassmorphism
  Widget _buildEnhancedStatsCards(
      AsyncValue<List<ProductModel>> productsAsync) {
    return productsAsync.when(
      data: (products) {
        final totalProducts = products.length;
        final lowStockProducts = products
            .where((p) => p.stockQuantity <= 10 && p.stockQuantity > 0)
            .length;
        final outOfStockProducts =
            products.where((p) => p.stockQuantity <= 0).length;
        final totalValue = products.fold<double>(
            0, (sum, p) => sum + (p.pricePerCarton * p.stockQuantity));

        final stats = [
          _StatCard(
            title: 'Total Products',
            value: totalProducts.toString(),
            subtitle: '+12% from last month',
            icon: Icons.inventory_2,
            color: const Color(0xFF667EEA),
            isPositive: true,
          ),
          _StatCard(
            title: 'Low Stock',
            value: lowStockProducts.toString(),
            subtitle: 'Items need restocking',
            icon: Icons.warning,
            color: const Color(0xFFED8936),
            isPositive: false,
          ),
          _StatCard(
            title: 'Out of Stock',
            value: outOfStockProducts.toString(),
            subtitle: 'Items unavailable',
            icon: Icons.error,
            color: const Color(0xFFE53E3E),
            isPositive: false,
          ),
          _StatCard(
            title: 'Total Value',
            value: '₦${totalValue.toStringAsFixed(0)}',
            subtitle: '+8.2% from last month',
            icon: Icons.attach_money,
            color: const Color(0xFF38A169),
            isPositive: true,
          ),
        ];

        return Row(
          children: stats
              .map((stat) => Expanded(
                    child: Container(
                      margin: const EdgeInsets.only(right: 24),
                      child: _buildEnhancedStatCard(stat),
                    ),
                  ))
              .toList(),
        );
      },
      loading: () => Row(
        children: [
          Expanded(child: _buildSkeletonCard()),
          const SizedBox(width: 24),
          Expanded(child: _buildSkeletonCard()),
          const SizedBox(width: 24),
          Expanded(child: _buildSkeletonCard()),
          const SizedBox(width: 24),
        ],
      ),
      error: (error, stack) => Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.shade200),
        ),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.red.shade600),
            const SizedBox(width: 12),
            Text(
              'Error loading stats: $error',
              style: TextStyle(color: Colors.red.shade600),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced Stat Card with Glassmorphism and Gradients
  Widget _buildEnhancedStatCard(_StatCard stat) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, animationValue, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * animationValue),
          child: Container(
            padding: const EdgeInsets.all(26),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.9),
                  Colors.white.withOpacity(0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: stat.color.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Enhanced Icon with Gradient Background
                    Container(
                      padding: const EdgeInsets.all(14),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            stat.color,
                            stat.color.withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: stat.color.withOpacity(0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        stat.icon,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const Spacer(),
                    // Enhanced Trend Indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: stat.isPositive
                              ? [
                                  const Color(0xFF38A169),
                                  const Color(0xFF2F855A)
                                ]
                              : [
                                  const Color(0xFFE53E3E),
                                  const Color(0xFFC53030)
                                ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            stat.isPositive
                                ? Icons.trending_up
                                : Icons.trending_down,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            stat.isPositive ? '+12%' : '-5%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Enhanced Value with Gradient Text
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: [
                      stat.color,
                      stat.color.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
                  child: Text(
                    stat.value,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  stat.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 10),
                // Enhanced Progress Bar
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.grey.shade200,
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: 0.7,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            stat.color,
                            stat.color.withOpacity(0.7),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  stat.subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: stat.isPositive
                        ? const Color(0xFF38A169)
                        : const Color(0xFF64748B),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Enhanced Search and Filters
  Widget _buildEnhancedSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.8),
                  Colors.white.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
              ),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search products by name or barcode...',
                prefixIcon: Container(
                  padding: const EdgeInsets.all(12),
                  child: ShaderMask(
                    shaderCallback: (bounds) => EnhancedInventoryColors
                        .primaryGradient
                        .createShader(bounds),
                    child: const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                // Add clear button if search is not empty
                suffixIcon: _searchQuery.isNotEmpty
                    ? Tooltip(
                        message: 'Clear Search',
                        child: Semantics(
                          label: 'Clear Search',
                          button: true,
                          child: Focus(
                            autofocus: false,
                            child: IconButton(
                              icon: const Icon(Icons.clear, color: Colors.grey),
                              onPressed: () {
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            ),
                          ),
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Filter Row
          Row(
            children: [
              // Category Filter
              Expanded(
                child: _buildEnhancedFilterDropdown(
                  'Category',
                  _selectedCategory,
                  [
                    'All',
                    'Electronics',
                    'Clothing',
                    'Food',
                    'Books',
                    'Home',
                    'Sports'
                  ],
                  (value) => setState(() => _selectedCategory = value ?? 'All'),
                  Icons.category,
                ),
              ),
              const SizedBox(width: 20),

              // Stock Status Filter
              Expanded(
                child: _buildEnhancedFilterDropdown(
                  'Stock Status',
                  _selectedStockStatus,
                  ['All', 'In Stock', 'Low Stock', 'Out of Stock'],
                  (value) =>
                      setState(() => _selectedStockStatus = value ?? 'All'),
                  Icons.inventory,
                ),
              ),
              const SizedBox(width: 20),

              // Export Button
              Container(
                decoration: BoxDecoration(
                  gradient: EnhancedInventoryColors.infoGradient,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00BCD4).withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Tooltip(
                  message: 'Export',
                  child: Semantics(
                    label: 'Export',
                    button: true,
                    child: Focus(
                      autofocus: false,
                      child: ElevatedButton.icon(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 16),
                        ),
                        icon: const Icon(
                          Icons.download,
                          color: Colors.white,
                          size: 20,
                        ),
                        label: const Text(
                          'Export',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Enhanced Filter Dropdown
  Widget _buildEnhancedFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.8),
            Colors.white.withOpacity(0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Container(
            padding: const EdgeInsets.all(12),
            child: ShaderMask(
              shaderCallback: (bounds) =>
                  EnhancedInventoryColors.primaryGradient.createShader(bounds),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        items: items
            .map((item) => DropdownMenuItem(
                  value: item,
                  child: Text(item),
                ))
            .toList(),
        onChanged: onChanged,
      ),
    );
  }

  /// Enhanced Product Table
  Widget _buildEnhancedProductTable(
      AsyncValue<List<ProductModel>> productsAsync) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: productsAsync.when(
          data: (products) {
            var filteredProducts = _filterProducts(products);

            // Always show at least one demo product for UI/UX review
            if (filteredProducts.isEmpty) {
              filteredProducts = [
                ProductModel(
                  id: 'demo-1',
                  name: 'Demo Product',
                  barcode: '0000000001',
                  category: 'Electronics',
                  stockQuantity: 25,
                  pricePerCarton: 15000,
                  description: 'A sample product for UI/UX review.',
                  merchantId: 'demo-merchant',
                  mallId: 'demo-mall',
                  unitType: 'carton',
                  unitsPerCarton: 12,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                  createdBy: 'demo-user',
                ),
              ];
            }

            return Column(
              children: [
                // Table Header
                _buildTableHeader(),

                // Product List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(0),
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = filteredProducts[index];
                      return _buildProductRow(product, index);
                    },
                  ),
                ),
              ],
            );
          },
          loading: () => ListView.builder(
            itemCount: 8,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 24),
              height: 56,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error loading products: $error',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Filter products based on search and filter criteria
  List<ProductModel> _filterProducts(List<ProductModel> products) {
    return products.where((product) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!product.name.toLowerCase().contains(query) &&
            !product.barcode.toLowerCase().contains(query)) {
          return false;
        }
      }

      // Category filter
      if (_selectedCategory != 'All' && product.category != _selectedCategory) {
        return false;
      }

      // Stock status filter
      if (_selectedStockStatus != 'All') {
        if (_selectedStockStatus == 'In Stock' && product.stockQuantity <= 0) {
          return false;
        }
        if (_selectedStockStatus == 'Low Stock' &&
            (product.stockQuantity > 10 || product.stockQuantity <= 0)) {
          return false;
        }
        if (_selectedStockStatus == 'Out of Stock' &&
            product.stockQuantity > 0) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// Table Header
  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.8),
            Colors.white.withOpacity(0.6),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: _isAllSelected,
            onChanged: (value) {
              setState(() {
                _isAllSelected = value ?? false;
                if (_isAllSelected) {
                  _selectedProducts.addAll(
                    _filterProducts(
                            ref.read(demoCurrentUserProductsProvider).value ??
                                [])
                        .map((p) => p.id),
                  );
                } else {
                  _selectedProducts.clear();
                }
              });
            },
          ),
          const SizedBox(width: 16),

          // Headers
          const Expanded(
            flex: 3,
            child: Text(
              'Product',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A202C),
              ),
            ),
          ),
          const Expanded(
            flex: 2,
            child: Text(
              'Category',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A202C),
              ),
            ),
          ),
          const Expanded(
            flex: 1,
            child: Text(
              'Stock',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A202C),
              ),
            ),
          ),
          const Expanded(
            flex: 1,
            child: Text(
              'Price',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A202C),
              ),
            ),
          ),
          const SizedBox(width: 100), // Actions column
        ],
      ),
    );
  }

  /// Product Row
  Widget _buildProductRow(ProductModel product, int index) {
    final isSelected = _selectedProducts.contains(product.id);

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredIndex = index),
      onExit: (_) => setState(() => _hoveredIndex = -1),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF667EEA).withOpacity(0.05)
              : (_hoveredIndex == index
                  ? Colors.grey.withOpacity(0.08)
                  : (index % 2 == 0
                      ? Colors.white.withOpacity(0.5)
                      : Colors.transparent)),
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade100,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Checkbox
            Checkbox(
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value ?? false) {
                    _selectedProducts.add(product.id);
                  } else {
                    _selectedProducts.remove(product.id);
                  }
                });
              },
            ),
            const SizedBox(width: 16),

            // Product Info
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  // Product Image
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      gradient: EnhancedInventoryColors.primaryGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.inventory_2,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Product Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1A202C),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Barcode: ${product.barcode}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Category
            Expanded(
              flex: 2,
              child: _buildCategoryChip(product.category),
            ),

            // Stock
            Expanded(
              flex: 1,
              child: _buildStockBadge(product.stockQuantity),
            ),

            // Price
            Expanded(
              flex: 1,
              child: Text(
                '₦${product.pricePerCarton.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF38A169),
                ),
              ),
            ),

            // Actions
            SizedBox(
              width: 100,
              child: Row(
                children: [
                  Semantics(
                    label: 'Edit Product',
                    button: true,
                    child: Tooltip(
                      message: 'Edit Product',
                      child: Focus(
                        autofocus: false,
                        child: IconButton(
                          onPressed: () => _showEditProductDialog(product),
                          icon: const Icon(Icons.edit, size: 18),
                        ),
                      ),
                    ),
                  ),
                  Semantics(
                    label: 'Delete Product',
                    button: true,
                    child: Tooltip(
                      message: 'Delete Product',
                      child: Focus(
                        autofocus: false,
                        child: IconButton(
                          onPressed: () => _showDeleteProductDialog(product),
                          icon: const Icon(Icons.delete,
                              size: 18, color: Colors.red),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced Category Chip
  Widget _buildCategoryChip(String category) {
    final gradient = EnhancedInventoryColors.categoryGradients[category] ??
        EnhancedInventoryColors.primaryGradient;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            gradient.colors.first.withOpacity(0.15),
            gradient.colors.last.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: gradient.colors.first.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        category,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: gradient.colors.first,
        ),
      ),
    );
  }

  /// Enhanced Stock Badge
  Widget _buildStockBadge(int stockQuantity) {
    Color color;
    LinearGradient gradient;

    if (stockQuantity <= 0) {
      color = const Color(0xFFE53E3E);
      gradient = EnhancedInventoryColors.errorGradient;
    } else if (stockQuantity <= 10) {
      color = const Color(0xFFED8936);
      gradient = EnhancedInventoryColors.warningGradient;
    } else {
      color = const Color(0xFF38A169);
      gradient = EnhancedInventoryColors.successGradient;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.15),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              gradient: gradient,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            '$stockQuantity',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced Empty State
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  EnhancedInventoryColors.primaryGradient.colors.first
                      .withOpacity(0.1),
                  EnhancedInventoryColors.primaryGradient.colors.last
                      .withOpacity(0.05),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: EnhancedInventoryColors.primaryGradient.colors.first,
            ),
          ),
          const SizedBox(height: 24),
          ShaderMask(
            shaderCallback: (bounds) =>
                EnhancedInventoryColors.primaryGradient.createShader(bounds),
            child: const Text(
              'No products found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Try adjusting your search or filter criteria',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 32),
          Container(
            decoration: BoxDecoration(
              gradient: EnhancedInventoryColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667EEA).withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: _showAddProductDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
              icon: const Icon(
                Icons.add,
                color: Colors.white,
              ),
              label: const Text(
                'Add Your First Product',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show Add Product Dialog
  void _showAddProductDialog() {
    showDialog(
      context: context,
      builder: (context) => const EnhancedAddProductDialog(),
    );
  }

  /// Show Edit Product Dialog
  void _showEditProductDialog(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => EnhancedAddProductDialog(product: product),
    );
  }

  /// Show Delete Product Dialog
  void _showDeleteProductDialog(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${product.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement delete functionality
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${product.name} deleted successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Build Skeleton Card for Loading State
  Widget _buildSkeletonCard() {
    return Container(
      padding: const EdgeInsets.all(26),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const Spacer(),
              Container(
                width: 60,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            width: 80,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: 6),
          Container(
            width: 120,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(height: 10),
          Container(
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 100,
            height: 13,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced Logout Section with Glassmorphism
  Widget _buildLogoutSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 5, 15, 15),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showLogoutConfirmation,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(18),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFFE53E3E).withOpacity(0.1),
                  const Color(0xFFD32F2F).withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE53E3E).withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFE53E3E).withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                // Logout Icon with Gradient
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFE53E3E), Color(0xFFD32F2F)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFE53E3E).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.logout,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                if (!_isSidebarCollapsed) ...[
                  const SizedBox(width: 14),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Logout',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFFE53E3E),
                          ),
                        ),
                        Text(
                          'Sign out of account',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Arrow Icon
                  Icon(
                    Icons.arrow_forward_ios,
                    color: const Color(0xFFE53E3E).withOpacity(0.7),
                    size: 14,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Show Logout Confirmation Dialog
  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.95),
                    Colors.white.withOpacity(0.85),
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Logout Icon
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFE53E3E), Color(0xFFD32F2F)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFE53E3E).withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.logout,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Title
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [Color(0xFFE53E3E), Color(0xFFD32F2F)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                    child: const Text(
                      'Confirm Logout',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Message
                  Text(
                    'Are you sure you want to logout? You will be redirected to the login screen.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // Action Buttons
                  Row(
                    children: [
                      // Cancel Button
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.grey.shade300,
                                Colors.grey.shade400,
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ElevatedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Logout Button
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFFE53E3E), Color(0xFFD32F2F)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFFE53E3E).withOpacity(0.3),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: () async {
                              Navigator.of(context).pop();
                              // Add a small delay to ensure dialog is closed
                              await Future.delayed(
                                  const Duration(milliseconds: 100));
                              if (mounted) {
                                _performLogout();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text(
                              'Logout',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Perform Logout with Loading State
  void _performLogout() async {
    // Check if widget is still mounted before proceeding
    if (!mounted) {
      debugPrint('⚠️ Merchant dashboard widget unmounted, skipping logout');
      return;
    }

    try {
      await LogoutService.performLogout(
        context: context,
        ref: ref,
        customMessage: 'Signing out from Merchant Dashboard...',
      );
    } catch (e) {
      debugPrint('❌ Error during merchant logout: $e');
      // If context is still available, try to navigate to login as fallback
      if (mounted && context.mounted) {
        context.go('/login');
      }
    }
  }

  /// Add a placeholder method for bulk delete
  void _bulkDeleteSelectedProducts() {
    // TODO: Implement bulk delete logic
    setState(() {
      _selectedProducts.clear();
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Selected products deleted (demo only)')),
    );
  }
}

/// Enhanced Navigation Item Model
class _EnhancedNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final int index;
  final LinearGradient gradient;

  const _EnhancedNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.index,
    required this.gradient,
  });
}

/// Enhanced Stat Card Model
class _StatCard {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isPositive;

  const _StatCard({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.isPositive,
  });
}
