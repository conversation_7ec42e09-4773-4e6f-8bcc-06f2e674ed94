import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/product_model.dart';
import '../../../shared/widgets/enhanced_buttons.dart';

class BulkActionsDialog extends ConsumerStatefulWidget {
  final List<ProductModel> selectedProducts;
  final Function(BulkAction, Map<String, dynamic>)? onAction;

  const BulkActionsDialog({
    super.key,
    required this.selectedProducts,
    this.onAction,
  });

  @override
  ConsumerState<BulkActionsDialog> createState() => _BulkActionsDialogState();
}

class _BulkActionsDialogState extends ConsumerState<BulkActionsDialog> {
  BulkAction? _selectedAction;
  final _formKey = GlobalKey<FormState>();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  String? _selectedCategory;

  final List<String> _categories = [
    'Electronics',
    'Clothing',
    'Food',
    'Books',
    'Home',
    'Sports',
    'Other'
  ];

  @override
  void dispose() {
    _priceController.dispose();
    _stockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.edit_note,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Bulk Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Selected items info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.selectedProducts.length} products selected',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action selection
            Text(
              'Select Action',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: BulkAction.values.map((action) {
                return ChoiceChip(
                  label: Text(_getActionLabel(action)),
                  selected: _selectedAction == action,
                  onSelected: (selected) {
                    setState(() {
                      _selectedAction = selected ? action : null;
                    });
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Action form
            if (_selectedAction != null) _buildActionForm(),
            
            const SizedBox(height: 24),
            
            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                EnhancedButton(
                  onPressed: _selectedAction != null ? _executeAction : null,
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Action Details',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          switch (_selectedAction!) {
            BulkAction.updatePrice => _buildPriceForm(),
            BulkAction.updateStock => _buildStockForm(),
            BulkAction.updateCategory => _buildCategoryForm(),
            BulkAction.delete => _buildDeleteConfirmation(),
            BulkAction.export => _buildExportOptions(),
          },
        ],
      ),
    );
  }

  Widget _buildPriceForm() {
    return Column(
      children: [
        TextFormField(
          controller: _priceController,
          decoration: const InputDecoration(
            labelText: 'New Price',
            prefixText: '₦',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a price';
            }
            if (double.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildStockForm() {
    return Column(
      children: [
        TextFormField(
          controller: _stockController,
          decoration: const InputDecoration(
            labelText: 'Stock Quantity',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter stock quantity';
            }
            if (int.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCategoryForm() {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: 'Category',
            border: OutlineInputBorder(),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a category';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDeleteConfirmation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              const SizedBox(width: 8),
              Text(
                'Warning',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'This action will permanently delete ${widget.selectedProducts.length} products. This cannot be undone.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildExportOptions() {
    return Text(
      'Export ${widget.selectedProducts.length} products to CSV file.',
      style: Theme.of(context).textTheme.bodyMedium,
    );
  }

  String _getActionLabel(BulkAction action) {
    switch (action) {
      case BulkAction.updatePrice:
        return 'Update Price';
      case BulkAction.updateStock:
        return 'Update Stock';
      case BulkAction.updateCategory:
        return 'Update Category';
      case BulkAction.delete:
        return 'Delete';
      case BulkAction.export:
        return 'Export';
    }
  }

  void _executeAction() {
    if (_formKey.currentState?.validate() ?? false) {
      final data = <String, dynamic>{};
      
      switch (_selectedAction!) {
        case BulkAction.updatePrice:
          data['price'] = double.parse(_priceController.text);
          break;
        case BulkAction.updateStock:
          data['stock'] = int.parse(_stockController.text);
          break;
        case BulkAction.updateCategory:
          data['category'] = _selectedCategory;
          break;
        case BulkAction.delete:
        case BulkAction.export:
          break;
      }
      
      widget.onAction?.call(_selectedAction!, data);
      Navigator.of(context).pop();
    }
  }
}

enum BulkAction {
  updatePrice,
  updateStock,
  updateCategory,
  delete,
  export,
}
