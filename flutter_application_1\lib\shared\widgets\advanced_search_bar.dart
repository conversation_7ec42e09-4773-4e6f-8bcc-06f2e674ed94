import 'package:flutter/material.dart';
import 'dart:async';

class AdvancedSearchBar extends StatefulWidget {
  final String? hintText;
  final Function(String) onSearchChanged;
  final Function(Map<String, dynamic>)? onFiltersChanged;
  final List<FilterOption>? filterOptions;
  final List<String>? suggestions;
  final Duration debounceTime;
  final bool showFilters;
  final Map<String, dynamic>? initialFilters;

  const AdvancedSearchBar({
    super.key,
    this.hintText,
    required this.onSearchChanged,
    this.onFiltersChanged,
    this.filterOptions,
    this.suggestions,
    this.debounceTime = const Duration(milliseconds: 300),
    this.showFilters = true,
    this.initialFilters,
  });

  @override
  State<AdvancedSearchBar> createState() => _AdvancedSearchBarState();
}

class _AdvancedSearchBarState extends State<AdvancedSearchBar>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _filterController;
  late Animation<double> _filterAnimation;
  
  Timer? _debounceTimer;
  bool _showFilterPanel = false;
  Map<String, dynamic> _currentFilters = {};
  List<String> _filteredSuggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _currentFilters = Map.from(widget.initialFilters ?? {});
    
    _filterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _filterAnimation = CurvedAnimation(
      parent: _filterController,
      curve: Curves.easeInOut,
    );

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    
    // Update suggestions
    if (widget.suggestions != null) {
      setState(() {
        _filteredSuggestions = widget.suggestions!
            .where((suggestion) => 
                suggestion.toLowerCase().contains(query.toLowerCase()))
            .take(5)
            .toList();
        _showSuggestions = query.isNotEmpty && _filteredSuggestions.isNotEmpty;
      });
    }

    // Debounce search
    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceTime, () {
      widget.onSearchChanged(query);
    });
  }

  void _toggleFilterPanel() {
    setState(() {
      _showFilterPanel = !_showFilterPanel;
    });
    
    if (_showFilterPanel) {
      _filterController.forward();
    } else {
      _filterController.reverse();
    }
  }

  void _updateFilter(String key, dynamic value) {
    setState(() {
      if (value == null || (value is String && value.isEmpty)) {
        _currentFilters.remove(key);
      } else {
        _currentFilters[key] = value;
      }
    });
    widget.onFiltersChanged?.call(_currentFilters);
  }

  void _clearAllFilters() {
    setState(() {
      _currentFilters.clear();
      _searchController.clear();
    });
    widget.onFiltersChanged?.call(_currentFilters);
    widget.onSearchChanged('');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Main search bar
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Search input
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: widget.hintText ?? 'Search...',
                    prefixIcon: const Icon(Icons.search),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                ),
              ),
              
              // Filter button
              if (widget.showFilters && widget.filterOptions != null)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    onPressed: _toggleFilterPanel,
                    icon: Stack(
                      children: [
                        Icon(
                          Icons.tune,
                          color: _showFilterPanel
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        if (_currentFilters.isNotEmpty)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              
              // Clear button
              if (_searchController.text.isNotEmpty || _currentFilters.isNotEmpty)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    onPressed: _clearAllFilters,
                    icon: const Icon(Icons.clear),
                  ),
                ),
            ],
          ),
        ),
        
        // Suggestions dropdown
        if (_showSuggestions) _buildSuggestions(),
        
        // Filter panel
        if (widget.showFilters && widget.filterOptions != null)
          _buildFilterPanel(),
        
        // Active filters chips
        if (_currentFilters.isNotEmpty) _buildActiveFilters(),
      ],
    );
  }

  Widget _buildSuggestions() {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _filteredSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _filteredSuggestions[index];
          return ListTile(
            dense: true,
            leading: const Icon(Icons.history, size: 16),
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              setState(() => _showSuggestions = false);
              widget.onSearchChanged(suggestion);
            },
          );
        },
      ),
    );
  }

  Widget _buildFilterPanel() {
    return SizeTransition(
      sizeFactor: _filterAnimation,
      child: Container(
        margin: const EdgeInsets.only(top: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: widget.filterOptions!.map((option) {
                return _buildFilterOption(option);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(FilterOption option) {
    switch (option.type) {
      case FilterType.dropdown:
        return SizedBox(
          width: 200,
          child: DropdownButtonFormField<String>(
            value: _currentFilters[option.key],
            decoration: InputDecoration(
              labelText: option.label,
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: option.options!.map((value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (value) => _updateFilter(option.key, value),
          ),
        );
      
      case FilterType.dateRange:
        return SizedBox(
          width: 200,
          child: TextFormField(
            decoration: InputDecoration(
              labelText: option.label,
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            readOnly: true,
            onTap: () => _showDateRangePicker(option.key),
          ),
        );
      
      case FilterType.numberRange:
        return SizedBox(
          width: 200,
          child: TextFormField(
            decoration: InputDecoration(
              labelText: option.label,
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => _updateFilter(option.key, value),
          ),
        );
    }
  }

  Widget _buildActiveFilters() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: _currentFilters.entries.map((entry) {
          return Chip(
            label: Text('${entry.key}: ${entry.value}'),
            deleteIcon: const Icon(Icons.close, size: 16),
            onDeleted: () => _updateFilter(entry.key, null),
          );
        }).toList(),
      ),
    );
  }

  void _showDateRangePicker(String key) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      _updateFilter(key, '${picked.start.toLocal().toString().split(' ')[0]} - ${picked.end.toLocal().toString().split(' ')[0]}');
    }
  }
}

class FilterOption {
  final String key;
  final String label;
  final FilterType type;
  final List<String>? options;

  const FilterOption({
    required this.key,
    required this.label,
    required this.type,
    this.options,
  });
}

enum FilterType {
  dropdown,
  dateRange,
  numberRange,
}
