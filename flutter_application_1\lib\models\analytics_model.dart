class SalesMetrics {
  final double totalRevenue;
  final double totalProfit;
  final int totalOrders;
  final int totalItems;
  final double averageOrderValue;
  final double profitMargin;
  final DateTime periodStart;
  final DateTime periodEnd;

  SalesMetrics({
    required this.totalRevenue,
    required this.totalProfit,
    required this.totalOrders,
    required this.totalItems,
    required this.averageOrderValue,
    required this.profitMargin,
    required this.periodStart,
    required this.periodEnd,
  });

  factory SalesMetrics.empty(DateTime start, DateTime end) {
    return SalesMetrics(
      totalRevenue: 0.0,
      totalProfit: 0.0,
      totalOrders: 0,
      totalItems: 0,
      averageOrderValue: 0.0,
      profitMargin: 0.0,
      periodStart: start,
      periodEnd: end,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalRevenue': totalRevenue,
      'totalProfit': totalProfit,
      'totalOrders': totalOrders,
      'totalItems': totalItems,
      'averageOrderValue': averageOrderValue,
      'profitMargin': profitMargin,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
    };
  }

  factory SalesMetrics.fromJson(Map<String, dynamic> json) {
    return SalesMetrics(
      totalRevenue: (json['totalRevenue'] ?? 0.0).toDouble(),
      totalProfit: (json['totalProfit'] ?? 0.0).toDouble(),
      totalOrders: json['totalOrders'] ?? 0,
      totalItems: json['totalItems'] ?? 0,
      averageOrderValue: (json['averageOrderValue'] ?? 0.0).toDouble(),
      profitMargin: (json['profitMargin'] ?? 0.0).toDouble(),
      periodStart: DateTime.parse(json['periodStart']),
      periodEnd: DateTime.parse(json['periodEnd']),
    );
  }
}

class InventoryMetrics {
  final int totalProducts;
  final int lowStockProducts;
  final int outOfStockProducts;
  final double totalInventoryValue;
  final double averageStockLevel;
  final Map<String, int> categoryBreakdown;
  final List<TopSellingProduct> topSellingProducts;
  final List<SlowMovingProduct> slowMovingProducts;

  InventoryMetrics({
    required this.totalProducts,
    required this.lowStockProducts,
    required this.outOfStockProducts,
    required this.totalInventoryValue,
    required this.averageStockLevel,
    required this.categoryBreakdown,
    required this.topSellingProducts,
    required this.slowMovingProducts,
  });

  double get stockHealthScore {
    if (totalProducts == 0) return 0.0;
    final healthyProducts = totalProducts - lowStockProducts - outOfStockProducts;
    return (healthyProducts / totalProducts) * 100;
  }

  factory InventoryMetrics.empty() {
    return InventoryMetrics(
      totalProducts: 0,
      lowStockProducts: 0,
      outOfStockProducts: 0,
      totalInventoryValue: 0.0,
      averageStockLevel: 0.0,
      categoryBreakdown: {},
      topSellingProducts: [],
      slowMovingProducts: [],
    );
  }
}

class TopSellingProduct {
  final String productId;
  final String productName;
  final String category;
  final int quantitySold;
  final double revenue;
  final double profitMargin;

  TopSellingProduct({
    required this.productId,
    required this.productName,
    required this.category,
    required this.quantitySold,
    required this.revenue,
    required this.profitMargin,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'category': category,
      'quantitySold': quantitySold,
      'revenue': revenue,
      'profitMargin': profitMargin,
    };
  }

  factory TopSellingProduct.fromJson(Map<String, dynamic> json) {
    return TopSellingProduct(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      category: json['category'] ?? '',
      quantitySold: json['quantitySold'] ?? 0,
      revenue: (json['revenue'] ?? 0.0).toDouble(),
      profitMargin: (json['profitMargin'] ?? 0.0).toDouble(),
    );
  }
}

class SlowMovingProduct {
  final String productId;
  final String productName;
  final String category;
  final int currentStock;
  final int daysSinceLastSale;
  final double inventoryValue;

  SlowMovingProduct({
    required this.productId,
    required this.productName,
    required this.category,
    required this.currentStock,
    required this.daysSinceLastSale,
    required this.inventoryValue,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'category': category,
      'currentStock': currentStock,
      'daysSinceLastSale': daysSinceLastSale,
      'inventoryValue': inventoryValue,
    };
  }

  factory SlowMovingProduct.fromJson(Map<String, dynamic> json) {
    return SlowMovingProduct(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      category: json['category'] ?? '',
      currentStock: json['currentStock'] ?? 0,
      daysSinceLastSale: json['daysSinceLastSale'] ?? 0,
      inventoryValue: (json['inventoryValue'] ?? 0.0).toDouble(),
    );
  }
}

class CustomerMetrics {
  final int totalCustomers;
  final int newCustomers;
  final int returningCustomers;
  final double customerRetentionRate;
  final double averageCustomerValue;
  final List<TopCustomer> topCustomers;

  CustomerMetrics({
    required this.totalCustomers,
    required this.newCustomers,
    required this.returningCustomers,
    required this.customerRetentionRate,
    required this.averageCustomerValue,
    required this.topCustomers,
  });

  factory CustomerMetrics.empty() {
    return CustomerMetrics(
      totalCustomers: 0,
      newCustomers: 0,
      returningCustomers: 0,
      customerRetentionRate: 0.0,
      averageCustomerValue: 0.0,
      topCustomers: [],
    );
  }
}

class TopCustomer {
  final String customerId;
  final String customerName;
  final String customerEmail;
  final int totalOrders;
  final double totalSpent;
  final DateTime lastOrderDate;

  TopCustomer({
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.totalOrders,
    required this.totalSpent,
    required this.lastOrderDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'totalOrders': totalOrders,
      'totalSpent': totalSpent,
      'lastOrderDate': lastOrderDate.toIso8601String(),
    };
  }

  factory TopCustomer.fromJson(Map<String, dynamic> json) {
    return TopCustomer(
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerEmail: json['customerEmail'] ?? '',
      totalOrders: json['totalOrders'] ?? 0,
      totalSpent: (json['totalSpent'] ?? 0.0).toDouble(),
      lastOrderDate: DateTime.parse(json['lastOrderDate']),
    );
  }
}

class DashboardAnalytics {
  final SalesMetrics salesMetrics;
  final InventoryMetrics inventoryMetrics;
  final CustomerMetrics customerMetrics;
  final List<SalesDataPoint> salesTrend;
  final Map<String, double> categoryPerformance;
  final DateTime lastUpdated;

  DashboardAnalytics({
    required this.salesMetrics,
    required this.inventoryMetrics,
    required this.customerMetrics,
    required this.salesTrend,
    required this.categoryPerformance,
    required this.lastUpdated,
  });

  factory DashboardAnalytics.empty() {
    final now = DateTime.now();
    return DashboardAnalytics(
      salesMetrics: SalesMetrics.empty(now.subtract(const Duration(days: 30)), now),
      inventoryMetrics: InventoryMetrics.empty(),
      customerMetrics: CustomerMetrics.empty(),
      salesTrend: [],
      categoryPerformance: {},
      lastUpdated: now,
    );
  }
}

class SalesDataPoint {
  final DateTime date;
  final double revenue;
  final int orders;

  SalesDataPoint({
    required this.date,
    required this.revenue,
    required this.orders,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'revenue': revenue,
      'orders': orders,
    };
  }

  factory SalesDataPoint.fromJson(Map<String, dynamic> json) {
    return SalesDataPoint(
      date: DateTime.parse(json['date']),
      revenue: (json['revenue'] ?? 0.0).toDouble(),
      orders: json['orders'] ?? 0,
    );
  }
}

/// Simple Analytics Model for Merchant Dashboard
class AnalyticsModel {
  final double totalRevenue;
  final double revenueGrowth;
  final int totalOrders;
  final double orderGrowth;
  final int activeProducts;
  final double productGrowth;
  final int totalCustomers;
  final double customerGrowth;
  final double averageOrderValue;
  final double conversionRate;
  final double customerRetention;
  final double inventoryTurnover;

  AnalyticsModel({
    required this.totalRevenue,
    required this.revenueGrowth,
    required this.totalOrders,
    required this.orderGrowth,
    required this.activeProducts,
    required this.productGrowth,
    required this.totalCustomers,
    required this.customerGrowth,
    required this.averageOrderValue,
    required this.conversionRate,
    required this.customerRetention,
    required this.inventoryTurnover,
  });

  factory AnalyticsModel.empty() {
    return AnalyticsModel(
      totalRevenue: 0.0,
      revenueGrowth: 0.0,
      totalOrders: 0,
      orderGrowth: 0.0,
      activeProducts: 0,
      productGrowth: 0.0,
      totalCustomers: 0,
      customerGrowth: 0.0,
      averageOrderValue: 0.0,
      conversionRate: 0.0,
      customerRetention: 0.0,
      inventoryTurnover: 0.0,
    );
  }

  factory AnalyticsModel.demo() {
    return AnalyticsModel(
      totalRevenue: 2500000.0,
      revenueGrowth: 12.5,
      totalOrders: 1250,
      orderGrowth: 8.3,
      activeProducts: 450,
      productGrowth: 15.2,
      totalCustomers: 850,
      customerGrowth: 22.1,
      averageOrderValue: 2000.0,
      conversionRate: 3.8,
      customerRetention: 78.5,
      inventoryTurnover: 4.2,
    );
  }
}
