import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mall_management_system/models/user_model.dart';
import 'package:mall_management_system/core/enums/user_role.dart';

void main() {
  group('UserModel Tests', () {
    test('should create UserModel with required fields', () {
      final now = DateTime.now();
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'Doe',
        role: UserRole.customer,
        createdAt: now,
        updatedAt: now,
      );

      expect(user.id, 'test-id');
      expect(user.email, '<EMAIL>');
      expect(user.firstName, 'John');
      expect(user.lastName, 'Doe');
      expect(user.role, UserRole.customer);
      expect(user.fullName, '<PERSON>');
      expect(user.displayName, '<PERSON>');
      expect(user.isActive, true);
    });

    test('should create UserModel with all fields', () {
      final now = DateTime.now();
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: '<PERSON>',
        role: UserRole.merchant,
        phoneNumber: '+1234567890',
        profileImageUrl: 'https://example.com/image.jpg',
        mallId: 'mall-123',
        isActive: false,
        createdAt: now,
        updatedAt: now,
        securityQuestions: {
          'question1': 'answer1',
          'question2': 'answer2',
        },
      );

      expect(user.phoneNumber, '+1234567890');
      expect(user.profileImageUrl, 'https://example.com/image.jpg');
      expect(user.mallId, 'mall-123');
      expect(user.isActive, false);
      expect(user.securityQuestions, isNotNull);
      expect(user.securityQuestions!['question1'], 'answer1');
    });

    test('should convert to Firestore map correctly', () {
      final now = DateTime.now();
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.salesRep,
        phoneNumber: '+1234567890',
        createdAt: now,
        updatedAt: now,
      );

      final firestoreData = user.toFirestore();

      expect(firestoreData['email'], '<EMAIL>');
      expect(firestoreData['firstName'], 'John');
      expect(firestoreData['lastName'], 'Doe');
      expect(firestoreData['role'], 'sales_rep');
      expect(firestoreData['phoneNumber'], '+1234567890');
      expect(firestoreData['isActive'], true);
      expect(firestoreData['createdAt'], isA<Timestamp>());
      expect(firestoreData['updatedAt'], isA<Timestamp>());
    });

    test('should copy with new values', () {
      final now = DateTime.now();
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.customer,
        createdAt: now,
        updatedAt: now,
      );

      final updatedUser = user.copyWith(
        firstName: 'Jane',
        role: UserRole.merchant,
        phoneNumber: '+1234567890',
      );

      expect(updatedUser.id, 'test-id'); // Should remain the same
      expect(updatedUser.email, '<EMAIL>'); // Should remain the same
      expect(updatedUser.firstName, 'Jane'); // Should be updated
      expect(updatedUser.lastName, 'Doe'); // Should remain the same
      expect(updatedUser.role, UserRole.merchant); // Should be updated
      expect(updatedUser.phoneNumber, '+1234567890'); // Should be updated
      expect(updatedUser.createdAt, now); // Should remain the same
      expect(updatedUser.updatedAt, isNot(now)); // Should be updated to current time
    });
  });
}
