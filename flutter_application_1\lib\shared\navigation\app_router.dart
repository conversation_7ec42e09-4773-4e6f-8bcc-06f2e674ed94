import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/splash/splash_screen.dart';
import '../../features/customer/screens/customer_dashboard.dart';
import '../../features/customer/screens/scanner_screen.dart';
import '../../features/customer/screens/cart_screen.dart';
import '../../features/customer/screens/checkout_screen.dart';
import '../../features/customer/screens/payment_success_screen.dart';
import '../../features/customer/screens/history_screen.dart';
import '../../features/customer/screens/profile_screen.dart';
import '../../features/merchant/screens/merchant_dashboard.dart';
import '../../features/merchant/screens/modern_inventory_dashboard.dart';
import '../../features/sales_rep/screens/sales_rep_dashboard.dart';
import '../../features/admin/screens/admin_dashboard.dart';
import '../../features/admin/screens/add_merchant_screen.dart';
import '../../features/boss/screens/boss_dashboard.dart';
import '../../test_navigation.dart';
import '../../features/test/role_interface_test.dart';
import '../../core/enums/user_role.dart';
import '../../providers/demo_auth_provider.dart';
import 'route_guard.dart';

// Router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation:
        '/demo-customer', // Auto-login demo customer to showcase UI/UX enhancements
    routes: [
      // Demo Customer Auto-Login (for showcasing UI/UX enhancements)
      GoRoute(
        path: '/demo-customer',
        name: 'demo-customer',
        builder: (context, state) => Consumer(
          builder: (context, ref, child) {
            // Auto-login demo customer
            WidgetsBinding.instance.addPostFrameCallback((_) {
              ref
                  .read(demoUserProvider.notifier)
                  .loginDemoUser('<EMAIL>');
              context.go('/customer');
            });
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading Customer Dashboard...'),
                  ],
                ),
              ),
            );
          },
        ),
      ),

      // Test Navigation Page
      GoRoute(
        path: '/test',
        name: 'test',
        builder: (context, state) => const TestNavigationPage(),
      ),

      // Role Interface Test Page
      GoRoute(
        path: '/role-test',
        name: 'role-test',
        builder: (context, state) => const RoleInterfaceTestPage(),
      ),

      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Customer Routes (Mobile) - Protected
      GoRoute(
        path: '/customer',
        name: 'customer',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: CustomerDashboard(),
        ),
      ),
      GoRoute(
        path: '/customer/scanner',
        name: 'customer-scanner',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: ScannerScreen(),
        ),
      ),
      GoRoute(
        path: '/customer/cart',
        name: 'customer-cart',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: CartScreen(),
        ),
      ),
      GoRoute(
        path: '/customer/history',
        name: 'customer-history',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: HistoryScreen(),
        ),
      ),
      GoRoute(
        path: '/customer/profile',
        name: 'customer-profile',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: ProfileScreen(),
        ),
      ),
      GoRoute(
        path: '/customer/checkout',
        name: 'customer-checkout',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.customer,
          child: CheckoutScreen(),
        ),
      ),
      GoRoute(
        path: '/customer/payment-success/:transactionId',
        name: 'payment-success',
        builder: (context, state) {
          final transactionId = state.pathParameters['transactionId']!;
          return ProtectedRoute(
            requiredRole: UserRole.customer,
            child: PaymentSuccessScreen(transactionId: transactionId),
          );
        },
      ),

      // Sales Rep Routes (Web) - Protected
      GoRoute(
        path: '/sales-rep',
        name: 'sales-rep',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.salesRep,
          child: SalesRepDashboard(),
        ),
      ),

      // Merchant Routes (Web) - Protected
      GoRoute(
        path: '/merchant',
        name: 'merchant',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.merchant,
          child: MerchantDashboard(),
        ),
      ),
      GoRoute(
        path: '/merchant/inventory',
        name: 'merchant-inventory',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.merchant,
          child: ModernInventoryDashboard(),
        ),
      ),

      // Admin Routes (Web) - Protected
      GoRoute(
        path: '/admin',
        name: 'admin',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.admin,
          child: AdminDashboard(),
        ),
      ),
      GoRoute(
        path: '/add-merchant',
        name: 'add-merchant',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.admin,
          child: AddMerchantScreen(),
        ),
      ),

      // Boss Routes (Web) - Protected
      GoRoute(
        path: '/boss',
        name: 'boss',
        builder: (context, state) => const ProtectedRoute(
          requiredRole: UserRole.boss,
          child: BossDashboard(),
        ),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/splash'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods
class AppRouter {
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  static void goToRegister(BuildContext context) {
    context.go('/register');
  }

  static void goToCustomerDashboard(BuildContext context) {
    context.go('/customer');
  }

  static void goToSalesRepDashboard(BuildContext context) {
    context.go('/sales-rep');
  }

  static void goToMerchantDashboard(BuildContext context) {
    context.go('/merchant');
  }

  static void goToAdminDashboard(BuildContext context) {
    context.go('/admin');
  }

  static void goToBossDashboard(BuildContext context) {
    context.go('/boss');
  }

  static void goToScanner(BuildContext context) {
    context.go('/customer/scanner');
  }

  static void goToCart(BuildContext context) {
    context.go('/customer/cart');
  }

  static void goToHistory(BuildContext context) {
    context.go('/customer/history');
  }
}
