class Merchant {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String category;
  final String address;
  final String status;
  final DateTime createdAt;
  final String? logoUrl;
  final double? revenue;
  final int? productsCount;

  Merchant({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.category,
    required this.address,
    this.status = 'pending',
    this.logoUrl,
    this.revenue = 0.0,
    this.productsCount = 0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'category': category,
      'address': address,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'logoUrl': logoUrl,
      'revenue': revenue,
      'productsCount': productsCount,
    };
  }

  factory Merchant.fromJson(Map<String, dynamic> json) {
    return Merchant(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      category: json['category'],
      address: json['address'],
      status: json['status'] ?? 'pending',
      logoUrl: json['logoUrl'],
      revenue: json['revenue']?.toDouble() ?? 0.0,
      productsCount: json['productsCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
