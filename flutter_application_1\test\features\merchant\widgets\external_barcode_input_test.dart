import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mall_management_system/features/merchant/widgets/external_barcode_input.dart';

void main() {
  group('ExternalBarcodeInput', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    testWidgets('should display correctly with default properties',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
            ),
          ),
        ),
      );

      expect(find.text('Barcode'), findsOneWidget);
      expect(find.text('Scan barcode or enter manually'), findsOneWidget);
      expect(find.byIcon(Icons.qr_code), findsOneWidget);
    });

    testWidgets('should show scanning indicator when focused', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
            ),
          ),
        ),
      );

      // Tap to focus the input field
      await tester.tap(find.byType(TextFormField));
      await tester.pump();

      expect(find.text('Ready for barcode scanner input'), findsOneWidget);
      expect(find.byIcon(Icons.qr_code_scanner), findsOneWidget);
    });

    testWidgets('should validate EAN-13 barcodes correctly', (tester) async {
      bool barcodeScanned = false;
      String? scannedBarcode;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                barcodeScanned = true;
                scannedBarcode = barcode;
              },
            ),
          ),
        ),
      );

      // Enter a valid EAN-13 barcode
      const validEAN13 = '1234567890128'; // Valid EAN-13 with correct checksum
      await tester.enterText(find.byType(TextFormField), validEAN13);
      await tester.pump();

      expect(barcodeScanned, isTrue);
      expect(scannedBarcode, equals(validEAN13));
      expect(find.text('Valid barcode detected'), findsOneWidget);
    });

    testWidgets('should validate UPC-A barcodes correctly', (tester) async {
      bool barcodeScanned = false;
      String? scannedBarcode;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                barcodeScanned = true;
                scannedBarcode = barcode;
              },
            ),
          ),
        ),
      );

      // Enter a valid UPC-A barcode
      const validUPCA = '123456789012'; // Valid UPC-A with correct checksum
      await tester.enterText(find.byType(TextFormField), validUPCA);
      await tester.pump();

      expect(barcodeScanned, isTrue);
      expect(scannedBarcode, equals(validUPCA));
    });

    testWidgets('should validate EAN-8 barcodes correctly', (tester) async {
      bool barcodeScanned = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                barcodeScanned = true;
              },
            ),
          ),
        ),
      );

      // Enter a valid EAN-8 barcode
      const validEAN8 = '12345670'; // Valid EAN-8 with correct checksum
      await tester.enterText(find.byType(TextFormField), validEAN8);
      await tester.pump();

      expect(barcodeScanned, isTrue);
    });

    testWidgets('should accept Code 128 barcodes', (tester) async {
      bool barcodeScanned = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                barcodeScanned = true;
              },
            ),
          ),
        ),
      );

      // Enter a Code 128 barcode
      const code128 = 'ABC123DEF456';
      await tester.enterText(find.byType(TextFormField), code128);
      await tester.pump();

      expect(barcodeScanned, isTrue);
    });

    testWidgets('should reject invalid barcodes', (tester) async {
      bool barcodeScanned = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                barcodeScanned = true;
              },
            ),
          ),
        ),
      );

      // Reset for each test
      barcodeScanned = false;

      // Enter invalid barcodes
      await tester.enterText(find.byType(TextFormField), '123'); // Too short
      await tester.pump();
      expect(barcodeScanned, isFalse);

      barcodeScanned = false;
      await tester.enterText(find.byType(TextFormField), ''); // Empty
      await tester.pump();
      expect(barcodeScanned, isFalse);

      barcodeScanned = false;
      await tester.enterText(
          find.byType(TextFormField), 'abc'); // Too short and invalid
      await tester.pump();
      expect(barcodeScanned, isFalse);
    });

    testWidgets('should show success animation when valid barcode is detected',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {},
            ),
          ),
        ),
      );

      // Enter a valid barcode
      await tester.enterText(find.byType(TextFormField), '1234567890128');
      await tester.pump();

      // Check for success indicator text (more reliable than icon count)
      expect(find.text('Valid barcode detected'), findsOneWidget);
    });

    testWidgets('should call validator when provided', (tester) async {
      bool validatorCalled = false;
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              key: formKey,
              child: ExternalBarcodeInput(
                controller: controller,
                validator: (value) {
                  validatorCalled = true;
                  if (value == null || value.isEmpty) {
                    return 'Barcode is required';
                  }
                  return null;
                },
              ),
            ),
          ),
        ),
      );

      // Trigger validation by submitting the form
      formKey.currentState?.validate();

      expect(validatorCalled, isTrue);
    });

    testWidgets('should show format support info when scanning',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
            ),
          ),
        ),
      );

      // Focus the input to show scanning state
      await tester.tap(find.byType(TextFormField));
      await tester.pump();

      expect(find.text('Supports: UPC-A, EAN-13, EAN-8, Code 128, and more'),
          findsOneWidget);
    });

    testWidgets('should handle manual entry button when provided',
        (tester) async {
      bool manualEntryPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onManualEntry: () {
                manualEntryPressed = true;
              },
            ),
          ),
        ),
      );

      // Find and tap the manual entry button
      await tester.tap(find.byIcon(Icons.keyboard));
      await tester.pump();

      expect(manualEntryPressed, isTrue);
    });

    testWidgets('should prevent duplicate barcode detection', (tester) async {
      int scanCount = 0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExternalBarcodeInput(
              controller: controller,
              onBarcodeScanned: (barcode) {
                scanCount++;
              },
            ),
          ),
        ),
      );

      const barcode = '1234567890128';

      // Enter the same barcode multiple times
      await tester.enterText(find.byType(TextFormField), barcode);
      await tester.pump();

      await tester.enterText(find.byType(TextFormField), barcode);
      await tester.pump();

      // Should only be called once
      expect(scanCount, equals(1));
    });
  });
}
