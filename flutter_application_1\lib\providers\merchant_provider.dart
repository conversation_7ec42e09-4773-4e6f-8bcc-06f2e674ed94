import 'package:flutter_riverpod/flutter_riverpod.dart';

class Merchant {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String category;
  final String address;
  final String status;

  Merchant({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.category,
    required this.address,
    this.status = 'pending',
  });
}

class MerchantNotifier extends StateNotifier<List<Merchant>> {
  MerchantNotifier() : super([]);

  void addMerchant({
    required String name,
    required String email,
    required String phone,
    required String category,
    required String address,
  }) {
    final newMerchant = Merchant(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      email: email,
      phone: phone,
      category: category,
      address: address,
    );
    
    state = [...state, newMerchant];
  }

  List<Merchant> get pendingMerchants => 
      state.where((merchant) => merchant.status == 'pending').toList();

  List<Merchant> get activeMerchants => 
      state.where((merchant) => merchant.status == 'active').toList();
}

final merchantProvider = StateNotifierProvider<MerchantNotifier, List<Merchant>>(
  (ref) => MerchantNotifier(),
);
