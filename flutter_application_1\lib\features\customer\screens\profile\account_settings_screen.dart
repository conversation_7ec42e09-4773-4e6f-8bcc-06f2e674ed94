import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../models/user_preferences_model.dart';
import '../../../../providers/account_provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../shared/widgets/settings_tile.dart';

class AccountSettingsScreen extends ConsumerWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userId = ref.watch(currentUserIdProvider);
    final preferencesAsync = ref.watch(userPreferencesProvider(userId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Settings'),
      ),
      body: preferencesAsync.when(
        data: (preferences) {
          if (preferences == null) {
            return const Center(
              child: Text('Unable to load preferences'),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Notifications Section
                SettingsSection(
                  title: 'Notifications',
                  tiles: [
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.notifications),
                      title: 'Push Notifications',
                      subtitle: 'Receive notifications on your device',
                      value: preferences.pushNotifications,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(pushNotifications: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.email),
                      title: 'Email Notifications',
                      subtitle: 'Receive notifications via email',
                      value: preferences.emailNotifications,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(emailNotifications: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.sms),
                      title: 'SMS Notifications',
                      subtitle: 'Receive notifications via SMS',
                      value: preferences.smsNotifications,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(smsNotifications: value),
                      ),
                    ),
                  ],
                ),

                // Notification Types Section
                SettingsSection(
                  title: 'Notification Types',
                  tiles: [
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.shopping_bag),
                      title: 'Order Updates',
                      subtitle: 'Notifications about your orders',
                      value: preferences.orderUpdates,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(orderUpdates: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.local_offer),
                      title: 'Promotional Offers',
                      subtitle: 'Special deals and discounts',
                      value: preferences.promotionalOffers,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(promotionalOffers: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.security),
                      title: 'Security Alerts',
                      subtitle: 'Important security notifications',
                      value: preferences.securityAlerts,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(securityAlerts: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.campaign),
                      title: 'Marketing Emails',
                      subtitle: 'Newsletter and marketing content',
                      value: preferences.marketingEmails,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(marketingEmails: value),
                      ),
                    ),
                  ],
                ),

                // Appearance Section
                SettingsSection(
                  title: 'Appearance',
                  tiles: [
                    SettingsTile.selection(
                      leading: const Icon(Icons.palette),
                      title: 'Theme',
                      subtitle: preferences.themeMode.displayName,
                      onTap: () => _showThemeSelector(context, ref, preferences),
                    ),
                    SettingsTile.selection(
                      leading: const Icon(Icons.language),
                      title: 'Language',
                      subtitle: preferences.language.displayName,
                      onTap: () => _showLanguageSelector(context, ref, preferences),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.text_fields),
                      title: 'Font Size',
                      subtitle: _getFontSizeLabel(preferences.fontSize),
                      onTap: () => _showFontSizeSelector(context, ref, preferences),
                    ),
                  ],
                ),

                // Accessibility Section
                SettingsSection(
                  title: 'Accessibility',
                  tiles: [
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.contrast),
                      title: 'High Contrast',
                      subtitle: 'Increase contrast for better visibility',
                      value: preferences.highContrast,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(highContrast: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.animation),
                      title: 'Reduce Animations',
                      subtitle: 'Minimize motion for better accessibility',
                      value: preferences.reduceAnimations,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(reduceAnimations: value),
                      ),
                    ),
                  ],
                ),

                // Privacy Section
                SettingsSection(
                  title: 'Privacy & Data',
                  tiles: [
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.location_on),
                      title: 'Location Services',
                      subtitle: 'Allow location-based features',
                      value: preferences.locationServices,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(locationServices: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.analytics),
                      title: 'Data Analytics',
                      subtitle: 'Help improve the app with usage data',
                      value: preferences.dataAnalytics,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(dataAnalytics: value),
                      ),
                    ),
                    SettingsTile.switchTile(
                      leading: const Icon(Icons.ads_click),
                      title: 'Personalized Ads',
                      subtitle: 'Show ads based on your interests',
                      value: preferences.personalizedAds,
                      onToggle: (value) => _updatePreferences(
                        ref,
                        preferences.copyWith(personalizedAds: value),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading settings',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updatePreferences(WidgetRef ref, UserPreferencesModel preferences) {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      ref.read(accountControllerProvider.notifier).updatePreferences(
        userId: userId,
        preferences: preferences,
      );
    }
  }

  void _showThemeSelector(BuildContext context, WidgetRef ref, UserPreferencesModel preferences) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((theme) {
            return RadioListTile<ThemeMode>(
              title: Text(theme.displayName),
              value: theme,
              groupValue: preferences.themeMode,
              onChanged: (value) {
                if (value != null) {
                  _updatePreferences(ref, preferences.copyWith(themeMode: value));
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelector(BuildContext context, WidgetRef ref, UserPreferencesModel preferences) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: Language.values.map((language) {
            return RadioListTile<Language>(
              title: Text(language.displayName),
              value: language,
              groupValue: preferences.language,
              onChanged: (value) {
                if (value != null) {
                  _updatePreferences(ref, preferences.copyWith(language: value));
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFontSizeSelector(BuildContext context, WidgetRef ref, UserPreferencesModel preferences) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Font Size'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Sample Text',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: (Theme.of(context).textTheme.bodyLarge?.fontSize ?? 16) * preferences.fontSize,
                  ),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: preferences.fontSize,
                  min: 0.8,
                  max: 1.4,
                  divisions: 6,
                  label: _getFontSizeLabel(preferences.fontSize),
                  onChanged: (value) {
                    setState(() {
                      // Update local state for immediate preview
                    });
                    _updatePreferences(ref, preferences.copyWith(fontSize: value));
                  },
                ),
                Text(_getFontSizeLabel(preferences.fontSize)),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  String _getFontSizeLabel(double fontSize) {
    if (fontSize <= 0.9) return 'Small';
    if (fontSize <= 1.1) return 'Normal';
    if (fontSize <= 1.3) return 'Large';
    return 'Extra Large';
  }
}
