import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/product_model.dart';
import '../../../providers/demo_product_provider.dart';
import '../../../shared/themes/merchant_theme.dart';
import '../widgets/enhanced_add_product_dialog.dart';

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});

  @override
  ConsumerState<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends ConsumerState<InventoryScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedStockFilter = 'All';
  bool _showGrid = true;
  
  late AnimationController _pageController;
  late Animation<double> _pageAnimation;
  
  final List<String> _categories = [
    'All', 'Electronics', 'Clothing', 'Food', 'Books', 
    'Home', 'Sports', 'Beauty', 'Automotive', 'Health', 'Toys'
  ];
  
  final List<String> _stockFilters = [
    'All', 'In Stock', 'Low Stock', 'Out of Stock'
  ];

  @override
  void initState() {
    super.initState();
    _pageController = AnimationController(
      duration: MerchantTheme.slowAnimation,
      vsync: this,
    );
    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeOutCubic,
    );
    _pageController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(demoCurrentUserProductsProvider);
    final padding = MerchantTheme.getResponsivePadding(context);
    
    return Theme(
      data: MerchantTheme.lightTheme,
      child: Scaffold(
        backgroundColor: MerchantTheme.neutral50,
        body: FadeTransition(
          opacity: _pageAnimation,
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 1200),
              child: CustomScrollView(
                slivers: [
                  // Header
                  SliverToBoxAdapter(
                    child: Container(
                      padding: EdgeInsets.fromLTRB(padding, padding * 2, padding, padding),
                      decoration: const BoxDecoration(
                        gradient: MerchantTheme.primaryGradient,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.inventory_2, color: Colors.white, size: 32),
                              const SizedBox(width: MerchantTheme.spacing16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Inventory Management',
                                      style: MerchantTheme.headline2.copyWith(color: Colors.white),
                                    ),
                                    Text(
                                      'Manage your products and stock levels',
                                      style: MerchantTheme.bodyLarge.copyWith(color: Colors.white70),
                                    ),
                                  ],
                                ),
                              ),
                              _buildViewToggle(),
                            ],
                          ),
                          const SizedBox(height: MerchantTheme.spacing24),
                          _buildInventoryStats(productsAsync),
                        ],
                      ),
                    ),
                  ),
                  
                  // Search and Filters
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(padding),
                      child: _buildSearchAndFilters(),
                    ),
                  ),
                  
                  // Products
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: padding),
                      child: productsAsync.when(
                        data: (products) => _buildProductsSection(products),
                        loading: () => _buildLoadingSection(),
                        error: (error, stack) => _buildErrorSection(error.toString()),
                      ),
                    ),
                  ),
                  
                  const SliverToBoxAdapter(
                    child: SizedBox(height: MerchantTheme.spacing64),
                  ),
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: Semantics(
          label: 'Add Product',
          button: true,
          child: Tooltip(
            message: 'Add Product',
            child: Focus(
              child: FloatingActionButton.extended(
                onPressed: _showAddProductDialog,
                backgroundColor: MerchantTheme.primaryBlue,
                foregroundColor: Colors.white,
                icon: const Icon(Icons.add),
                label: const Text('Add Product'),
              ),
              onFocusChange: (hasFocus) => setState(() => _fabHasFocus = hasFocus),
              autofocus: false,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildViewToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton(
            icon: Icons.grid_view,
            isSelected: _showGrid,
            onTap: () => setState(() => _showGrid = true),
          ),
          _buildToggleButton(
            icon: Icons.view_list,
            isSelected: !_showGrid,
            onTap: () => setState(() => _showGrid = false),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(MerchantTheme.spacing8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusSmall),
        ),
        child: Icon(
          icon,
          color: isSelected ? MerchantTheme.primaryBlue : Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildInventoryStats(AsyncValue<List<ProductModel>> productsAsync) {
    return productsAsync.when(
      data: (products) {
        final totalProducts = products.length;
        final lowStockProducts = products.where((p) => p.isLowStock && !p.isOutOfStock).length;
        final outOfStockProducts = products.where((p) => p.isOutOfStock).length;
        final totalValue = products.fold<double>(0, (sum, p) => sum + p.totalStockValue);

        return Row(
          children: [
            Expanded(child: _buildStatCard('Total Products', totalProducts.toString(), Icons.inventory_2, MerchantTheme.primaryBlue)),
            const SizedBox(width: MerchantTheme.spacing16),
            Expanded(child: _buildStatCard('Low Stock', lowStockProducts.toString(), Icons.warning, MerchantTheme.warningOrange)),
            const SizedBox(width: MerchantTheme.spacing16),
            Expanded(child: _buildStatCard('Out of Stock', outOfStockProducts.toString(), Icons.error, MerchantTheme.errorRed)),
            const SizedBox(width: MerchantTheme.spacing16),
            Expanded(child: _buildStatCard('Total Value', '₦${totalValue.toStringAsFixed(0)}', Icons.attach_money, MerchantTheme.successGreen)),
          ],
        );
      },
      loading: () => Row(
        children: List.generate(4, (index) => Expanded(
          child: Container(
            height: 80,
            margin: EdgeInsets.only(right: index < 3 ? MerchantTheme.spacing16 : 0),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            ),
            child: const Center(child: CircularProgressIndicator(color: Colors.white)),
          ),
        )),
      ),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Text(
                value,
                style: MerchantTheme.headline3.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          Text(
            title,
            style: MerchantTheme.bodyMedium.copyWith(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
            boxShadow: MerchantTheme.cardShadow,
          ),
          child: TextField(
            controller: _searchController,
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: 'Search products...',
              prefixIcon: const Icon(Icons.search, color: MerchantTheme.neutral500),
              suffixIcon: _searchQuery.isNotEmpty
                  ? Semantics(
                      label: 'Clear Search',
                      button: true,
                      child: Tooltip(
                        message: 'Clear Search',
                        child: Focus(
                          child: IconButton(
                            icon: const Icon(Icons.clear, color: MerchantTheme.neutral500),
                            onPressed: () {
                              _searchController.clear();
                              setState(() => _searchQuery = '');
                            },
                          ),
                          onFocusChange: (hasFocus) => setState(() => _searchHasFocus = hasFocus),
                          autofocus: false,
                        ),
                      ),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: MerchantTheme.spacing20,
                vertical: MerchantTheme.spacing16,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: MerchantTheme.spacing16),
        
        Row(
          children: [
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedCategory,
                items: _categories,
                onChanged: (value) => setState(() => _selectedCategory = value!),
                label: 'Category',
              ),
            ),
            const SizedBox(width: MerchantTheme.spacing12),
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedStockFilter,
                items: _stockFilters,
                onChanged: (value) => setState(() => _selectedStockFilter = value!),
                label: 'Stock Status',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterDropdown({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: MerchantTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        border: Border.all(color: MerchantTheme.neutral200),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          items: items.map((item) => DropdownMenuItem(
            value: item,
            child: Text(item, style: MerchantTheme.bodyMedium),
          )).toList(),
          onChanged: onChanged,
          icon: const Icon(Icons.keyboard_arrow_down, color: MerchantTheme.neutral500),
          isExpanded: true,
        ),
      ),
    );
  }

  Widget _buildProductsSection(List<ProductModel> products) {
    final filteredProducts = _filterProducts(products);
    
    if (filteredProducts.isEmpty) {
      return _buildEmptyState();
    }

    if (_showGrid) {
      return _buildGridView(filteredProducts);
    } else {
      return _buildListView(filteredProducts);
    }
  }

  List<ProductModel> _filterProducts(List<ProductModel> products) {
    return products.where((product) {
      final matchesSearch = _searchQuery.isEmpty ||
          product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          product.barcode.contains(_searchQuery) ||
          product.category.toLowerCase().contains(_searchQuery.toLowerCase());
      
      final matchesCategory = _selectedCategory == 'All' || product.category == _selectedCategory;
      
      bool matchesStock = true;
      switch (_selectedStockFilter) {
        case 'In Stock':
          matchesStock = !product.isLowStock && !product.isOutOfStock;
          break;
        case 'Low Stock':
          matchesStock = product.isLowStock && !product.isOutOfStock;
          break;
        case 'Out of Stock':
          matchesStock = product.isOutOfStock;
          break;
      }
      
      return matchesSearch && matchesCategory && matchesStock;
    }).toList();
  }

  Widget _buildGridView(List<ProductModel> products) {
    final crossAxisCount = MerchantTheme.getResponsiveGridCrossAxisCount(context);
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: MerchantTheme.spacing16,
        mainAxisSpacing: MerchantTheme.spacing16,
        childAspectRatio: 0.8,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return _buildProductCard(product);
      },
    );
  }

  Widget _buildProductCard(ProductModel product) {
    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredIndex = index),
      onExit: (_) => setState(() => _hoveredIndex = -1),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
          boxShadow: _hoveredIndex == index ? [BoxShadow(color: Colors.black12, blurRadius: 10)] : MerchantTheme.cardShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image Placeholder
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: MerchantTheme.neutral100,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(MerchantTheme.radiusMedium),
                  topRight: Radius.circular(MerchantTheme.radiusMedium),
                ),
              ),
              child: Center(
                child: Icon(
                  Icons.inventory_2,
                  color: MerchantTheme.primaryBlue,
                  size: 40,
                ),
              ),
            ),
            
            // Product Info
            Padding(
              padding: const EdgeInsets.all(MerchantTheme.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: MerchantTheme.headline4,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: MerchantTheme.spacing8),
                  Text(
                    product.description,
                    style: MerchantTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: MerchantTheme.spacing12),
                  Row(
                    children: [
                      _buildStockChip(product),
                      const Spacer(),
                      Text(
                        '₦${product.pricePerCarton.toStringAsFixed(0)}',
                        style: MerchantTheme.labelLarge.copyWith(
                          color: MerchantTheme.successGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: MerchantTheme.spacing12),
                  Row(
                    children: [
                      Semantics(
                        label: 'Edit Product',
                        button: true,
                        child: Tooltip(
                          message: 'Edit Product',
                          child: Focus(
                            child: OutlinedButton(
                              onPressed: () => _showEditProductDialog(product),
                              child: const Text('Edit'),
                            ),
                            onFocusChange: (hasFocus) => setState(() => _editHasFocus = hasFocus),
                            autofocus: false,
                          ),
                        ),
                      ),
                      const SizedBox(width: MerchantTheme.spacing8),
                      Semantics(
                        label: 'Update Stock',
                        button: true,
                        child: Tooltip(
                          message: 'Update Stock',
                          child: ElevatedButton(
                            onPressed: () => _showStockUpdateDialog(product),
                            child: const Text('Stock'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListView(List<ProductModel> products) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return Container(
          margin: const EdgeInsets.only(bottom: MerchantTheme.spacing16),
          child: _buildProductListTile(product),
        );
      },
    );
  }

  Widget _buildProductListTile(ProductModel product) {
    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredIndex = index),
      onExit: (_) => setState(() => _hoveredIndex = -1),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
          boxShadow: _hoveredIndex == index ? [BoxShadow(color: Colors.black12, blurRadius: 10)] : MerchantTheme.cardShadow,
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.all(MerchantTheme.spacing16),
          leading: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: MerchantTheme.neutral100,
              borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
            ),
            child: Icon(Icons.inventory_2, color: MerchantTheme.primaryBlue, size: 30),
          ),
          title: Text(product.name, style: MerchantTheme.headline4),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: MerchantTheme.spacing8),
              Text(
                product.description,
                style: MerchantTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: MerchantTheme.spacing8),
              Row(
                children: [
                  _buildStockChip(product),
                  const SizedBox(width: MerchantTheme.spacing8),
                  Text(
                    '₦${product.pricePerCarton.toStringAsFixed(0)}',
                    style: MerchantTheme.labelLarge.copyWith(
                      color: MerchantTheme.successGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          trailing: Semantics(
            label: 'Product Actions',
            button: true,
            child: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditProductDialog(product);
                    break;
                  case 'delete':
                    _deleteProduct(product);
                    break;
                  case 'stock':
                    _showStockUpdateDialog(product);
                    break;
                }
              },
              itemBuilder: (context) => [
                Semantics(
                  label: 'Edit Product',
                  button: true,
                  child: PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                ),
                Semantics(
                  label: 'Update Stock',
                  button: true,
                  child: PopupMenuItem(
                    value: 'stock',
                    child: Row(
                      children: [
                        Icon(Icons.inventory, size: 20),
                        SizedBox(width: 8),
                        Text('Update Stock'),
                      ],
                    ),
                  ),
                ),
                Semantics(
                  label: 'Delete Product',
                  button: true,
                  child: PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red, size: 20),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStockChip(ProductModel product) {
    Color color;
    String text;
    
    if (product.isOutOfStock) {
      color = MerchantTheme.errorRed;
      text = 'Out of Stock';
    } else if (product.isLowStock) {
      color = MerchantTheme.warningOrange;
      text = 'Low Stock';
    } else {
      color = MerchantTheme.successGreen;
      text = 'In Stock';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: MerchantTheme.spacing8,
        vertical: MerchantTheme.spacing4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusSmall),
      ),
      child: Text(
        text,
        style: MerchantTheme.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing64),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2_outlined, size: 80, color: MerchantTheme.neutral400),
          const SizedBox(height: MerchantTheme.spacing24),
          Text(
            'No products found',
            style: MerchantTheme.headline3.copyWith(color: MerchantTheme.neutral600),
          ),
          const SizedBox(height: MerchantTheme.spacing12),
          Text(
            'Try adjusting your search or filters',
            style: MerchantTheme.bodyLarge.copyWith(color: MerchantTheme.neutral500),
          ),
          const SizedBox(height: MerchantTheme.spacing32),
          Tooltip(
            message: 'Add Product',
            child: ElevatedButton.icon(
              onPressed: _showAddProductDialog,
              icon: Icon(Icons.add),
              label: Text('Add Your First Product'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingSection() {
    // Use skeleton loaders (placeholder cards) for a modern loading experience
    final crossAxisCount = MerchantTheme.getResponsiveGridCrossAxisCount(context);
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: MerchantTheme.spacing16,
        mainAxisSpacing: MerchantTheme.spacing16,
        childAspectRatio: 0.8,
      ),
      itemCount: 6,
      itemBuilder: (context, index) => AnimatedContainer(
        duration: Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: MerchantTheme.neutral100,
          borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        ),
        child: Center(
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: MerchantTheme.neutral200,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorSection(String error) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: MerchantTheme.errorRed),
          const SizedBox(height: MerchantTheme.spacing16),
          Text(
            'Error loading products',
            style: MerchantTheme.headline3.copyWith(color: MerchantTheme.errorRed),
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          Text(error, style: MerchantTheme.bodyMedium, textAlign: TextAlign.center),
          const SizedBox(height: MerchantTheme.spacing24),
          Tooltip(
            message: 'Retry',
            child: ElevatedButton.icon(
              onPressed: () => ref.invalidate(demoCurrentUserProductsProvider),
              icon: Icon(Icons.refresh),
              label: Text('Retry'),
            ),
          ),
        ],
      ),
    );
  }

  // Action Methods
  void _showAddProductDialog() {
    showDialog(
      context: context,
      builder: (context) => const EnhancedAddProductDialog(),
    );
  }

  void _showEditProductDialog(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => EnhancedAddProductDialog(product: product),
    );
  }

  void _showStockUpdateDialog(ProductModel product) {
    final controller = TextEditingController(text: product.stockQuantity.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Stock'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'New Stock Quantity',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newQuantity = int.tryParse(controller.text);
              if (newQuantity != null) {
                _updateStock(product, newQuantity);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateStock(ProductModel product, int newQuantity) async {
    try {
      await ref.read(demoProductRepositoryProvider).updateStock(product.id, newQuantity);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Stock updated for ${product.name}'),
            backgroundColor: MerchantTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating stock: $e'),
            backgroundColor: MerchantTheme.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _deleteProduct(ProductModel product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${product.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: MerchantTheme.errorRed),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(demoProductRepositoryProvider).deleteProduct(product.id);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${product.name} deleted successfully'),
              backgroundColor: MerchantTheme.successGreen,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting product: $e'),
              backgroundColor: MerchantTheme.errorRed,
            ),
          );
        }
      }
    }
  }
} 