2025-06-27 19:00:44,527 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:01:56,715 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:01:56,797 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:04:06,885 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:04:10,738 INFO [core.ui.api_server] IPC server started on localhost:8262
2025-06-27 19:04:10,740 DEBUG [core.ui.ipc_client] Connecting to IPC server at localhost:8125
2025-06-27 19:04:12,782 DEBUG [core.telemetry] Sending trace event stack-choice to https://api.pythagora.io/telemetry: {'pathId': '4f03f4b182ba4ba29639ed8073616c65', 'event': 'trace-stack-choice', 'data': {'language': 'node', 'app_id': None, 'user_contact': '<EMAIL>', 'platform': 'win32', 'pilot_version': '1.3.25', 'model': 'gpt-4o-2024-05-13'}}
2025-06-27 19:04:15,534 INFO [core.state.state_manager] Created new project "temp-project" (id=20d7245f-f550-4f3d-b028-6d2288be2325) with default branch "None" (id=None) and initial state id=None (step_index=1)
2025-06-27 19:04:15,536 DEBUG [core.telemetry] Sending trace event create-project to https://api.pythagora.io/telemetry: {'pathId': '4f03f4b182ba4ba29639ed8073616c65', 'event': 'trace-create-project', 'data': {'name': 'temp-project', 'app_id': None, 'user_contact': '<EMAIL>', 'platform': 'win32', 'pilot_version': '1.3.25', 'model': 'gpt-4o-2024-05-13'}}
2025-06-27 19:04:20,011 WARNING [core.state.state_manager] Failed to upload new project: Object of type UUID is not JSON serializable
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\state\state_manager.py", line 215, in create_project
    resp = await client.post(
           ^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1892, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1561, in request
    request = self.build_request(
        method=method,
    ...<9 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 357, in build_request
    return Request(
        method,
    ...<8 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_models.py", line 340, in __init__
    headers, stream = encode_request(
                      ~~~~~~~~~~~~~~^
        content=content,
        ^^^^^^^^^^^^^^^^
    ...<7 lines>...
        ),
        ^^
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 212, in encode_request
    return encode_json(json)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 175, in encode_json
    body = json_dumps(json).encode("utf-8")
           ~~~~~~~~~~^^^^^^
  File "C:\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type UUID is not JSON serializable
2025-06-27 19:04:20,883 WARNING [core.state.state_manager] Failed to upload new project: Object of type UUID is not JSON serializable
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\state\state_manager.py", line 215, in create_project
    resp = await client.post(
           ^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1892, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1561, in request
    request = self.build_request(
        method=method,
    ...<9 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 357, in build_request
    return Request(
        method,
    ...<8 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_models.py", line 340, in __init__
    headers, stream = encode_request(
                      ~~~~~~~~~~~~~~^
        content=content,
        ^^^^^^^^^^^^^^^^
    ...<7 lines>...
        ),
        ^^
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 212, in encode_request
    return encode_json(json)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 175, in encode_json
    body = json_dumps(json).encode("utf-8")
           ~~~~~~~~~~^^^^^^
  File "C:\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type UUID is not JSON serializable
2025-06-27 19:04:21,656 WARNING [core.state.state_manager] Failed to upload new project: Object of type UUID is not JSON serializable
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\state\state_manager.py", line 215, in create_project
    resp = await client.post(
           ^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1892, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1561, in request
    request = self.build_request(
        method=method,
    ...<9 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 357, in build_request
    return Request(
        method,
    ...<8 lines>...
        extensions=extensions,
    )
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_models.py", line 340, in __init__
    headers, stream = encode_request(
                      ~~~~~~~~~~~~~~^
        content=content,
        ^^^^^^^^^^^^^^^^
    ...<7 lines>...
        ),
        ^^
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 212, in encode_request
    return encode_json(json)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_content.py", line 175, in encode_json
    body = json_dumps(json).encode("utf-8")
           ~~~~~~~~~~^^^^^^
  File "C:\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type UUID is not JSON serializable
2025-06-27 19:04:21,659 INFO [core.agents.orchestrator] Starting core.agents.orchestrator.Orchestrator
2025-06-27 19:04:21,660 DEBUG [core.ui.ipc_client] Sending project loading finished signal to the extension
2025-06-27 19:04:21,661 INFO [core.agents.orchestrator] Checking for offline changes.
2025-06-27 19:04:21,661 INFO [core.agents.orchestrator] Offline changes check done.
2025-06-27 19:04:21,662 DEBUG [core.ui.ipc_client] Sending updated knowledge base
2025-06-27 19:04:21,663 DEBUG [core.agents.orchestrator] Running agent Wizard (step 1)
2025-06-27 19:04:21,664 DEBUG [core.agents.orchestrator] Running agent SpecWriter (step 1)
2025-06-27 19:04:21,665 DEBUG [core.ui.ipc_client] Sending message: [## Write specification

Pythagora is generating a detailed specification for app based on your input.] from agent:spec-writer
2025-06-27 19:04:29,697 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:04:29,716 DEBUG [core.agents.convo] Loading template spec-writer/build_full_specification.prompt
2025-06-27 19:04:29,737 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:04:29,738 DEBUG [core.llm.base] Calling openai model claude-sonnet-4-20250514 (temp=0.0), prompt length: 1.4 KB
2025-06-27 19:04:35,311 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60865)
2025-06-27 19:04:35,313 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60866)
2025-06-27 19:04:35,316 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60867)
2025-06-27 19:04:35,318 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60868)
2025-06-27 19:04:35,320 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60869)
2025-06-27 19:04:35,323 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60870)
2025-06-27 19:04:35,326 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60871)
2025-06-27 19:04:35,328 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60872)
2025-06-27 19:04:35,331 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60873)
2025-06-27 19:04:35,333 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60874)
2025-06-27 19:04:35,335 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 60875)
2025-06-27 19:04:35,347 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047475335547149
2025-06-27 19:04:35,349 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047475335547149
2025-06-27 19:04:35,351 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475336742427
2025-06-27 19:04:35,352 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475336742427
2025-06-27 19:04:35,354 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047475336464879
2025-06-27 19:04:35,355 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047475336464879
2025-06-27 19:04:35,358 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475337718147
2025-06-27 19:04:35,360 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475337718147
2025-06-27 19:04:35,361 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047475338477788
2025-06-27 19:04:35,363 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047475338477788
2025-06-27 19:04:35,365 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475339757637
2025-06-27 19:04:35,366 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475339757637
2025-06-27 19:04:35,368 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047475342388826
2025-06-27 19:04:35,370 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047475342388826
2025-06-27 19:04:35,372 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475343638580
2025-06-27 19:04:35,373 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475343638580
2025-06-27 19:04:35,375 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047475344115759
2025-06-27 19:04:35,376 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047475344115759
2025-06-27 19:04:35,379 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475345740373
2025-06-27 19:04:35,380 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475345740373
2025-06-27 19:04:35,382 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047475347358690
2025-06-27 19:04:35,383 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047475347358690
2025-06-27 19:04:51,469 DEBUG [core.ui.ipc_client] Sending message: [] from agent:spec-writer
2025-06-27 19:04:51,671 WARNING [core.llm.openai_client] OpenAI response did not include token counts, estimating with tiktoken: 280 input tokens, 733 output tokens
2025-06-27 19:04:51,675 DEBUG [core.llm.base] Total openai response time 21.94s, 280 prompt tokens, 733 completion tokens used
2025-06-27 19:04:53,085 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:04:53,087 DEBUG [core.agents.convo] Loading template spec-writer/project_name.prompt
2025-06-27 19:04:53,122 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:04:53,128 DEBUG [core.llm.base] Calling openai model gpt-4o-2024-05-13 (temp=0), prompt length: 4.2 KB
2025-06-27 19:04:56,112 DEBUG [core.llm.base] Total openai response time 2.98s, 846 prompt tokens, 3 completion tokens used
2025-06-27 19:04:56,121 DEBUG [core.state.state_manager] Committing session
2025-06-27 19:04:56,149 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:04:56,305 DEBUG [core.state.state_manager] Session committed successfully
2025-06-27 19:04:56,331 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:04:56,375 DEBUG [core.agents.orchestrator] Agent SpecWriter returned
2025-06-27 19:04:56,378 DEBUG [core.agents.orchestrator] Running agent SpecWriter (step 1)
2025-06-27 19:04:57,623 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:04:57,625 DEBUG [core.agents.convo] Loading template spec-writer/build_full_specification.prompt
2025-06-27 19:04:57,627 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047496907742782
2025-06-27 19:04:57,642 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047496907742782
2025-06-27 19:04:57,647 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047496945784392
2025-06-27 19:04:57,649 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047496945784392
2025-06-27 19:05:07,721 DEBUG [core.ui.ipc_client] Sending message: [## What would you like to add?] from agent:spec-writer
2025-06-27 19:06:12,905 INFO [core.cli.main] Interrupted by user
2025-06-27 19:06:12,907 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60865) disconnected
2025-06-27 19:06:12,908 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60866) disconnected
2025-06-27 19:06:12,909 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60867) disconnected
2025-06-27 19:06:12,910 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60868) disconnected
2025-06-27 19:06:12,911 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60865) closed
2025-06-27 19:06:12,912 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60869) disconnected
2025-06-27 19:06:12,913 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60870) disconnected
2025-06-27 19:06:12,914 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60871) disconnected
2025-06-27 19:06:12,915 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60872) disconnected
2025-06-27 19:06:12,915 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60873) disconnected
2025-06-27 19:06:12,916 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60874) disconnected
2025-06-27 19:06:12,917 DEBUG [core.ui.api_server] Client ('127.0.0.1', 60875) disconnected
2025-06-27 19:06:12,918 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60866) closed
2025-06-27 19:06:12,919 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60867) closed
2025-06-27 19:06:12,920 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60868) closed
2025-06-27 19:06:12,922 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60869) closed
2025-06-27 19:06:12,923 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60870) closed
2025-06-27 19:06:12,923 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60871) closed
2025-06-27 19:06:12,924 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60872) closed
2025-06-27 19:06:12,925 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60873) closed
2025-06-27 19:06:12,926 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60874) closed
2025-06-27 19:06:12,927 DEBUG [core.ui.api_server] Connection to ('127.0.0.1', 60875) closed
2025-06-27 19:06:12,928 DEBUG [core.telemetry] Telemetry.send(): sending telemetry data to https://api.pythagora.io/telemetry
2025-06-27 19:08:49,735 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:08:49,802 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:09:43,907 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:09:43,976 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:10:33,303 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:10:33,372 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:11:02,432 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:11:04,088 INFO [core.ui.api_server] IPC server started on localhost:8262
2025-06-27 19:11:04,092 DEBUG [core.ui.ipc_client] Connecting to IPC server at localhost:8125
2025-06-27 19:11:06,159 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:11:06,594 DEBUG [core.db.models.project_state] Deleting all project states in branch 83faf115-e0f5-4b33-8c24-b67f4cba6a29 after 73f1b998-bc98-4936-a1de-03550d3018b7
2025-06-27 19:11:06,699 DEBUG [core.state.state_manager] Loaded project <Project(id=20d7245f-f550-4f3d-b028-6d2288be2325)> (20d7245f-f550-4f3d-b028-6d2288be2325) branch <Branch(id=83faf115-e0f5-4b33-8c24-b67f4cba6a29)> (83faf115-e0f5-4b33-8c24-b67f4cba6a29step 1 (state id=73f1b998-bc98-4936-a1de-03550d3018b7)
2025-06-27 19:11:06,724 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:11:06,870 DEBUG [core.db.models.project_state] Found 0 states in branch
2025-06-27 19:11:06,905 DEBUG [core.db.models.project_state] []
2025-06-27 19:11:06,953 INFO [core.agents.orchestrator] Starting core.agents.orchestrator.Orchestrator
2025-06-27 19:11:06,957 DEBUG [core.ui.ipc_client] Sending project loading finished signal to the extension
2025-06-27 19:11:06,959 INFO [core.agents.orchestrator] Checking for offline changes.
2025-06-27 19:11:06,962 INFO [core.agents.orchestrator] Detected empty workspace, restoring state from the database.
2025-06-27 19:11:06,964 INFO [core.agents.orchestrator] Offline changes check done.
2025-06-27 19:11:06,968 DEBUG [core.ui.ipc_client] Sending updated knowledge base
2025-06-27 19:11:06,970 DEBUG [core.agents.orchestrator] Running agent SpecWriter (step 1)
2025-06-27 19:11:09,915 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:11:09,920 DEBUG [core.agents.convo] Loading template spec-writer/build_full_specification.prompt
2025-06-27 19:11:11,231 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 61059)
2025-06-27 19:11:11,233 DEBUG [core.ui.api_server] New connection from ('127.0.0.1', 61060)
2025-06-27 19:11:11,237 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047871232716432
2025-06-27 19:11:11,241 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047871232716432
2025-06-27 19:11:11,243 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751047871234035149
2025-06-27 19:11:11,245 DEBUG [core.ui.api_server] Sending project info with request_id: 1751047871234035149
2025-06-27 19:11:11,250 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751047871246293084
2025-06-27 19:11:11,252 DEBUG [core.ui.api_server] Sending knowledge base with request_id: 1751047871246293084
2025-06-27 19:11:25,732 DEBUG [core.ui.ipc_client] Sending message: [## Refining specification

Pythagora is refining the specs based on your input.] from agent:spec-writer
2025-06-27 19:11:25,739 DEBUG [core.agents.convo] Loading template spec-writer/add_to_specification.prompt
2025-06-27 19:11:25,804 DEBUG [core.agents.convo] Loading template spec-writer/system.prompt
2025-06-27 19:11:25,812 DEBUG [core.llm.base] Calling openai model claude-sonnet-4-20250514 (temp=0.0), prompt length: 5.2 KB
2025-06-27 19:11:41,594 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:11:56,866 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:12:34,174 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:12:51,062 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:12:55,083 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:12:58,783 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:13:09,447 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:13:13,838 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:13:28,602 WARNING [core.llm.base] API connection error: Connection error.
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\base.py", line 231, in __call__
    response, prompt_tokens, completion_tokens = await self._make_request(
                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\core\llm\openai_client.py", line 51, in _make_request
    stream = await self.client.chat.completions.create(**completion_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 312, in _sentry_patched_create_async
    return await _execute_async(f, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 298, in _execute_async
    raise e from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\openai.py", line 295, in _execute_async
    result = await f(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\resources\chat\completions.py", line 1339, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<35 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1815, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1509, in request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1572, in _request
    return await self._retry_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1642, in _retry_request
    return await self._request(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\openai\_base_client.py", line 1582, in _request
    raise APIConnectionError(request=request) from err
openai.APIConnectionError: Connection error.
2025-06-27 19:14:13,838 WARNING [core.cli.main] an LLM API error occurred: Error connecting to the LLM: API connection error: Connection error.
2025-06-27 19:14:13,989 DEBUG [core.telemetry] Telemetry.send(): sending telemetry data to https://api.pythagora.io/telemetry
2025-06-27 19:14:14,624 ERROR [core.telemetry] Telemetry.send(): failed to send telemetry data: [Errno 11001] getaddrinfo failed
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        pool_request.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\telemetry\__init__.py", line 349, in send
    response = await client.post(self.endpoint, json=payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1892, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sentry_sdk\integrations\httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 19:14:14,661 DEBUG [core.ui.ipc_client] Closing the IPC connection to localhost:8125
2025-06-27 19:14:24,994 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_INFO with request ID 1751048064991278499
2025-06-27 19:14:24,996 ERROR [core.ui.api_server] Error handling project info request: Instance <Project at 0x21fc244d2b0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\ui\api_server.py", line 436, in _handle_project_info
    project_details = self.state_manager.get_project_info()
  File "c:\flutter app\flutter_application_1\pythagora-core\core\state\state_manager.py", line 666, in get_project_info
    "name": self.project.name,
            ^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
    ...<2 lines>...
    )
sqlalchemy.orm.exc.DetachedInstanceError: Instance <Project at 0x21fc244d2b0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-06-27 19:14:27,432 DEBUG [core.ui.api_server] Received message of type MessageType.EPICS_AND_TASKS with request ID 1751048067431555526
2025-06-27 19:14:27,434 ERROR [core.ui.api_server] Error handling epics and tasks request: Instance <Branch at 0x21fc20df620> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\ui\api_server.py", line 419, in _handle_epics_and_tasks
    epics_and_tasks = await self.state_manager.get_all_epics_and_tasks(self.state_manager.branch.id)
                                                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
    ...<2 lines>...
    )
sqlalchemy.orm.exc.DetachedInstanceError: Instance <Branch at 0x21fc20df620> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-06-27 19:14:29,291 DEBUG [core.ui.api_server] Received message of type MessageType.PROJECT_SPECS with request ID 1751048069290571479
2025-06-27 19:14:29,294 ERROR [core.ui.api_server] Error handling knowledge base request: Instance <Specification at 0x21fc244dbe0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "c:\flutter app\flutter_application_1\pythagora-core\core\ui\api_server.py", line 489, in _handle_project_specs
    if next_state.specification and next_state.specification.description:
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\flutter app\flutter_application_1\pythagora-core\venv\Lib\site-packages\sqlalchemy\orm\loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
    ...<2 lines>...
    )
sqlalchemy.orm.exc.DetachedInstanceError: Instance <Specification at 0x21fc244dbe0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-06-27 19:14:58,798 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:14:58,835 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-27 19:14:58,849 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-27 19:14:58,894 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-28 01:52:16,377 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-28 01:52:16,662 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-28 01:53:02,066 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-28 01:53:03,065 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
2025-06-28 01:57:40,060 DEBUG [core.db.setup] Running database migrations for sqlite:///data/database/pythagora.db (config: c:\flutter app\flutter_application_1\pythagora-core\core\db\alembic.ini)
2025-06-28 01:57:40,257 DEBUG [core.db.session] Connected to database sqlite+aiosqlite:///data/database/pythagora.db
