import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../models/account_settings_model.dart';
import '../../../../providers/account_provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../services/validation_service.dart';
import '../../../../shared/widgets/forms/custom_text_field.dart';
import '../../../../shared/widgets/settings_tile.dart';

class SecuritySettingsScreen extends ConsumerWidget {
  const SecuritySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userId = ref.watch(currentUserIdProvider);
    final settingsAsync = ref.watch(accountSettingsProvider(userId));
    final user = ref.watch(currentUserProvider).value;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Settings'),
      ),
      body: settingsAsync.when(
        data: (settings) {
          if (settings == null || user == null) {
            return const Center(
              child: Text('Unable to load security settings'),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Security Status Section
                _buildSecurityStatus(context, settings),

                // Password Section
                SettingsSection(
                  title: 'Password & Authentication',
                  tiles: [
                    SecuritySettingsTile(
                      title: 'Change Password',
                      subtitle: settings.lastPasswordChange != null
                          ? 'Last changed ${_formatDate(settings.lastPasswordChange!)}'
                          : 'Never changed',
                      isSecure: !settings.needsPasswordChange,
                      onTap: () => _showChangePasswordDialog(context, ref),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.email),
                      title: 'Password Reset Email',
                      subtitle: 'Send password reset to ${user.email}',
                      onTap: () => _sendPasswordResetEmail(context, ref, user.email),
                    ),
                  ],
                ),

                // Two-Factor Authentication Section
                SettingsSection(
                  title: 'Two-Factor Authentication',
                  tiles: [
                    SettingsTile.switchTile(
                      leading: Icon(
                        Icons.security,
                        color: settings.twoFactorEnabled 
                            ? Theme.of(context).colorScheme.primary 
                            : Theme.of(context).colorScheme.outline,
                      ),
                      title: 'Two-Factor Authentication',
                      subtitle: settings.twoFactorEnabled 
                          ? 'Enabled via ${settings.twoFactorMethod.displayName}'
                          : 'Disabled - Recommended for security',
                      value: settings.twoFactorEnabled,
                      onToggle: (value) => _toggle2FA(context, ref, settings, value),
                    ),
                    if (settings.twoFactorEnabled)
                      SettingsTile.selection(
                        leading: const Icon(Icons.phone_android),
                        title: '2FA Method',
                        subtitle: settings.twoFactorMethod.displayName,
                        onTap: () => _show2FAMethodSelector(context, ref, settings),
                      ),
                  ],
                ),

                // Recovery Options Section
                SettingsSection(
                  title: 'Account Recovery',
                  tiles: [
                    SettingsTile.navigation(
                      leading: const Icon(Icons.email_outlined),
                      title: 'Backup Email',
                      subtitle: settings.backupEmail ?? 'Not set',
                      onTap: () => _showBackupEmailDialog(context, ref, settings),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.phone_outlined),
                      title: 'Recovery Phone',
                      subtitle: settings.recoveryPhone ?? 'Not set',
                      onTap: () => _showRecoveryPhoneDialog(context, ref, settings),
                    ),
                  ],
                ),

                // Security Review Section
                SettingsSection(
                  title: 'Security Review',
                  tiles: [
                    SecuritySettingsTile(
                      title: 'Security Review',
                      subtitle: settings.lastSecurityReview != null
                          ? 'Last reviewed ${_formatDate(settings.lastSecurityReview!)}'
                          : 'Never reviewed',
                      isSecure: !settings.needsSecurityReview,
                      onTap: () => _performSecurityReview(context, ref),
                    ),
                    SettingsTile.navigation(
                      leading: const Icon(Icons.devices),
                      title: 'Trusted Devices',
                      subtitle: '${settings.trustedDevices.length} devices',
                      onTap: () => _showTrustedDevices(context, settings),
                    ),
                  ],
                ),

                // Account Actions Section
                SettingsSection(
                  title: 'Account Actions',
                  tiles: [
                    if (settings.accountDeactivationRequested)
                      SettingsTile.navigation(
                        leading: Icon(
                          Icons.cancel,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        title: 'Cancel Deactivation Request',
                        subtitle: 'Requested on ${_formatDate(settings.deactivationRequestDate!)}',
                        onTap: () => _cancelDeactivation(context, ref),
                      )
                    else
                      SettingsTile.navigation(
                        leading: Icon(
                          Icons.delete_forever,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        title: 'Deactivate Account',
                        subtitle: 'Temporarily disable your account',
                        onTap: () => _showDeactivationDialog(context, ref),
                      ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading security settings',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSecurityStatus(BuildContext context, AccountSettingsModel settings) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final securityScore = _calculateSecurityScore(settings);
    final isSecure = securityScore >= 80;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isSecure ? colorScheme.primaryContainer : colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSecure 
              ? colorScheme.primary.withOpacity(0.3)
              : colorScheme.error.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isSecure ? Icons.shield : Icons.warning,
                color: isSecure ? colorScheme.primary : colorScheme.error,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Security Score',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '$securityScore/100',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: isSecure ? colorScheme.primary : colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: securityScore / 100,
            backgroundColor: colorScheme.outline.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              isSecure ? colorScheme.primary : colorScheme.error,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            isSecure 
                ? 'Your account security is good!'
                : 'Your account security needs attention.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isSecure 
                  ? colorScheme.onPrimaryContainer 
                  : colorScheme.onErrorContainer,
            ),
          ),
        ],
      ),
    );
  }

  int _calculateSecurityScore(AccountSettingsModel settings) {
    int score = 0;
    
    // Password age (30 points max)
    if (settings.lastPasswordChange != null) {
      final daysSinceChange = DateTime.now().difference(settings.lastPasswordChange!).inDays;
      if (daysSinceChange < 90) score += 30;
      else if (daysSinceChange < 180) score += 20;
      else score += 10;
    } else {
      score += 10; // Some points for having a password
    }
    
    // Two-factor authentication (40 points)
    if (settings.twoFactorEnabled) score += 40;
    
    // Recovery options (20 points max)
    if (settings.backupEmail != null) score += 10;
    if (settings.recoveryPhone != null) score += 10;
    
    // Security review (10 points)
    if (settings.lastSecurityReview != null) {
      final daysSinceReview = DateTime.now().difference(settings.lastSecurityReview!).inDays;
      if (daysSinceReview < 90) score += 10;
    }
    
    return score.clamp(0, 100);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${(difference.inDays / 365).floor()} years ago';
    }
  }

  void _showChangePasswordDialog(BuildContext context, WidgetRef ref) {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              PasswordTextField(
                label: 'Current Password',
                controller: currentPasswordController,
                validator: ValidationService.validateCurrentPassword,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              PasswordTextField(
                label: 'New Password',
                controller: newPasswordController,
                validator: ValidationService.validatePassword,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              PasswordTextField(
                label: 'Confirm New Password',
                controller: confirmPasswordController,
                validator: (value) => ValidationService.validateConfirmPassword(
                  value,
                  newPasswordController.text,
                ),
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                try {
                  await ref.read(accountControllerProvider.notifier).changePassword(
                    currentPassword: currentPasswordController.text,
                    newPassword: newPasswordController.text,
                  );
                  
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Password changed successfully!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to change password: $e'),
                        backgroundColor: Theme.of(context).colorScheme.error,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('Change Password'),
          ),
        ],
      ),
    );
  }

  void _sendPasswordResetEmail(BuildContext context, WidgetRef ref, String email) async {
    try {
      await ref.read(accountControllerProvider.notifier).sendPasswordResetEmail(email);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Password reset email sent!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send reset email: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _toggle2FA(BuildContext context, WidgetRef ref, AccountSettingsModel settings, bool enabled) {
    if (enabled) {
      // Show 2FA setup dialog
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Two-factor authentication setup coming soon!'),
        ),
      );
    } else {
      // Disable 2FA
      _updateSettings(ref, settings.copyWith(
        twoFactorEnabled: false,
        twoFactorMethod: TwoFactorMethod.none,
      ));
    }
  }

  void _show2FAMethodSelector(BuildContext context, WidgetRef ref, AccountSettingsModel settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose 2FA Method'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TwoFactorMethod.values.where((method) => method != TwoFactorMethod.none).map((method) {
            return RadioListTile<TwoFactorMethod>(
              title: Text(method.displayName),
              value: method,
              groupValue: settings.twoFactorMethod,
              onChanged: (value) {
                if (value != null) {
                  _updateSettings(ref, settings.copyWith(twoFactorMethod: value));
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showBackupEmailDialog(BuildContext context, WidgetRef ref, AccountSettingsModel settings) {
    final controller = TextEditingController(text: settings.backupEmail ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Email'),
        content: Form(
          key: formKey,
          child: EmailTextField(
            controller: controller,
            validator: (value) {
              if (value == null || value.isEmpty) return null; // Optional
              return ValidationService.validateEmail(value);
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                _updateSettings(ref, settings.copyWith(
                  backupEmail: controller.text.trim().isEmpty ? null : controller.text.trim(),
                ));
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showRecoveryPhoneDialog(BuildContext context, WidgetRef ref, AccountSettingsModel settings) {
    final controller = TextEditingController(text: settings.recoveryPhone ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recovery Phone'),
        content: Form(
          key: formKey,
          child: PhoneTextField(
            controller: controller,
            validator: ValidationService.validatePhoneNumber,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                _updateSettings(ref, settings.copyWith(
                  recoveryPhone: controller.text.trim().isEmpty ? null : controller.text.trim(),
                ));
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _performSecurityReview(BuildContext context, WidgetRef ref) async {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      try {
        await ref.read(accountControllerProvider.notifier).updateSecurityReview(userId);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Security review completed!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update security review: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  void _showTrustedDevices(BuildContext context, AccountSettingsModel settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Trusted Devices'),
        content: settings.trustedDevices.isEmpty
            ? const Text('No trusted devices found.')
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: settings.trustedDevices.map((device) {
                  return ListTile(
                    leading: const Icon(Icons.devices),
                    title: Text(device),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        // Remove device logic would go here
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Device management coming soon!'),
                          ),
                        );
                      },
                    ),
                  );
                }).toList(),
              ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDeactivationDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate Account'),
        content: const Text(
          'Are you sure you want to deactivate your account? This action can be reversed within 30 days.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final userId = ref.read(currentUserIdProvider);
              if (userId != null) {
                try {
                  await ref.read(accountControllerProvider.notifier).requestAccountDeactivation(userId);
                  
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Account deactivation requested'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to request deactivation: $e'),
                        backgroundColor: Theme.of(context).colorScheme.error,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _cancelDeactivation(BuildContext context, WidgetRef ref) async {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      try {
        await ref.read(accountControllerProvider.notifier).cancelAccountDeactivation(userId);
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Account deactivation cancelled'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to cancel deactivation: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  void _updateSettings(WidgetRef ref, AccountSettingsModel settings) {
    final userId = ref.read(currentUserIdProvider);
    if (userId != null) {
      ref.read(accountControllerProvider.notifier).updateAccountSettings(
        userId: userId,
        settings: settings,
      );
    }
  }
}
