import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/enums/user_role.dart';
import '../../providers/demo_auth_provider.dart';

/// Simple route guard for demo purposes
class RouteGuard {
  // Simplified route guard - not used in current implementation
  // The ProtectedRoute widget handles authentication directly
}

/// Widget that wraps protected routes with authentication checks
class ProtectedRoute extends ConsumerWidget {
  final Widget child;
  final UserRole? requiredRole;
  final bool requireAuth;

  const ProtectedRoute({
    super.key,
    required this.child,
    this.requiredRole,
    this.requireAuth = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Simple authentication check without complex route guarding
    final demoUser = ref.watch(demoUserProvider);
    final isAuthenticated = demoUser != null;

    debugPrint('🔍 Simple route check:');
    debugPrint('  - Demo user: ${demoUser?.email}');
    debugPrint('  - Is authenticated: $isAuthenticated');
    debugPrint('  - Required role: $requiredRole');

    // If authentication is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      debugPrint('🚫 Access denied - redirecting to login');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          context.go('/login');
        }
      });
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If specific role is required, check it
    if (requiredRole != null && demoUser?.role != requiredRole) {
      debugPrint('🚫 Access denied - wrong role');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          context.go('/login');
        }
      });
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    debugPrint('✅ Access granted');
    // User has permission, show the protected content
    return child;
  }
}

/// Unauthorized access screen
class UnauthorizedScreen extends StatelessWidget {
  final String? message;

  const UnauthorizedScreen({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.lock_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Access Denied',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message ?? 'You do not have permission to access this page.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/login'),
              child: const Text('Go to Login'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Loading screen for route transitions
class RouteLoadingScreen extends StatelessWidget {
  const RouteLoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      ),
    );
  }
}
